image: gitlab.metaleap.com:5050/library/node-maven/node:22.14.0-alpine-git
stages:
  - deploy
variables:
  KUBECONFIG: '/root/.m2/test.config'
  NODE_OPTIONS: '--openssl-legacy-provider'
  TZ: 'Asia/Shanghai' # 设置时区为北京时间

deploy-test:
  stage: deploy
  script:
    - node -v
    - yarn
    - export NODE_OPTIONS=--max-old-space-size=8192
    - yarn build:test
  only:
    - test
  tags:
    - k8s
  when: on_success
deploy-live:
  variables:
    KUBECONFIG: '/root/.m2/live.config'
  stage: deploy
  script:
    - yarn
    - export NODE_OPTIONS=--max-old-space-size=8192
    - yarn build:live
  only:
    - /^mb-users-draw-screen_.*$/
  except:
    - branchs
  tags:
    - k8s
  when: manual
