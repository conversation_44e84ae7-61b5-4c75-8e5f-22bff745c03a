const { execSync } = require('child_process')
const { Upload } = require('@msb-next/webpack-plugins')
const request = require('request')
const qs = require('qs')
const pkg = require('./package.json')
const buildConfig = require('./electron-builder.config')
const builder = require('electron-builder')
const colors = require('colors')
const { rmSync } = require('fs')
const path = require('path')
const Platform = builder.Platform

const env = process.argv[2] || 'test'
const action = process.argv[3] || 'pack'
const platform = process.platform === 'darwin' ? 'darwin' : 'win'
const cdnUrl = 'https://smallimg.meishubao.com'
const base = `release/${env}/${pkg.version}`
const prefix = `${pkg.name}/${env}/${pkg.version}/${platform === 'darwin' ? 'mac' : 'win'}`
let downloadUrl = ''
const electronConf = buildConfig(action, env, base)

console.log(`*** build 模式:${action === 'pack' ? '产物模式' : '上传注册'}  环境: ${env}, 平台: ${platform}, 版本: ${pkg.version} ***`.green.bold)

// 检测是否存在未上传代码
const validate = () => {
  console.log('step1:'.blue, '检查是否存在未提交代码'.green.bold)
  if (execSync('git status -s', { encoding: 'utf-8' }) !== '') {
    console.log('step1:'.blue, '请先提交修改内容'.red.bold)
    // process.exit(0);
  }
}

// 编译
const build = () => {
  console.log('step2:'.blue, '编译'.green.bold)
  execSync(`yarn build:ele:${env}`, { stdio: 'inherit' })
}

// 打包
const pack = () => {
  console.log('step3:'.blue, '打包'.green.bold)
  rmSync(path.join(__dirname, base), { recursive: true, force: true }) // v14.14.0
  return new Promise((resolve, reject) => {
    builder
      .build({
        targets: platform === 'darwin' ? Platform.MAC.createTarget() : Platform.WINDOWS.createTarget(),
        config: electronConf
      })
      .then((result) => {
        resolve(result)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 上传
const upload = () => {
  console.log('step4:'.blue, '上传'.green.bold)
  const upload = new Upload({
    prefix,
    bucket: 'small-class',
    base,
    exclude: [/未来学苑.app/, /win-ia32-unpacked/],
    showProgress: true,
    overwrite: true
  })
  upload.on('uploaded', (res) => {
    const suffix = platform === 'darwin' ? 'dmg' : 'exe'
    const suffixRemotePath = new RegExp(`.*.(${suffix})$`)
    if (res.url.match(suffixRemotePath)) {
      downloadUrl = res.url
    }
  })
  return upload.start()
}

// 注册版本
const register = () => {
  console.log('step5:'.blue, '注册版本'.green.bold)
  return new Promise((resolve, reject) => {
    // 环境区分
    const registerMap = {
      test: 'https://tmpapi.meishubao.com',
      live: 'https://mpapi.meishubao.com'
    }
    const bundleId = platform === 'darwin' ? 'com.yiqimac.ArtVideoWB.Teacher' : 'com.msb.win32.1v1.teacher'
    const version = pkg.version
    let versionCode = 0
    const tr = (t) => t.split('.').map((v, i) => (Number(v) < 10 && i ? `0${Number(v)}` : Number(v)))
    if (version.includes('-')) {
      //  1.0.0-alpha.0 => 1 00 00 01 00
      //  1.0.0-beta.0  => 1 00 00 02 00
      //  1.0.0-rc.0    => 1 00 00 03 00
      //  1.0.0         => 1 00 00 04 00
      version.includes('-alpha') ? (versionCode = Number([...tr(version.replace('-alpha', '.01'))].join(''))) : ''
      version.includes('-beta') ? (versionCode = Number([...tr(version.replace('-beta', '.02'))].join(''))) : ''
      version.includes('-rc') ? (versionCode = Number([...tr(version.replace('-rc', '.03'))].join(''))) : ''
    } else {
      versionCode = Number([...tr(version), '04', '00'].join(''))
    }
    const params = {
      bundleId,
      // https://smallimg.meishubao.com/msb-teacher-website/test/3.9.0/mac/msb-teacher-website-${os}-${version}.${ext}
      // fileUrl: `${cdnUrl}/${prefix}/msb-teacher-website-${platform === 'darwin' ? 'mac' : 'win'}-${pkg.version}.${platform === 'darwin' ? 'dmg' : 'exe'}`,
      fileUrl: downloadUrl,
      versionCode,
      versionDesc: `v${pkg.version}`
    }
    console.log(params)
    const addUrl = `${registerMap[env]}/operation/version/addVersion?${qs.stringify(params)}`
    request.post(addUrl, (error, response, body) => {
      if (!error) {
        const { status, msg } = JSON.parse(body)
        if (status === 1) {
          reject(msg)
        } else {
          resolve()
        }
      } else {
        reject(error)
      }
    })
  })
}

// 钉钉消息
const dingTalk = () => {
  console.log('step6:'.blue, '钉钉推送消息'.green.bold)
  const dingTalkToken = 'https://oapi.dingtalk.com/robot/send?access_token=6089874a2163de5e2100bd16ea95997443597dba364d491ef3598bcbfdac696f'
  // okex
  // const dingTalkToken = 'https://oapi.dingtalk.com/robot/send?access_token=19c9f5a93d73d55ba1ef90ad4ce5c588cf9057223b70cacbfeb87bd9145746a3';
  if (!downloadUrl) {
    console.log('step6:'.blue, '下载路径不存在'.red.bold)
    process.exit(0)
  }
  const options = {
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    },
    json: {
      msgtype: 'markdown',
      markdown: {
        title: '构建通知',
        text: `
### 构建信息: ${electronConf.productName}
构建平台: **${platform === 'darwin' ? 'MacOS' : 'Windows'}**  
构建环境: **${env}**  
构建版本: **${pkg.version}**  
下载地址: [${downloadUrl}](${downloadUrl})
`
      }
    }
  }
  request.post(dingTalkToken, options, function (error, response, body) {})
}

;(async function main() {
  execSync(`node -e 'console.log(v8.getHeapStatistics().heap_size_limit/(1024*1024))'`, { stdio: 'inherit' })
  execSync(`set NODE_OPTIONS="--max-old-space-size=8192"`, { stdio: 'inherit' })
  // 打包流程，按需使用
  validate() // 1.检测是否存在未上
  await build() // 2.编译
  await pack() // 3.打包
  if (action === 'publish') {
    await upload() // 4.上传
    await register() // 5.注册版本（更新官网下载链接、更新下载链接）
    dingTalk() // 6.钉钉消息
  }
})()
