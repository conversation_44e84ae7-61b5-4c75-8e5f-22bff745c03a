// 调试书法笔刷参数的脚本
// 在浏览器控制台中运行此脚本来测试参数保存和恢复

console.log('=== 书法笔刷参数调试脚本 ===');

// 模拟测试函数
function testCalligraphyParams() {
    console.log('开始测试书法笔刷参数...');
    
    // 检查是否有 PenBrush 实例
    if (typeof window.klApp === 'undefined') {
        console.error('klApp 未找到，请确保应用已加载');
        return;
    }
    
    // 尝试获取当前笔刷
    try {
        const currentBrush = window.klApp.getCurrentBrush();
        console.log('当前笔刷:', currentBrush);
        
        if (currentBrush && currentBrush.constructor.name === 'PenBrush') {
            console.log('找到 PenBrush 实例');
            
            // 测试设置书法参数
            console.log('测试设置书法参数...');
            
            // 先设置为书法模式
            currentBrush.setAlpha(4);
            console.log('已设置为书法模式 (Alpha 4)');
            
            // 设置测试参数
            const testParams = {
                velocityInfluence: 0.8,
                pressureInfluence: 0.9,
                velocitySmoothing: 0.2
            };
            
            console.log('设置测试参数:', testParams);
            currentBrush.setCalligraphyVelocityInfluence(testParams.velocityInfluence);
            currentBrush.setCalligraphyPressureInfluence(testParams.pressureInfluence);
            currentBrush.setCalligraphyVelocitySmoothing(testParams.velocitySmoothing);
            
            // 检查参数是否正确设置
            if (currentBrush.calligraphyBrush) {
                const actualParams = currentBrush.calligraphyBrush.getCalligraphyParams();
                console.log('实际参数:', actualParams);
                
                const paramsMatch = 
                    Math.abs(actualParams.velocityInfluence - testParams.velocityInfluence) < 0.01 &&
                    Math.abs(actualParams.pressureInfluence - testParams.pressureInfluence) < 0.01 &&
                    Math.abs(actualParams.velocitySmoothing - testParams.velocitySmoothing) < 0.01;
                
                if (paramsMatch) {
                    console.log('✅ 参数设置成功');
                } else {
                    console.log('❌ 参数设置失败');
                }
            } else {
                console.log('❌ 书法笔刷未初始化');
            }
            
        } else {
            console.log('当前不是 PenBrush，请切换到笔刷工具');
        }
        
    } catch (error) {
        console.error('测试过程中出错:', error);
    }
}

// 模拟撤销测试
function testUndoCalligraphyParams() {
    console.log('=== 测试撤销后参数保持 ===');
    
    try {
        const currentBrush = window.klApp.getCurrentBrush();
        
        if (currentBrush && currentBrush.constructor.name === 'PenBrush') {
            // 记录当前参数
            const beforeParams = currentBrush.calligraphyParams;
            console.log('撤销前的缓存参数:', beforeParams);
            
            // 模拟撤销操作（重新创建笔刷实例）
            console.log('模拟撤销操作...');
            
            // 保存当前参数
            const savedParams = {...beforeParams};
            
            // 重新设置笔刷（模拟撤销重放）
            currentBrush.setAlpha(4); // 重新初始化书法笔刷
            
            // 检查参数是否保持
            const afterParams = currentBrush.calligraphyParams;
            console.log('撤销后的缓存参数:', afterParams);
            
            const paramsPreserved = 
                Math.abs(afterParams.velocityInfluence - savedParams.velocityInfluence) < 0.01 &&
                Math.abs(afterParams.pressureInfluence - savedParams.pressureInfluence) < 0.01 &&
                Math.abs(afterParams.velocitySmoothing - savedParams.velocitySmoothing) < 0.01;
            
            if (paramsPreserved) {
                console.log('✅ 撤销后参数保持正常');
            } else {
                console.log('❌ 撤销后参数丢失');
            }
            
            // 检查书法笔刷实例的参数
            if (currentBrush.calligraphyBrush) {
                const brushParams = currentBrush.calligraphyBrush.getCalligraphyParams();
                console.log('书法笔刷实例参数:', brushParams);
            }
            
        } else {
            console.log('请先切换到笔刷工具');
        }
        
    } catch (error) {
        console.error('撤销测试过程中出错:', error);
    }
}

// 导出测试函数到全局
window.testCalligraphyParams = testCalligraphyParams;
window.testUndoCalligraphyParams = testUndoCalligraphyParams;

console.log('调试脚本已加载。使用以下命令进行测试:');
console.log('testCalligraphyParams() - 测试参数设置');
console.log('testUndoCalligraphyParams() - 测试撤销后参数保持');
