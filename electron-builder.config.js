const { notarize } = require('electron-notarize')
const pkg = require('./package.json')
const colors = require('colors')

const notarizeMac = async (context) => {
  const appName = context.packager.appInfo.productFilename
  const { appOutDir } = context
  console.log('Apple:'.blue, '上传Apple公证中...'.green.bold)
  const appPath = `${appOutDir}/${appName}.app`
  return notarize({
    tool: 'notarytool',
    teamId: 'PRA2G4V9FC',
    appBundleId: 'com.yiqimac.ArtVideoWB.Teacher',
    appPath,
    appleId: '<EMAIL>',
    appleIdPassword: 'rplf-amwr-qwnx-gpls',
    ascProvider: 'PRA2G4V9FC'
  })
}

/**
 * API看这里:
 * https://www.electron.build/configuration/configuration
 */
const config = (action, env, output) => {
  let win = {
    icon: 'electron/resources/icon.ico',
    requestedExecutionLevel: 'requireAdministrator',
    target: [
      {
        target: 'nsis',
        arch: ['ia32']
      }
    ]
  }
  if (env === 'live') {
    win = {
      ...win,
      // fpx模式
      // timeStampServer: 'http://timestamp.digicert.com',
      // certificateFile: 'config/ca/msb-2023.pfx',
      // certificatePassword: 'Meishubao123.',

      // U盾
      verifyUpdateCodeSignature: false,
      signingHashAlgorithms: ['sha256'],
      signDlls: true,
      rfc3161TimeStampServer: 'http://timestamp.digicert.com',
      certificateFile: 'config/ca/msb-2023.pfx',
      // 每月会强制修改密码，密码就是 Meishubao123. 或 Meishubao123.. 密码千万不能输错，会锁令牌，每次修改密码就多一个点或者少一个点，输入密码次数较多，直接粘贴即可
      certificatePassword: 'Meishubao123..', // 本次修改时间：2025年03月02日
      certificateSubjectName: ''
    }
  }
  return {
    appId: 'com.yiqimac.ShenBiMaLiang.DrawScreen',
    productName: pkg.productName,
    artifactName: 'ml-ai-draw-screen${os}-${version}.${ext}',
    copyright: `Copyright © 2024 ${pkg.author}`,
    // 如果要读写文件或启用资源服务，不能启用asar
    asar: false,
    directories: {
      output,
      buildResources: 'electron/resources'
    },
    electronDownload: {
      mirror: 'https://npmmirror.com/mirrors/electron/'
    },
    files: ['dist'],
    publish: [
      {
        provider: 'generic',
        url: ''
      }
    ],
    afterSign: async (context) => {
      if (action === 'publish' && env === 'live' && context.electronPlatformName === 'darwin') {
        await notarizeMac(context)
        console.log('Apple:'.blue, '公证完成,请查收Apple Developer邮件确认'.green.bold)
      }
    },
    win,
    nsis: {
      oneClick: false,
      perMachine: true,
      allowToChangeInstallationDirectory: true,
      deleteAppDataOnUninstall: false
    },
    mac: {
      identity: 'Zhejiang Yiqi Education Technology co. LTD (PRA2G4V9FC)',
      category: 'public.app-category.developer-tools',
      icon: 'electron/resources/icon.icns',
      hardenedRuntime: true,
      gatekeeperAssess: false,
      entitlements: 'ca/entitlements.mac.plist',
      entitlementsInherit: 'ca/entitlements.mac.plist',
      darkModeSupport: true,
      extendInfo: {
        NSMicrophoneUsageDescription: '我们申请使用【麦克风】权限，用于上课及录制课程视频，以保障我们的服务质量。详见【设置】-【隐私政策】。您同意开通权限会被视为同意上述安排。',
        NSCameraUsageDescription: '我们申请使用【摄像头】权限，用于上课及录制课程视频，以保障我们的服务质量。详见【设置】-【隐私政策】。您同意开通权限会被视为同意上述安排。'
      },
      target: {
        arch: 'universal',
        target: 'default'
      }
    },
    dmg: {
      sign: false,
      title: `${pkg.productName}${pkg.version}`,
      contents: [
        {
          x: 130,
          y: 200,
          type: 'file'
        },
        {
          x: 410,
          y: 200,
          type: 'link',
          path: '/Applications'
        }
      ],
      background: 'electron/resources/background.png'
    },
    // 允许 x.x.x-alpha.1、x.x.x-rc.1 这种版本号生成的yml为alph、rc，不然为latest。如果需要做开发版与release版的隔离,隔离后无法相互升级,设置true
    detectUpdateChannel: false,
    releaseInfo: {
      // 是否强升 forced 强升 freedom 自由，与发版文案|分割
      releaseNotes: '1.修复已知问题<br/>2.优化用户体验|freedom'
    }
  }
}

module.exports = config
