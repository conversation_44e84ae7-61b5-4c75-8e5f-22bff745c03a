import { app, BrowserWindow, ipcMain } from 'electron'
import { download } from 'electron-dl'

/**
 * 注册下载处理器，处理渲染进程的下载请求
 */
export const registerDownloadHandler = () => {
  ipcMain.on('download-files', async (event, { urls, options }) => {
    // 获取发起请求的窗口
    const win = BrowserWindow.fromWebContents(event.sender)
    if (!win) {
      console.error('找不到下载请求的窗口')
      return
    }

    // 确保urls是数组
    const urlsArray = Array.isArray(urls) ? urls : [urls]

    // 处理每个URL的下载
    for (const url of urlsArray) {
      try {
        const item = await download(win, url, {
          directory: options.directory,
          showBadge: true,
          showProgressBar: options.showProgressBar !== false,
          openFolderWhenDone: options.openFolderWhenDone || false,
          saveAs: options.saveAs || false,
          onProgress: (progress) => {
            // 向渲染进程发送下载进度
            event.sender.send('download-progress', progress)
          },
          onCompleted: (downloadItem) => {
            // 向渲染进程发送下载完成事件
            event.sender.send('download-completed', downloadItem)
          }
        })

        console.log(`下载完成: ${item.getSavePath()}`)
      } catch (error) {
        console.error(`下载失败: ${url}`, error)
      }
    }
  })
}
