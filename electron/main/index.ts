import colors from 'colors'
import { zip } from 'compressing'
import { app, BrowserWindow, desktopCapturer, dialog, ipcMain, Menu, screen, shell, Tray } from 'electron'
import electronDl, { download } from 'electron-dl'
import log from 'electron-log'
import fastFolderSizeSync from 'fast-folder-size/sync'
import { fileCollectionFromPath } from 'filelist-utils'
import * as fs from 'fs'
import { emptyDir, pathExists } from 'fs-extra'
import { release } from 'os'
import { basename, join } from 'path'
import si from 'systeminformation'
import * as path from 'path'

import { registerDownloadHandler } from './download-handler'
import { Network } from './network'
import koa from './serve'
import { Updater } from './updater'

let port = 8989
log.transports.file.level = 'silly'
// 文件最大不超过 10M
log.transports.file.maxSize = 10485760
log.initialize({ preload: true })
const logConfig = {
  path: '',
  todayFileName: `${new Date().toLocaleDateString('sv-SE')}.log`
}
log.transports.file.resolvePathFn = (variables) => {
  logConfig.path = variables.libraryDefaultDir
  return join(variables.libraryDefaultDir, logConfig.todayFileName)
}
log.info('                            ')
log.info('--------- APPStart ---------')
log.info('                            ')
electronDl()
registerDownloadHandler()

// 解决部分win电脑无法启动问题,但是会降低安全性
if (process.platform !== 'darwin') {
  app.commandLine.appendSwitch('no-sandbox')
  app.commandLine.appendSwitch('disable-gpu')
  app.commandLine.appendSwitch('disable-software-rasterizer')
  app.commandLine.appendSwitch('disable-gpu-compositing')
  app.commandLine.appendSwitch('disable-gpu-rasterization')
  app.commandLine.appendSwitch('disable-gpu-sandbox')
  app.commandLine.appendSwitch('--no-sandbox')
  app.disableHardwareAcceleration()
} else {
  app.commandLine.appendSwitch('disable-features', 'IOSurfaceCapturer,DesktopCaptureMacV2')
}
// Disable GPU Acceleration for Windows 7
if (release().startsWith('6.1')) app.disableHardwareAcceleration()

// Set application name for Windows 10+ notifications
if (process.platform === 'win32') app.setAppUserModelId(app.getName())

if (!app.requestSingleInstanceLock()) {
  app.quit()
  process.exit(0)
}

process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'

export const ROOT_PATH = {
  // /dist
  dist: join(__dirname, '..'),
  // /dist or /public
  public: join(__dirname, app.isPackaged ? '..' : '../../public')
}

interface BrowserWindowType extends BrowserWindow {
  winName?: string
}

let wins: { [key: string]: BrowserWindowType } = {}
// Here, you can also use other preload
// const preload = join(__dirname, '../preload/index.js')
const preload = join(__dirname, '../preload/index.js')
const url = process.env.VITE_DEV_SERVER_URL
const isMac = process.platform === 'darwin'
const indexHtml = join(ROOT_PATH.dist, 'index.html')

// create window
async function createWindow(data: any): Promise<BrowserWindowType> {
  const { newWinName, path = '', screenId, ...other } = data

  // // 获取指定屏幕信息或默认使用主屏幕
  // let targetScreen = screen.getPrimaryDisplay()
  // if (screenId !== undefined) {
  //   const allScreens = screen.getAllDisplays()
  //   const foundScreen = allScreens.find(display => display.id === screenId)
  //   if (foundScreen) {
  //     targetScreen = foundScreen
  //   }
  // }

  const window: BrowserWindowType = new BrowserWindow({
    title: newWinName,
    icon: join(ROOT_PATH.public, 'favicon.png'),
    // 菜单在预发、线上已经移除，任然需要在window系统隐藏菜单条
    width: 760,
    height: 480,
    // 设置窗口在目标屏幕上的位置
    // x: targetScreen.workArea.x,
    // y: targetScreen.workArea.y,
    autoHideMenuBar: process.platform === 'win32',
    // visibleOnAllWorkspaces: true, // shi
    // center: true,
    // frame: true,
    webPreferences: {
      devTools: true,
      preload,
      additionalArguments: ['--custom-data', JSON.stringify({ winName: newWinName, isMac, platform: process.platform })],
      nodeIntegration: true,
      contextIsolation: true,
      enableRemoteModule: true,
      nodeIntegrationInWorker: true,
      nodeIntegrationInSubFrames: true,
      sandbox: false,
      // 以下设置对屏幕共享很重要
      backgroundThrottling: false, // 防止在后台时节流
      // 启用webview标签
      webviewTag: true,
      // 同源策略
      webSecurity: false, // 注意：在生产环境中应谨慎关闭
      // 自动播放策略设置为<无需用户交互>
      autoplayPolicy: 'no-user-gesture-required'
    },
    ...other
  })

  window.winName = newWinName
  // 存放窗口容器中
  wins[newWinName] = window

  // 注册窗口事件
  emitEvents(window)

  if (app.isPackaged) {
    if (path.includes('http')) {
      console.log('pack loadURL', path)
      window.loadURL(path)
    } else {
      console.log('pack loadURL', indexHtml, path)
      window.loadFile(indexHtml, { hash: path })
    }
  } else {
    window.webContents.openDevTools()
    if (path.includes('http')) {
      console.log('loadURL', path)
      window.loadURL(path)
    } else {
      console.log('loadURL', url + path)
      window.loadURL(url + path)
    }
  }

  window.webContents.on('will-attach-webview', (e, webPreferences) => {
    console.log('🚀 ~ window.webContents.on ~ webPreferences:', webPreferences)
    webPreferences.sandbox = false
    webPreferences.nodeIntegration = true
    webPreferences.nodeIntegrationInWorker = true
    webPreferences.nodeIntegrationInSubFrames = true
    webPreferences.contextIsolation = true
    webPreferences.preload = join(__dirname, '../preload/webview.js')
  })

  // 窗口加载完成
  window.webContents.on('did-finish-load', () => {
    // 打开一个新的子窗口，给自己的页面发名称
    setTimeout(() => {
      send({
        sendWinName: newWinName,
        toWinName: newWinName,
        method: 'create-win',
        data: { winName: newWinName, isMac }
      })
    }, 100)
    // 给所有页面发送新窗口打开的信息
    for (const key in wins) {
      if (key !== newWinName) {
        send({
          sendWinName: newWinName,
          toWinName: key,
          method: 'loaded',
          data: newWinName
        })
      }
    }
  })

  // Make all links open with the browser, not with the application
  window.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) shell.openExternal(url)
    return { action: 'deny' }
  })

  return window
}

// browser-window event
function emitEvents(win: BrowserWindowType) {
  const events: any[] = [
    'close',
    'closed',
    'query-session-end',
    'session-end',
    'blur',
    'focus',
    'show',
    'hide',
    'maximize',
    'unmaximize',
    'minimize',
    'restore',
    'will-resize',
    'resize',
    'resized',
    'will-move',
    'move',
    'moved',
    'enter-full-screen',
    'leave-full-screen',
    'always-on-top-changed',
    'app-command',
    'swipe',
    'rotate-gesture',
    'sheet-begin',
    'sheet-end',
    'new-window-for-tab',
    'system-context-menu'
  ]
  // 自动订阅所有事件
  events.forEach((eventName) => {
    win.on(eventName, (event, ...args) => {
      // 关闭窗口时，删除wins对象中的窗口
      if (eventName === 'closed') {
        delete wins[win.winName as string]
      }
      // 子窗口关闭，给所有窗口发信息
      for (const key in wins) {
        send({
          sendWinName: win.winName,
          toWinName: key,
          method: eventName,
          data: { winName: win.winName, event: eventName, ...args }
        })
      }
    })
  })
}

// ---------- app runner ----------
let tray = null
// app准备好了，创建主窗口
app.whenReady().then(async () => {
  // 添加控制台插件
  // const reactDevToolsPath = join(homedir(), '/Library/Application Support/Google/Chrome/Default/Extensions/jhfmmdhbinleghabnblahfjfalfgidik/3.2.1_0')
  // await session.defaultSession.loadExtension(reactDevToolsPath)

  const { width, height } = screen && screen.getPrimaryDisplay() && screen.getPrimaryDisplay().workAreaSize
  // const h = Math.min(height, Number((width * 0.5625).toFixed(0)))
  log.info('workAreaSize', width, height)
  const win = await createWindow({
    newWinName: 'mainWin',
    width,
    height: height,
    resizable: false,
    frame: false
  })

  const submenu1: Electron.MenuItemConstructorOptions[] = [
    { label: '关于', role: 'about' },
    {
      label: '官网',
      click: async () => {
        shell.openExternal('https://vip.meishubao.com')
      }
    },
    { type: 'separator' }
  ]
  const submenu2: Electron.MenuItemConstructorOptions[] = [
    {
      label: 'DevTools',
      accelerator: 'CmdOrCtrl+Alt+L',
      click: async () => {
        win.webContents.toggleDevTools()
      }
    },
    {
      label: 'OsInfo',
      accelerator: 'CmdOrCtrl+Alt+I',
      click: async () => {
        const osInfo = await si.osInfo()
        const cpu = await si.cpu()
        const mem = await si.mem()
        const used = mem.total - mem.available
        const displays = screen.getAllDisplays()
        let message = `系统:
${osInfo.codename || osInfo.platform} 版本:${osInfo.release} 架构:${osInfo.arch}

CPU:
${cpu.brand} 核心:${cpu.physicalCores} 速度:${cpu.speed}GHz

内存:
${(mem.total / 1073741824).toFixed(2)}G 已用:${(used / 1073741824).toFixed(2)}G 空闲:${(mem.available / 1073741824).toFixed(2)}G
`
        displays.forEach((v, i) => {
          message += `
屏幕${i + 1}:
宽:${v.workArea.width} 高:${v.workArea.height} x:${v.workArea.x} y:${v.workArea.y}
`
        })
        dialog.showMessageBox({
          type: 'info',
          title: '设备信息',
          message,
          buttons: ['好的']
        })
      }
    },
    {
      label: 'Logs',
      accelerator: 'CmdOrCtrl+Alt+R',
      click: async () => {
        shell.openPath(logConfig.path)
      }
    },
    { type: 'separator' },
    {
      label: '重启',
      click: async () => {
        app.relaunch()
        app.exit(0)
      }
    },
    { label: '退出', role: 'quit' }
  ]

  // 移除顶部最上角的菜单栏
  Menu.setApplicationMenu(
    Menu.buildFromTemplate([
      {
        label: app.name,
        submenu: [
          ...submenu1,
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectAll' },
          { type: 'separator' },
          ...submenu2
        ]
      }
    ])
  )
  const menu1 = Menu.buildFromTemplate([...submenu1, ...submenu2])
  tray = new Tray(join(ROOT_PATH.public, process.platform === 'win32' ? 'bar.png' : 'mac-bar.png'))
  tray.setToolTip(app.name)
  tray.setContextMenu(menu1)
  port = await Network.tryUsePort(port)
  koa.listen(port)
  console.log('port:', port)
  win.on('closed', () => {
    app.quit(); // 退出应用
  });
})

app.on('before-quit', () => {
  // 强制退出应用（不执行任何清理操作）
  app.exit();
});

// 当所有的窗口都被关闭时关闭应用
app.on('window-all-closed', () => {
  wins = {}
  app.quit()
})

// 全局异常捕获
process.on('uncaughtException', (err, _origin) => {
  // 收集日志
  log.error('全局异常捕获 err = ', err)
  // 显示异常提示信息或者重新加载应用
})

// 当所有的窗口都被关闭时关闭应用
app.on('will-quit', () => {
  log.info('                            ')
  log.info('--------- APP Quit ---------')
  log.info('                            ')
})

// 第二个实例被执行，聚焦主窗口
app.on('second-instance', () => {
  const mainWin = wins.mainWin
  if (mainWin) {
    // Focus on the main window if the user tried to open another
    if (mainWin.isMinimized()) mainWin.restore()
    mainWin.focus()
  }
})

// app激活时，判断是否存在主窗口，没有创建
app.on('activate', () => {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length) {
    allWindows[0].focus()
  } else {
    createWindow({
      newWinName: 'mainWin',
      width: 1050,
      height: 650
    })
  }
})

interface BodyType {
  /**
   * 发送者 默认sessionStorage.winName
   */
  sendWinName?: string
  /**
   * 方法
   */
  method?: string
  /**
   * 接收者 -1=全部 空=不通知 字符串=指定win
   */
  toWinName?: string | -1
  /**
   * 数据
   */
  data?: any
}

interface DlFile extends electronDl.File {
  fileName: string
}

/**
 * 发送消息
 * @param body 接收者
 */
const send = (body: BodyType) => {
  const { toWinName, method: channel } = body
  log.info(colors.blue.bold(`send ${channel}`), JSON.stringify(body))
  if (toWinName && toWinName !== -1 && wins[toWinName] && channel) {
    wins[toWinName].webContents.send(channel, {
      data: {},
      ...body
    })
  }
}

// ---------- autoUpdater ----------
const updater = new Updater(app)
updater.on('send', send)

// ---------- ipcMain on ----------

// 自定义事件
const customMethod = {
  // 互发消息
  msg: (body: BodyType) => {
    const { data } = body
    return data
  },
  // 获取窗口信息
  'win-info': () => {
    return { isMac, logConfig, port }
  },
  // 获取屏幕信息
  'screen-info': () => {
    return screen.getAllDisplays()
  },
  // 获取所有窗口e
  'all-wins': () => {
    const wins: BrowserWindowType[] = BrowserWindow.getAllWindows()
    return wins.map((v) => v.winName)
  },
  // 创建窗口
  'create-win': async (body: BodyType) => {
    await createWindow(body.data)
  },
  // 下载
  download: async (body: BodyType) => {
    const { sendWinName, data = {} } = body
    const { directory = '/zip', url } = data
    if (sendWinName && !wins[sendWinName]) {
      return false
    }
    try {
      if (sendWinName) {
        const res = []
        for (let i = 0; i < url.length; i++) {
          const exists = await pathExists(join(app.getAppPath(), `/dist/public${directory}`, basename(url[i])))
          // console.log('exists', join(app.getAppPath(), `/dist/public${directory}`, basename(url[i])))
          if (!exists) {
            const dat = await new Promise((resolve, reject) => {
              download(wins[sendWinName], url[i], {
                overwrite: true,
                directory: join(app.getAppPath(), `/dist/public${directory}`),
                onProgress: (res) => {
                  send({
                    sendWinName,
                    toWinName: sendWinName,
                    method: 'download-progress',
                    data: { ...res, name: basename(url[i]) }
                  })
                },
                onCompleted: (file: electronDl.File) => {
                  // electronDl.File 中已经存在fileName，但是ts不全，处理办法
                  const dlFile: DlFile = { fileName: '', ...file }
                  const data = {
                    ...file,
                    localUrl: `http://localhost:${port}${directory}/${dlFile.fileName}`
                  }
                  log.info('download-completed', directory, url[i])
                  resolve(data)
                }
              })
            })
            res.push(dat)
          }
        }
        // send({
        //   sendWinName,
        //   toWinName: sendWinName,
        //   method: 'download-completed',
        //   data: res
        // })
        return res
      }
    } catch (err) {
      return err
    }
  },
  // 解压
  unpack: async (body: BodyType) => {
    const { sendWinName, data = {} } = body
    const { directory = '/unzip', path = [] } = data

    const unzip = async (source: { name: string; file: string; dir: string }[], index: number) => {
      // console.log('source', source, index)
      const array = []
      for (let i = 0; i < source.length; i++) {
        array.push(
          new Promise((resolve) => {
            // 这里有个巨坑,课件第一级压缩是不带文件夹的,子级压缩都存在一个文件夹,所以先判断是否是第一级做兼容处理
            zip.uncompress(source[i].file, index === 0 ? `${source[i].dir}/${source[i].name}` : source[i].dir).then(() => {
              resolve(true)
            })
          })
        )
      }
      await Promise.all(array)
      const child = []
      for (let i = 0; i < source.length; i++) {
        const dirList = fs.readdirSync(`${source[i].dir}/${source[i].name}`)
        const dirZip = dirList.filter((item) => item.includes('.zip'))
        for (let j = 0; j < dirZip.length; j++) {
          child.push({ name: basename(dirZip[j], '.zip'), file: join(source[i].dir, source[i].name, dirZip[j]), dir: `${source[i].dir}/${source[i].name}` })
        }
      }
      if (child.length !== 0) {
        await unzip(child, index + 1)
      }
    }

    await unzip(
      path.map((v: string) => ({
        // v = /zip/xxx/xxx.zip
        name: basename(v, '.zip'),
        file: join(app.getAppPath(), `/dist/public${v}`),
        dir: join(app.getAppPath(), `/dist/public${directory}`)
      })),
      0
    )

    const res = []
    for (let i = 0; i < path.length; i++) {
      const fileCollection = await fileCollectionFromPath(join(app.getAppPath(), `/dist/public${directory}`, basename(path[i], '.zip')))
      const dat = {
        folder: `http://localhost:${port}${directory}/`,
        files: fileCollection.files.map((v) => ({
          name: v.name,
          size: v.size,
          relativePath: v.relativePath
        }))
      }
      res.push(dat)
    }
    // send({
    //   sendWinName,
    //   toWinName: sendWinName,
    //   method: 'unpack-completed',
    //   data: res
    // })
    return res
  },
  // updater - 设置更新地址
  setFeedURL: (body: BodyType) => {
    const { sendWinName, data = {} } = body
    const { url } = data
    updater.setFeedURL(url)
  },
  // updater - 启动更新检测
  'check-update': Updater.checkForUpdates,
  // updater - 开始下载
  'comfirm-update': Updater.downloadUpdate,
  // updater - 安装重启
  'install-update': Updater.quitAndInstall,
  openDevTools: (body: BodyType) => {
    const { sendWinName, data } = body
    const { mode } = data
    if (!sendWinName || !wins[sendWinName]) {
      return false
    }
    wins[sendWinName].webContents.openDevTools({ mode })
  },
  // 打开文件夹
  openPath: (body: BodyType) => {
    const { sendWinName, data } = body
    const { path } = data
    shell.openPath(path)
  },
  // 打开文件夹
  cocosPreloadPath: (body: BodyType) => {
    return join(__dirname, '../preload/index.js')
  },
  // https://tsejx.github.io/cross-platform-guidebook/electron/other/compose
  // https://systeminformation.io/network.html
  // 系统信息
  osInfo: (body: BodyType) => {
    const { sendWinName, data } = body
    return si.osInfo()
  },
  // CPU信息
  'si-cpu': (body: BodyType) => {
    const { sendWinName, data } = body
    return si.cpu()
  },
  // CPU速度
  cpuCurrentSpeed: (body: BodyType) => {
    const { sendWinName, data } = body
    return si.cpuCurrentSpeed()
  },
  // 内存信息与使用率
  'si-mem': (body: BodyType) => {
    const { sendWinName, data } = body
    return si.mem()
  },
  // 系统信息
  systeminformation: (body: BodyType) => {
    const { sendWinName, data } = body
    return si.get(data.valuesObject)
  },
  // public文件夹大小
  'cache-size': (body: BodyType) => {
    try {
      const bytes = fastFolderSizeSync(join(app.getAppPath(), `/dist/public`)) || 0
      const sizeM = Math.ceil(bytes / 1024 / 1024)
      return sizeM.toFixed(1)
    } catch (error) {
      return 0
    }
  },
  // 清空public文件夹
  'cache-clear': async (body: BodyType) => {
    try {
      await emptyDir(join(app.getAppPath(), `/dist/public`))
      return true
    } catch (error) {
      return true
    }
  }
}

// 消息处理中继
const transfer = async (channel: string, args: any[]) => {
  const [body, ...parameter] = args
  log.info(colors.blue.green(`on ${channel}`), JSON.stringify(body), ...parameter)
  const { sendWinName, method, toWinName } = body as BodyType
  let res
  try {
    switch (channel) {
      case 'app':
        // @ts-ignore
        res = await app[method](...parameter)
        break
      case 'win':
        // @ts-ignore
        if (wins[sendWinName]) {
          // @ts-ignore
          res = await wins[sendWinName][method](...parameter)
        }
        break
      case 'screen':
        // @ts-ignore
        res = await screen[method](...parameter)
        break
      case 'custom':
        // @ts-ignore
        if (customMethod[method]) {
          // @ts-ignore
          res = await customMethod[method](body)
        }
        break
    }
    // notice
    const reslut = { sendWinName, method, toWinName, data: res }
    if (toWinName === -1) {
      for (const key in wins) {
        if (key !== sendWinName) {
          send({ ...reslut, toWinName: key })
        }
      }
    } else if (toWinName) {
      if (wins[toWinName]) {
        send(reslut)
      }
    }
    return reslut
  } catch (error) {
    log.error(colors.red.bold(channel), error)
  }
}

// 通过ipc执行app的通用带返回值方法，ipcRenderer.invoke调用
ipcMain.handle('app', (event, ...args) => {
  return transfer('app', args)
})

// 通过ipc执行窗口的通用带返回值方法，ipcRenderer.invoke调用
ipcMain.handle('win', (event, ...args) => {
  console.log('🚀 ~ ipcMain.handle ~ args:', args)
  return transfer('win', args)
})

// 通过ipc执行窗口的通用带返回值方法，ipcRenderer.invoke调用
ipcMain.handle('screen', (event, ...args) => {
  return transfer('screen', args)
})

// 通过ipc执行窗口的通用带返回值方法，ipcRenderer.invoke调用
ipcMain.handle('custom', (event, ...args) => {
  return transfer('custom', args)
})

// 声网共享屏幕使用
ipcMain.handle('DESKTOP_CAPTURER_GET_SOURCES', async (event, opts) => {
  try {
    const sources = await desktopCapturer.getSources(opts)
    const displays = screen?.getAllDisplays()
    const newSources = sources.map((item) => {
      const { bounds, workArea } = displays.find((dis) => dis.id === +item.display_id) ?? {}
      return { ...item, bounds, workArea }
    })
    return newSources
  } catch (error) {
    console.error('Error getting desktop capturer sources:', error)
    return {}
  }
})

ipcMain.handle('SET_LOCAL_JSON_FILE', async (event, opts) => {
  try {
    const dataPath = path.join(app.getPath('userData'), `${opts.name}.json`);
    await fs.promises.writeFile(dataPath, JSON.stringify(opts.data));
    console.log(`数据已保存到 ${dataPath}`);
    return { success: true, path: dataPath };
  } catch (err) {
    console.error('保存数据失败:', err);
    return { success: false, error: err.message };
  }
});

ipcMain.handle('GET_LOCAL_JSON_FILE', async (event, opts) => {
  try {
    const dataPath = path.join(app.getPath('userData'), `${opts.name}.json`);
    if (!await pathExists(dataPath)) {
      return { success: false, error: '文件不存在', path: dataPath };
    }
    const data = await fs.promises.readFile(dataPath, 'utf8');
    const parsedData = JSON.parse(data);
    console.log(`读取到的数据 ${opts.name}.json:`, parsedData);
    return { success: true, ...parsedData };
  } catch (err) {
    console.error('读取数据失败:', err);
    return { success: false, error: opts };
  }
});
