import net from 'net'

export class Network {
  // 端口能不能用
  public static portInUse(port: number): Promise<unknown> {
    return new Promise((resolve) => {
      const server = net.createServer().listen(port)
      server.on('listening', () => {
        server.close()
        resolve(port)
      })
      server.on('error', (err: any) => {
        if (err.code == 'EADDRINUSE') {
          resolve(err)
        }
      })
    })
  }
  // 整个能用的端口
  public static async tryUsePort(port = 8989): Promise<number> {
    const res = await Network.portInUse(port)
    if (res instanceof Error) {
      console.log(`端口：${port}被占用\n`)
      return await Network.tryUsePort(port + 1)
    } else {
      return port
    }
  }
}
