import Router from '@koa/router'
import Koa from 'koa'
import { koaBody } from 'koa-body'
import koaStatic from 'koa-static'
import { resolve } from 'path'

const app = new Koa()
const router = new Router()

app.use(koaBody())
// 资源文件
app.use(koaStatic(resolve(__dirname, '../public')))
// localhost:8989/a
router.post('/a', async (ctx: any, next: any) => {
  // ctx.router available
  console.log(ctx.request.body)
  ctx.body = ctx.request.body
})

app.use(router.routes()).use(router.allowedMethods())

export default app
