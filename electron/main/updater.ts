import log from 'electron-log'
import { autoUpdater } from 'electron-updater'
import events from 'events'

// 1.启动更新检测      client -> check-update
// 2.检查更新中        server -> update-checking
// 3.有更新           server -> update-available
// 3.1没有更新        server -> update-not-available
// 4.开始下载         client -> comfirm-update
// 5.下载中           server -> download-progress
// 6.下载完成         server -> update-downloaded
// 7.安装重启         server -> install-update
export class Updater extends events {
  private app: Electron.App
  constructor(app: Electron.App) {
    super()
    this.app = app

    // 更新异常
    autoUpdater.on('error', (error) => {
      log.error(error)
      this.printUpdaterMessage('update-error', error)
    })

    // 2.检查更新中
    autoUpdater.on('checking-for-update', () => {
      this.printUpdaterMessage('update-checking')
    })

    // 3.有更新
    autoUpdater.on('update-available', (info) => {
      const releaseNotes = (info.releaseNotes as string) || ''
      const fixReleaseNotes = releaseNotes.split('|')
      let isFreedom = false
      if (fixReleaseNotes[1] && fixReleaseNotes[1] === 'freedom') {
        isFreedom = true
      }
      const file = info.files[0]
      const size = file.size || 0
      this.printUpdaterMessage('update-available', {
        ...info,
        // 是否强升
        forced: !isFreedom,
        releaseNotes: `${fixReleaseNotes[0]}`,
        size
      })
    })

    // 3.1没有更新
    autoUpdater.on('update-not-available', (info) => {
      this.printUpdaterMessage('update-not-available', info)
    })

    // 5.下载进度，包含进度百分比、下载速度、已下载字节、总字节等
    // ps: 调试时，想重复更新，会因为缓存导致该事件不执行，下载直接完成，可找到C:\Users\<USER>\AppData\Local\xxx-updater\pending下的缓存文件将其删除（这是我本地的路径）
    autoUpdater.on('download-progress', (progressObj) => {
      this.printUpdaterMessage('update-progress', progressObj)
    })

    // 6.下载完成，告诉渲染进程，是否立即执行更新安装操作
    autoUpdater.on('update-downloaded', () => {
      this.printUpdaterMessage('update-downloaded')
    })
  }

  // 将日志在渲染进程里面打印出来
  private printUpdaterMessage(channel: string, arg?: any) {
    const message: { [key: string]: string } = {
      'update-error': '更新出错',
      'update-checking': '正在检查更新',
      'update-available': '检测到新版本',
      'update-progress': '下载中',
      'update-not-available': '无新版本',
      'update-downloaded': '下载完成'
    }
    this.emit('send', {
      sendWinName: 'mainWin',
      toWinName: 'mainWin',
      method: channel,
      data: {
        message: message[channel] || '',
        ...arg
      }
    })
  }

  public setFeedURL(url?: string) {
    const isMac = process.platform === 'darwin'
    // 配置提供更新的程序，及build中配置的url
    const feedURL = url || `https://smallimg.meishubao.com/${import.meta.env.VITE_APP_NAME}/${import.meta.env.VITE_APP_ENV}/${isMac ? 'mac' : 'win'}`
    if (!this.app.isPackaged) {
      // feedURL = `http://localhost:8989`
    }
    log.info('feedURL:', feedURL)
    autoUpdater.setFeedURL(feedURL)
    // 是否自动更新，如果为true，当可以更新时(update-available)自动执行更新下载。
    autoUpdater.autoDownload = false

    if (!this.app.isPackaged) {
      autoUpdater.forceDevUpdateConfig = true
      // 开发时可以走这个逻辑，但是我们起了本地服务，也可以走setFeedURL的逻辑
      // autoUpdater.updateConfigPath = join(__dirname, '../../../dev-app-update.yml')
    }
  }

  static checkForUpdates() {
    autoUpdater.checkForUpdates()
  }

  static downloadUpdate() {
    autoUpdater.downloadUpdate()
  }

  static quitAndInstall() {
    autoUpdater.quitAndInstall()
  }
}
