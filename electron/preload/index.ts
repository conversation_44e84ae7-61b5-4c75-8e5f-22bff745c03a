import { contextBridge, ipcRenderer, nativeImage } from 'electron'
// 移除fs和path引用，文件操作全部在主进程中处理
const customData = JSON.parse(process.argv[process.argv.indexOf('--custom-data') + 1])

const windowInfo: {
  winName: string
  isMac: boolean
} | null = {
  ...customData
}

// 暴露 ipcRenderer 的 invoke 和 on 方法给前端
contextBridge.exposeInMainWorld('electronAPI', {
  invoke: (channel: string, ...args: any) => {
    return ipcRenderer.invoke(channel, ...args)
  },
  on: (channel: string, listener: (event: Electron.IpcRendererEvent, ...args: any[]) => void) => {
    return ipcRenderer.on(channel, listener)
    // return () => {
    //   ipcRenderer.removeListener(channel, subscription)
    // }
  },
  nativeImage,
  send: (channel: string, ...args: any) => {
    ipcRenderer.send(channel, ...args)
  },
  removeListener: (channel: string, listener: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, listener)
  },
  windowInfo
})

let gameRegisterHandler: any = {}

const WebViewJavascriptBridge = {
  // getWebContents: () => {
  //   return remote.getCurrentWebContents();
  // },
  callHandler: (channel: string, cb: any) => {
    console.log('---------  callHandler  ---------')
    console.log(channel, cb)

    if (cb instanceof Function) {
      ipcRenderer.sendTo(1, 'cocos', channel)
      ipcRenderer.on(channel, (event, ...args) => {
        console.log('run callback')
        cb(...args)
      })
    } else {
      ipcRenderer.sendTo(1, 'cocos', channel, cb)
    }
  },
  registerHandler: (channel: string, cb: any) => {
    console.log('---------- registerHandler --------')
    console.log(channel, cb)
    // todo 拦截重复多次注册
    if (gameRegisterHandler[channel]) {
      return
    }
    gameRegisterHandler[channel] = channel
    ipcRenderer.on(channel, (event, ...args) => cb(...args))
  },

  clearRegister: () => {
    gameRegisterHandler = {}
  }
}

contextBridge.exposeInMainWorld('WebViewJavascriptBridge', WebViewJavascriptBridge)

console.log('preload 加载完成，windowInfo =', windowInfo)
console.log('preload 加载完成，window.location', window.location)
