const { contextBridge, ipcRenderer } = require('electron')

// 暴露 API 给 webview 内容
contextBridge.exposeInMainWorld('electronAPI', {
  // 发送消息到主进程
  sendMessage: ({ action, params }) => {
    ipcRenderer.sendToHost(action, params)
  },
  // 从主进程接收消息
  onMessage: (channel, callback) => {
    // 创建一个包装回调，移除第一个事件参数
    const subscription = (_, ...args) => callback(...args)
    // 订阅事件
    ipcRenderer.on(channel, subscription)
    // 返回一个取消订阅函数
    return () => ipcRenderer.removeListener(channel, subscription)
  }
})

// 监听页面加载完成事件
window.addEventListener('DOMContentLoaded', () => {
  // 向主进程发送页面加载完成的消息
  ipcRenderer.send('webview-page-loaded', {
    url: window.location.href,
    title: document.title
  })

  // 可以在这里添加其他页面加载完成后的操作
})

// 清理事件监听
window.addEventListener('beforeunload', () => {
  // 可以在这里添加清理代码
})
