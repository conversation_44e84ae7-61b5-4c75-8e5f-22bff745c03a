<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta content="width=device-width, initial-scale=1.0,user-scalable=no,maximum-scale=1.0" name="viewport" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no,address=no" />
    <meta name="apple-mobile-web-app-status-bar-style" content="white" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>中国美术学院AI中心</title>
    <script>
      !(function (n) {
        function f() {
          var e = n.document.documentElement,
            w = e.getBoundingClientRect().width,
            x = (16 * w) / 1024
          e.style.fontSize = x + 'px'
        }
        n.addEventListener('resize', function () {
          f()
        }),
          f()
      })(window)
    </script>
    <meta name="Keywords" content="中国美术学院少儿数字艺术" />
    <meta name="description" content="" />
    <link rel="icon" href="/logo.png" />
    <meta property="og:url" content="" />
    <meta property="og:title" content="" />
    <meta property="og:description" content="" />
    <style>
      #loading {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        /* background-image: url('./src/assets/images/newBoard/playBg.jpg'); */
        background-size: cover;
        background-color: #C6D0ED;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.35s;
      }
      .loader-line-3 {
        margin: 0 auto;
        width: 60px;
        height: 30px;
        text-align: center;
        position: relative;
        display: flex;
      }
      .loader-line-3 > div {
        height: 100%;
        width: 8px;
        display: inline-block;
        margin-left: 2px;
        animation: anm-LL-3-move 0.8s infinite ease-in-out;
      }
      .loader-line-3 .bar1 {
        background-color: #754fa0;
      }
      .loader-line-3 .bar2 {
        background-color: #09b7bf;
        animation-delay: -0.7s;
      }
      .loader-line-3 .bar3 {
        background-color: #90d36b;
        animation-delay: -0.6s;
      }
      .loader-line-3 .bar4 {
        background-color: #f2d40d;
        animation-delay: -0.5s;
      }
      .loader-line-3 .bar5 {
        background-color: #fcb12b;
        animation-delay: -0.4s;
      }
      .loader-line-3 .bar6 {
        background-color: #ed1b72;
        animation-delay: -0.3s;
      }
      @keyframes anm-LL-3-move {
        0%,
        40%,
        100% {
          transform: scaleY(0.05);
        }
        20% {
          transform: scaleY(1);
        }
      }
    </style>
    <script>
      var _hmt = _hmt || []
      ;(function () {
        var hm = document.createElement('script')
        hm.src = window.location.host === 'aigc.metaleap.com' ? 'https://hm.baidu.com/hm.js?7590b4f5efe6cff25de6cf2fe89348be' : 'https://hm.baidu.com/hm.js?7445917f89267977f516e5cd8403671d'
        var s = document.getElementsByTagName('script')[0]
        s.parentNode.insertBefore(hm, s)
      })()
    </script>
  </head>
  <body>
    <div id="root">
      <div id="loading">
        <div class="loader-line-3">
          <div class="bar1"></div>
          <div class="bar2"></div>
          <div class="bar3"></div>
          <div class="bar4"></div>
          <div class="bar5"></div>
          <div class="bar6"></div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
