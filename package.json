{"name": "ml-ai-draw-screen", "version": "1.0.0", "private": true, "description": "实时绘画分屏", "author": "浙江艺旗教育科技有限公司", "main": "dist/main/index.js", "productName": "AI画影随形", "scripts": {"prepare": "husky install", "dev": "vite --mode development", "build:live": "vite build --mode live", "build:test": "vite build --mode test", "build:ele:live": "vite build --mode live", "build:ele:test": "vite build --mode test", "pack:live": "node build.js live pack", "pack:test": "node build.js test pack", "publish:live": "node build.js live publish", "publish:test": "node build.js test publish", "prettier": "npx prettier --write ."}, "dependencies": {"@koa/router": "^12.0.0", "colors": "^1.4.0", "compressing": "^1.6.2", "electron-dl": "^3.3.1", "electron-log": "^5.0.0-beta.23", "electron-updater": "^5.3.0", "fast-folder-size": "^2.2.0", "filelist-utils": "^1.8.1", "koa": "^2.13.4", "koa-body": "^6.0.1", "koa-static": "^5.0.0", "systeminformation": "^5.17.13"}, "devDependencies": {"@emotion/css": "^11.11.2", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@fingerprintjs/fingerprintjs": "^4.5.1", "@tailwindcss/forms": "^0.5.3", "@vant/area-data": "^2.0.0", "ag-psd": "^20.2.0", "ali-oss": "^6.20.0", "axios": "^1.7.3", "buffer": "^6.0.3", "china-location": "^2.1.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "eruda": "^3.2.1", "events": "^3.3.0", "fabric": "5.3.0", "file-saver": "^2.0.5", "framer-motion": "^7.2.1", "jotai": "^2.2.2", "js-beautify": "^1.15.1", "json5": "^2.2.3", "md5": "^2.3.0", "mdn-polyfills": "^5.20.0", "polygon-clipping": "^0.15.7", "qrcode.react": "^4.2.0", "qs": "^6.11.2", "react": "^18.2.0", "react-circular-progressbar": "^2.1.0", "react-colorful": "^5.6.1", "react-div-100vh": "^0.7.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-icons": "^4.8.0", "react-infinite-scroll-hook": "^5.0.1", "react-router-dom": "^6.3.0", "react-toastify": "^10.0.5", "react-use": "^17.4.0", "react-vant": "^3.3.5", "recorder-core": "^1.3.24040900", "swiper": "^11.1.8", "transformation-matrix": "^2.16.1", "weixin-units": "^0.0.98", "xgplayer": "^3.0.19", "@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@emotion/babel-plugin": "^11.11.0", "@msb-next/vite-plugins": "^1.4.0", "@types/ali-oss": "^6.16.11", "@types/crypto-js": "^4.1.1", "@types/fabric": "^5.3.8", "@types/file-saver": "^2.0.7", "@types/md5": "^2.3.2", "@types/qs": "^6.9.7", "@types/react": "^18.0.17", "@types/react-color": "^3.0.12", "@types/react-dom": "^18.0.6", "@types/throttle-debounce": "^5.0.2", "@vitejs/plugin-legacy": "^5.4.1", "@vitejs/plugin-react": "3.1.0", "autoprefixer": "^10.4.8", "electron": "22.3.13", "electron-builder": "^23.3.3", "electron-notarize": "^1.2.2", "husky": "^8.0.3", "postcss": "^8.4.16", "postcss-px-to-viewport-8-plugin": "^1.2.5", "postcss-pxtorem": "^6.1.0", "prettier": "^2.8.8", "sass": "^1.54.4", "tailwindcss": "^3.1.8", "terser": "^5.31.3", "typescript": "^4.7.4", "vite": "^4.3.1", "vite-plugin-electron": "^0.15.4", "vite-plugin-electron-renderer": "^0.14.5"}, "engines": {"node": "^14.18.0 || >=16.0.0"}}