<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.meishubao</groupId>
    <artifactId>art-creation</artifactId>
    <version>0.0.1</version>
    <packaging>pom</packaging>
    <properties>
        <jkube.debug.enabled>false</jkube.debug.enabled>
        <jkube.docker.verbose>false</jkube.docker.verbose>
        <jkube.docker.logStdout>true</jkube.docker.logStdout>
        <maven.deploy.skip>true</maven.deploy.skip>
        <docker.command>docker</docker.command>
        <registry.prefix>gitlab.metaleap.com:5050/cca-art/frontend/art-creation</registry.prefix>
    </properties>
    <distributionManagement>
        <repository>
            <id>${releases.id}</id>
            <name>${releases.name}</name>
            <url>${releases.url}</url>
        </repository>
        <snapshotRepository>
            <id>${snapshots.id}</id>
            <name>${snapshots.name}</name>
            <url>${snapshots.url}</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.eclipse.jkube</groupId>
                <artifactId>kubernetes-maven-plugin</artifactId>
                <version>1.8.0</version>
                <executions>
                    <execution>
                        <id>jkube-resource</id>
                        <phase>package</phase>
                        <goals>
                            <goal>resource</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jkube-build</id>
                        <phase>install</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jkube-push</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <enricher>
                        <config>
                            <jkube-revision-history>
                                <limit>10</limit>
                            </jkube-revision-history>
                            <jkube-service>
                                <name>${project.artifactId}</name>
                                <port>80</port>
                            </jkube-service>
                        </config>
                    </enricher>
                    <images>
                        <image>
                            <name>${registry.prefix}/${project.artifactId}:${jkube.namespace}-${project.version}</name>
                            <run>
                                <env>
                                    <LANG>en_US.UTF-8</LANG>
                                </env>
                            </run>
                            <build>
                                <from>gitlab.metaleap.com:5050/library/nginx:1.21.6</from>
                                <maintainer><EMAIL></maintainer>
                                <volumes>
                                    <volume>/tmp</volume>
                                </volumes>
                                <ports>
                                    <port>80</port>
                                </ports>
                                <assembly>
                                    <mode>dir</mode>
                                    <name>statics</name>
                                    <targetDir>/</targetDir>
                                    <inline>
                                        <fileSets>
                                            <fileSet>
                                                <directory>${project.basedir}/dist</directory>
                                                <outputDirectory>opt</outputDirectory>
                                                <includes>
                                                    <include>index.html</include>
                                                </includes>
                                            </fileSet>
                                            <fileSet>
                                                <directory>${project.basedir}/src/main/nginx</directory>
                                                <outputDirectory>etc/nginx/conf.d</outputDirectory>
                                                <includes>
                                                    <include>default.conf</include>
                                                    <include>chat-web.conf</include>
                                                </includes>
                                            </fileSet>
                                            <fileSet>
                                                <directory>${project.basedir}/src/main/nginx</directory>
                                                <outputDirectory>etc/nginx</outputDirectory>
                                                <includes>
                                                    <include>nginx.conf</include>
                                                </includes>
                                            </fileSet>
                                        </fileSets>
                                    </inline>
                                </assembly>
                            </build>
                        </image>
                    </images>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
