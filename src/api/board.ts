import request, { Res } from '@/utils/request'
import { getVisitorId, headerInfo } from '@/utils/tool'
import { GenericAbortSignal } from 'axios'
import md5 from 'md5'

// 翻译
export const translate = (data: any) => {
  return request.post<Res, Res>(`/metaleap-api/v1/draw/anon/translate`, data)
}
// 音频转文字
export const audio2word = (data: any) => {
  return request.post<Res, Res>(`/metaleap-api/v1/draw/anon/audio2word`, data)
}

// ai图生图
export const img2img = async (data: any) => {
  const { prompt, height, imageUrl, styleId, width } = data
  const timestamp = new Date().getTime()
  const txt = `${prompt}${height}${imageUrl}${styleId}${width}`
  const txt2 = `${headerInfo.deviceId}${headerInfo.appVersion}${headerInfo.deviceType}${txt}${timestamp}${import.meta.env.VITE_APP_APPSECRET}`
  const sign = md5(txt2)
  const drawId = await getVisitorId
  return request.post<Res, Res>(`/metaleap-api/v1/draw/img2img`, { ...data, timestamp, sign }, { headers: { drawId } })
}

// 保存作品
export const saveWorks = (data: any) => {
  return request.post<Res, Res>(`/metaleap-api/v1/works/save`, data)
}

// 获取绘画请求ID
export const getDrawId = () => {
  return request.get<Res, Res>(`/metaleap-api/v1/draw/anon/getDrawId`)
}

// 作品详情
export const worksDetail = (id: any) => {
  return request.get<Res, Res>(`/metaleap-api/v1/works/anon/get?id=${id}`)
}

// 风格
export const aiStyle = () => {
  return request.get<Res, Res>(`/metaleap-api/v1/draw/anon/list/style`)
}

// 作品列表
export const userWorksList = (page: any) => {
  return request.get<Res, Res>(`/metaleap-api/v1/works/list?page=${page}`)
}

// 作品开启时间
export const competitionTime = () => {
  return request.get<Res, Res>(`/metaleap-api/v1/works/anon/competition/time`)
}

// 作品参赛
export const competitionJoin = (data: any) => {
  return request.post<Res, Res>(`/metaleap-api/v1/works/competition/join`, data)
}

// 考试提交
export const examSumit = (data: any) => {
  return request.post<Res, Res>(`/metaleap-api/v1/works/competition/join`, data)
}
