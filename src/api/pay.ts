import request, { Res } from '@/utils/request'
import { headerInfo } from '@/utils/tool'
import md5 from 'md5'

// 获取中台国内地址（前三级）
export const getAddressListByCenter = () => {
  return request.post<Res, Res>(`/metaleap-api/v1/express/address/getAddressListByCenter`)
}

// 获取中台国内地址（下一级）
// code	区级编号	query	true
export const getNextAddressByCode = (data: any) => {
  return request.post<Res, Res>(`/metaleap-api/v1/express/address/getNextAddressByCode`, data, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 新建用户收获地址 - 前端（安卓、IOS、有token的H5）- 服务端
// userId	用户ID	query	true
// integer(int64)
// username	收件人姓名	query	true
// string
// phone	收件人电话	query	true
// string
// province	省	query	true
// string
// city	市	query	true
// string
// area	区	query	true
// string
// street	街道	query	true
// string
// addressDetail	详细地址	query	true
// string
// defaults	是否默认	query	true
// integer(int32)
export const createUserAddress = (data: any) => {
  return request.post<Res, Res>(`/metaleap-api/v1/express/address/createUserAddress`, data, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 获取微信openId
// wechatCode 微信code
export const getOpenId = (data: any) => {
  return request.get<Res, Res>(`/metaleap-api/v1/pay/anon/getOpenId?wechatCode=${data.wechatCode}`)
}

// 微信JSAPI
// "channel": "H5", 渠道
// "templateCode": "H5QA20240815", 商品编码
// "timestamp": 0, 时间戳
// "sign": "", 签名 = MD5(deviceId + appVersion + deviceType + {参数} + timestamp + APPSECRET) 参数：channel + templateCode
// "openId": "" 微信openId
// "addressId": 0 地址ID
export const wechatJs = (data: any) => {
  const { channel, templateCode, openId } = data
  const timestamp = new Date().getTime()
  const txt = `${channel}${templateCode}`
  const txt2 = `${headerInfo.deviceId}${headerInfo.appVersion}${headerInfo.deviceType}${txt}${timestamp}${import.meta.env.VITE_APP_APPSECRET}`
  const sign = md5(txt2)
  return request.post<Res, Res>(`/metaleap-api/v1/pay/wechatJs`, { ...data, sign, timestamp })
}

// 网页支付宝
// "channel": "H5", 渠道
// "templateCode": "H5QA20240815", 商品编码
// "timestamp": 0, 时间戳
// "sign": "", 签名 = MD5(deviceId + appVersion + deviceType + {参数} + timestamp + APPSECRET) 参数：channel + templateCode
// "addressId": 0 地址ID
// targetUrl 跳转地址
export const h5Alipay = (data: any) => {
  const { channel, templateCode } = data
  const timestamp = new Date().getTime()
  const txt = `${channel}${templateCode}`
  const txt2 = `${headerInfo.deviceId}${headerInfo.appVersion}${headerInfo.deviceType}${txt}${timestamp}${import.meta.env.VITE_APP_APPSECRET}`
  const sign = md5(txt2)
  return request.post<Res, Res>(`/metaleap-api/v1/pay/h5Alipay`, { ...data, sign, timestamp })
}

// 会员权益列表
export const perquisiteList = () => {
  return request.get<Res, Res>('/metaleap-api/v1/member/anon/perquisite/list')
}

// 状态订单列表
export const listStatus = ({ minId, status = -1 }: { minId: number | string; status: number }) => {
  return request.get<Res, Res>(`/metaleap-api/v1/order/list/status?minId=${minId}&status=${status}`)
}
