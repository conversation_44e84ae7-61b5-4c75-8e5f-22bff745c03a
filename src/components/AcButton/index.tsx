import { CSSProperties, ReactNode } from 'react'
import './index.css'
interface Props {
  children: ReactNode
  onClick?: () => void
  className?: string
  style?: CSSProperties
}
export default function AcButton({ children, onClick, className = '', style }: Props) {
  return (
    <div
      className={`ac-btn ${className}`}
      style={{
        ...style
      }}
      onClick={onClick}
    >
      {children}
    </div>
  )
}
