.ac-form-card {
  margin: 10px 28px;
  padding: 8px 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 8px 10 23px 0px rgba(233, 233, 233, 0.25);
  border: 1px solid #eee;
}
.ac-form-card .desc {
  z-index: 1;
  left: 20px;
  top: calc(50% + 5px);
}
.ac-form-card .rv-cell {
  padding: 6px 12px;
  display: flex;
  align-items: center;
}
.idCard .rv-uploader {
  width: 45px;
  height: 45px;
  border-radius: 10px;
  cursor: pointer;
}

.idCard .rv-field__children {
  flex-direction: column-reverse;
  align-items: end;
}
.idCard .rv-field__error-message {
  position: absolute;
  z-index: 2;
  top: calc(50% - 13px);
}
.idCard .rv-cell::before {
  top: 13px;
}
.idCard .rv-field__label {
  margin-bottom: 10px;
}
.idCard .rv-uploader__preview-image {
  width: 45px;
  height: 45px;
  border-radius: 10px;
}
.ac-form-card .rv-cell__title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #272d53;
  width: 6.8em;
}
.ac-form-card .rv-cell::before {
  left: 5px;
}
.ac-form-card .rv-input__control {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
}
.ac-form-card .rv-field__children {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
}
