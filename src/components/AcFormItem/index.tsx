import { FormItemProps } from 'react-vant'
import { Form } from 'react-vant'
import { WarningO } from '@react-vant/icons'
import './index.css'

export default function AcFormItem({ className = '', desc, ...props }: FormItemProps & { desc?: string }) {
  return (
    <div className={`ac-form-card relative ${className}`}>
      {desc && (
        <div className="desc flex absolute items-center gap-1">
          <WarningO color="#FE974C" /> <div className="text-[12px] text-[#c9cad6]">{desc}</div>
        </div>
      )}
      <Form.Item {...props} className="ac-form-content" />
    </div>
  )
}
