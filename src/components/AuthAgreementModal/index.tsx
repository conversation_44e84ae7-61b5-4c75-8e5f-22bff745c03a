import { Popup, Button } from 'react-vant'
import './index.css'
export default function AuthAgreementModal({ visible, containerRef, onAgree, onCancel }: { containerRef: any; visible: boolean; onAgree: () => void; onCancel: () => void }) {
  return (
    <Popup className="auth-agreement-popup" teleport={containerRef} visible={visible} style={{ height: '80%', padding: '16px' }} position="center" round onClose={onCancel}>
      <div className="h-full flex flex-col">
        <h2 className="text-center text-lg font-bold mb-4">个人信息使用授权书</h2>

        <div className="flex-1 overflow-y-auto text-gray-700 text-sm leading-6">
          <p className="mb-3 font-medium">授权人：未成年参与者的监护人</p>
          <p className="mb-3 font-medium">被授权人：杭州边界人工智能应用软件有限公司</p>

          <p className="mb-3">
            基于<span className="underline px-2">中国美术学院</span>委托被授权人提供与<span className="underline px-2">“智性觉醒·AI艺术中国”</span>
            活动的报名及相关服务，依据《中华人民共和国个人信息保护法》，为确保参与人报名参加的<span className="underline px-2">“智性觉醒·AI艺术中国”</span>
            活动顺利举办，并确保获奖证书及奖牌的按时发放，特做如下授权：
          </p>

          <h3 className="font-bold mb-3">一、同意被授权人收集使用参与者个人信息</h3>
          <p className="mb-2">
            1. 授权人同意被授权人为提供与<span className="underline px-2">“智性觉醒·AI艺术中国”</span>活动报名及相关服务而收集、存储、使用、加工、传输、提供、公开、删除参与者个人信息，包括：
            <span className="underline px-2 font-bold">姓名、姓名拼音、年龄、组别、作品类别、电子作品收货人姓名、收货人手机号、收货人详细地址。（以上各项合称“参与者个人信息”）</span>
          </p>

          <p className="mb-2">
            <span className="font-bold">2.授权人同意被授权人收集、存储、使用、加工、传输、提供、公开、删除参与者个人信息的目的包括：</span>
            报名信息表的填写、信息审核、以及通过网络传输方式提供给第三方机构进行评审、制作证书和奖牌、颁发证书和奖牌、获奖名单在活动官方平台线上公布，以及作品在活动官方平台在线展出等。
          </p>
          <p className="mb-2 font-bold">3.授权人同意被授权人在为实现收集、使用参与者个人信息的目的所必需的合理期限内存储个人信息。</p>

          <h3 className="font-bold mb-3">二、同意被授权人向第三方机构提供参与者个人信息</h3>
          <p className="mb-2">
            授权人同意被授权人将含有参与者个人信息的活动报名信息表、电子作品提供给 <span className="underline px-2 ">杭州边界人工智能应用软件有限公司</span> ，并由{' '}
            <span className="underline px-2 ">杭州边界人工智能应用软件有限公司</span>
            进行相关处理后，提交至{' '}
            <span className="underline px-2 ">
              中国美术学院 <span className="font-bold">（以下简称“组委会/主办方”）。</span>
            </span>
            <span className="underline px-2 ">各省美术院校</span> 合称“协办单位”。
          </p>
          <p className="mb-2">
            1. 向协办单位提供的参与者个人信息包括：<span className="underline font-bold px-2">姓名、姓名拼音、年龄、组别、作品类别、作品名称、作品描述、电子作品。</span>
          </p>
          <p className="mb-2 font-bold">2.参与者个人信息的限定处理场景和目的</p>
          <p className="mb-2">
            2.1 用于将 ：<span className="underline px-2">“智性觉醒·AI艺术中国”</span> 的活动报名信息表、电子作品提供给 <span className="underline px-2 ">杭州边界人工智能应用软件有限公司</span> ，并由
            <span className="underline px-2 ">杭州边界人工智能应用软件有限公司</span>
            再次提供给 <span className="underline px-2">各省美术院校</span>
            ，由其进行审核校对后，将参与者的作品信息（包括：姓名、姓名拼音、年龄、组别、作品类别、作品名称、作品描述、电子作品）提交至<span className="underline px-2 ">组委会/主办方</span>
            ，进行评审；评审结果出来后，由组委会将获奖名单反馈给被授权人，由其在<span className="underline px-2 ">xxxx</span>{' '}
            进行公布，公布信息包括：姓名、组别、奖项（一等奖/二等奖..）、作品名称；参与者可以通过 查询获奖情况。
          </p>
          <p className="mb-2">
            2.2 用于<span className="underline px-2 ">组委会/主办方</span> 根据本次活动的最终获奖情况，完成获奖证书及奖牌的制作与发放。
          </p>
          <h3 className="font-bold mb-3">三、同意被授权人及其关联方使用参与者作品</h3>
          <p className="mb-3">授权人同意被授权人及其关联方出于宣传目的，通过展览、展示等形式使用参与者作品的图 片。</p>

          <h3 className="font-bold mb-3">四、授权期限</h3>
          <p className="mb-3">
            本授权书的授权期限为自授权人签署本授权书之日起至被授权人及第三方机构所提供的服务完全结束止。授权人同意被授权人有权在授权期限届满后仍按照法律法规及监管要求保留本授权书及业务存档所必须的个人信息及行为数据。
          </p>

          <h3 className="font-bold mb-3">五、其他条款</h3>
          <p className="mb-2 font-bold underline">
            1.本人已清楚知悉本《授权书》所有内容（特别是以加粗和/或下划线方式进行提示的内容）的意义以及由此产生的法律效力，自愿做出上述授权，本《授权书》是本人真实的意思表达，本人同意承担由此带来的一切法律后果。
          </p>
          <p className="mb-2 font-bold underline">2.授权人承诺并确认，如果参加与 xxxx 活动的参与者是14周岁以下的未成年人，本《授权书》应由监护人完成签署。</p>
          <p className="mb-2 font-bold underline">
            3.被授权人非常重视未成年人个人信息的保护，会根据国家相关法律法规的规定保护未成年人的个人信息，仅在法律允许、父母或其他监护人明确同意的情况下收集、使用、储存或披露未成年人的个人信息。
          </p>
          <p className="mb-2">
            4.如果您对此有任何疑问，请随时通过电子邮箱与我们联系，将您的询问发送至<span className="font-bold px-2 underline"><EMAIL>。</span>
          </p>
          <p className="mb-2">5.被授权人可向其书面指定的关联方进行转授权。</p>
          <p className="mb-2">6.本文件经授权人同意后生效。</p>
        </div>

        <div className="grid grid-cols-2 gap-4 mt-4">
          <Button block onClick={onCancel} className="bg-gray-100 text-gray-600 hover:bg-gray-200 !rounded-3xl disagree-btn">
            不同意
          </Button>
          <Button block type="primary" onClick={onAgree} className="hover:opacity-90 !rounded-3xl agree-btn">
            同意并继续
          </Button>
        </div>
      </div>
    </Popup>
  )
}
