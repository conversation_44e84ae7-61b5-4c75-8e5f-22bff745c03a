import styled from '@emotion/styled'
import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'
import { RiCloseLine, RiContractUpDownFill, RiExpandUpDownFill, RiSubtractFill } from 'react-icons/ri'
import { VscChromeClose, VscChromeMaximize, VscChromeMinimize } from 'react-icons/vsc'

import { ipcInvoke, windowInfo } from '@/utils/electron'

export default function Index() {
  const [maxSize, setMaxSize] = useState<boolean>(false)

  useEffect(() => {
    if (!localStorage.prveWidth || !localStorage.prveHeight) {
      localStorage.prveWidth = 1200
      localStorage.prveHeight = 768
    }
  }, [])

  const quitApp = () => {
    ipcInvoke('app', { method: 'quit' })
  }

  const minimize = () => {
    ipcInvoke('win', { method: 'minimize' })
  }

  const maximizeOrunmaximize = () => {
    console.log('maximizeOrunmaximize')
    ipcInvoke('win', { method: 'getSize' }).then((reslut) => {
      const [width, height] = reslut.data
      console.log('🚀 ~ ipcInvoke ~ reslut.data:', reslut.data)

      if (width <= Number(localStorage.workAreaWidth) - 100) {
        setMaxSize(false)
        localStorage.prveWidth = width
        localStorage.prveHeight = height
        ipcInvoke(
          'win',
          { method: 'setBounds' },
          {
            width: Number(localStorage.workAreaWidth),
            height: Number(localStorage.workAreaHeight),
            x: 0,
            y: 0
          },
          false
        )
      } else {
        ipcInvoke('win', { method: 'getMinimumSize' }).then((reslut) => {
          setMaxSize(true)
          ipcInvoke(
            'win',
            { method: 'setBounds' },
            {
              width: Number(localStorage.prveWidth),
              height: Number(localStorage.prveHeight),
              x: 0,
              y: 0
            },
            false
          )
          ipcInvoke('win', { method: 'center' })
        })
      }
    })
  }

  const isMac = windowInfo.isMac

  return (
    <Container className={`fixed left-0 top-0 z-[8888] w-full h-[28px] flex items-center justify-between px-4 bg-[#f5f5f5] select-none truncate`}>
      <div className={`group w-[75px] flex items-center justify-between cursor-pointer ${isMac ? 'visible' : 'invisible'}`}>
        <div onClick={() => quitApp()} className="w-[14px] h-[14px] flex items-center justify-center text-[14px] rounded-full bg-[#E96A5F] text-transparent group-hover:text-[#000]">
          <RiCloseLine />
        </div>
        <div onClick={() => minimize()} className="w-[14px] h-[14px] flex items-center justify-center text-[14px] rounded-full bg-[#F3C052] text-transparent group-hover:text-[#000]">
          <RiSubtractFill />
        </div>
        <div onClick={() => maximizeOrunmaximize()} className="w-[14px] h-[14px] flex items-center justify-center text-[14px] rounded-full bg-[#67C756] text-transparent group-hover:text-[#000] ">
          {maxSize ? <RiExpandUpDownFill className="text-[12px] font-bold rotate-[-50deg]" /> : <RiContractUpDownFill className="text-[12px] font-bold rotate-[-50deg]" />}
        </div>
      </div>
      <div onDoubleClick={() => maximizeOrunmaximize()} className="title flex-1 text-center text-[14px]">
        美术宝未来学苑
      </div>
      <div className={`w-[120px] flex items-center justify-between mr-[-1rem] ${!isMac ? 'visible' : 'invisible'}`}>
        <div onClick={() => minimize()} className="w-[40px] h-full flex items-center justify-center text-[#666] hover:text-[#000] cursor-pointer">
          <VscChromeMinimize />
        </div>
        <div onClick={() => maximizeOrunmaximize()} className="w-[40px] h-full flex items-center justify-center text-[#666] hover:text-[#000] cursor-pointer">
          <VscChromeMaximize />
        </div>
        <div onClick={() => quitApp()} className="w-[40px] h-full flex items-center justify-center text-[#666] hover:text-[#000] cursor-pointer">
          <VscChromeClose />
        </div>
      </div>
    </Container>
  )
}
const Container = styled(motion.header)`
  -webkit-app-region: none;
  .title {
    -webkit-app-region: drag;
  }
`
