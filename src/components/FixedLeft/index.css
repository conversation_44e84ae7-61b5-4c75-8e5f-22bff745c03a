.fixed-left-container {
  box-shadow: 0px 7 15px 0px rgba(211, 209, 216, 0.25);
  border-radius: 20px 0px 0px 20px;
}
.fixed-left-children-container {
  border-radius: 0px 0px 0px 20px;

}
.header-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  font-size: 24px;
  font-weight: 500;
  cursor: pointer;
}
.close-btn {
  right: 20px;
}
.back-btn {
  left: 20px;
}
.rv-popup--bottom {
  right: 0 !important;
  bottom: 0 !important;
  left: unset !important;
  width: 400px !important;
  height: 383px !important;
}
