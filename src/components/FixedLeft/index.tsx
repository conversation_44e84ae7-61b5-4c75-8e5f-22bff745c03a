import { ArrowLef<PERSON>, Cross } from '@react-vant/icons'
import './index.css'
import { useRef, isValidElement, cloneElement } from 'react'

// 添加类型定义
interface Props {
  title: string
  modelVisible: boolean
  backgroundColor?: string
  className?: string
  onClose?: () => void
  onBack?: () => void
  children: React.ReactNode
  hasReturnBtn?: boolean
  hasCloseBtn?: boolean
  containerRef?: any
}

const EventModal = ({ onClose, onBack, className = '', modelVisible, title, children, backgroundColor = '#f3f3f3', hasReturnBtn = false, hasCloseBtn = true }: Props) => {
  const containerRef = useRef<HTMLDivElement>(null)
  // 将header提取为独立函数
  const renderHeader = () => (
    <div className="flex items-center justify-center p-13 border-b relative h-[76px]">
      {hasReturnBtn && (
        <div onClick={onBack} className="header-btn back-btn cursor-pointer text-2xl">
          <ArrowLeft />
        </div>
      )}

      <div className="text-center text-[16px] font-medium text-[#272D53]">{title}</div>
      {hasCloseBtn && (
        <div onClick={onClose} className="header-btn close-btn cursor-pointer text-2xl">
          <Cross />
        </div>
      )}
    </div>
  )
  console.log(containerRef)

  return (
    <div className={`${className} fixed inset-0 bg-gray-500/50 transition-opacity duration-300 ${modelVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
      <div className="flex justify-end h-full">
        <div ref={containerRef} className="fixed-left-container relative w-[400px] bg-white shadow-xl flex flex-col">
          {renderHeader()}
          <div className="fixed-left-children-container flex-1 overflow-y-auto overflow-x-hidden" style={{ backgroundColor }}>
            {isValidElement(children) ? cloneElement(children, { containerRef: containerRef.current } as any) : children}
          </div>
        </div>
      </div>
    </div>
  )
}

export default EventModal
