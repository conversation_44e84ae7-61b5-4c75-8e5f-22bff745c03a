import React, { useState } from 'react'
import bitmapImg from '@/assets/images/home/<USER>'

interface IProps {
  style?: React.CSSProperties
  width?: string
  onLoad?: () => void
  [key: string]: any
}

export default ({ style = {}, onLoad = () => {}, width = '50%', ...props }: IProps) => {
  const { display = '' } = style
  const [state, setState] = useState(false)
  const [errorState, setErrorState] = useState(false)
  const load = () => {
    setState(true)
    onLoad()
  }

  const error = () => {
    setErrorState(true)
    onLoad()
  }

  return (
    <React.Fragment>
      <img {...props} style={{ ...style, display: state ? display || '' : 'none' }} onLoad={load} onError={error} />
      {!state && !errorState ? (
        <div className="w-full h-full flex_center flex-col absolute top-0 z-10">
          <img style={{ ...style, margin: '0 auto', width, display }} src={bitmapImg} alt="placeholder" />
        </div>
      ) : null}
      {errorState ? (
        <div className="w-full h-full flex_center flex-col absolute top-0 z-10">
          <img style={{ margin: '0 auto', width: width, display }} src={bitmapImg} alt="placeholder" />
        </div>
      ) : null}
    </React.Fragment>
  )
}
