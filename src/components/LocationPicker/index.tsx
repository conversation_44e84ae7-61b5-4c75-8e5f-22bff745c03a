import { Picker } from 'react-vant'
import locationData from 'china-location/dist/location.json'

export interface LocationValue {
  province: {
    code: string
    name: string
  }
  city: {
    code: string
    name: string
  }
  district: {
    code: string
    name: string
  }
}

export default function LocationPicker({
  onConfirm,
}: {
  onConfirm: (value: LocationValue) => void
}) {
  const provinceOptions = Object.values(locationData).map((province) => ({
    text: province.name,
    value: province.code,
    children: Object.values(province.cities).map((city) => ({
      text: city.name,
      value: city.code,
      children: Object.entries(city.districts).map(([code, name]) => ({
        text: name,
        value: code
      }))
    }))
  }))

  return (
    <Picker
      popup
      columns={provinceOptions}
      onConfirm={(_: any, selected: any) => {
        const [province, city, district] = selected
        onConfirm({
          province: { code: province.value, name: province.text },
          city: { code: city.value, name: city.text },
          district: { code: district.value, name: district.text }
        })
      }}
    />
  )
}