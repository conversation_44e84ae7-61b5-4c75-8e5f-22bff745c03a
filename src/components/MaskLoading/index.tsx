import React, { useEffect, useRef, useState } from 'react'
import { useInterval } from 'react-use'
import loadingGif from '@/assets/images/global/loading.png'
import Modal from '../Modal'

interface Props {
  state?: boolean
  scale?: number
  time: number
}

export default function Index({ state = true, time = 2000, scale = 95, ...restProps }: Props) {
  const tRef = useRef<NodeJS.Timeout | null>(null)
  const [progress, setProgress] = useState(0)
  const [progressControl, setProgressControl] = useState({
    isStart: false, // 是否已经开始
    runnig: false, // 是否运行
    speed: 200 // 步进一次的时间
  })

  useEffect(() => {
    if (!state) {
      setProgressControl({
        isStart: false,
        runnig: false,
        speed: 200
      })
      setProgress(100)
    } else {
      setProgress(0)
      setProgressControl({
        isStart: true,
        runnig: true,
        speed: time / scale
      })
      tRef.current = setTimeout(() => {
        setProgressControl((v) => ({
          ...v,
          runnig: false
        }))
      }, time)
    }
    return () => {
      tRef.current && clearTimeout(tRef.current)
    }
  }, [state])

  // 假进度
  useInterval(
    () => {
      progress < 100 && setProgress((v) => v + 1)
    },
    progressControl.runnig && progress < 100 ? progressControl.speed : null
  )

  return (
    <Modal show={state} mask={false}>
      <div className="w-full h-full flex_center flex-col z-10 font-normal" {...restProps}>
        <div className="bg-[#00000045] rounded-[10px] flex_center w-[117px] h-[89px]">
          <img className="w-[34px] h-[34px] animate-spin" src={loadingGif} />
          {/* <div
            className="font-medium text-[40px] md:text-[3.28vw] text-[#D95100] leading-[1] ai_progress_num_stroke relative"
            data-content={`${progress}%`}
          >
            {progress}%
          </div> */}
        </div>
      </div>
    </Modal>
  )
}
