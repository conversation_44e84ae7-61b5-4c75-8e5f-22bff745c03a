import { Steps } from 'react-vant'
import './index.css'
import iconPen from './icon-pen.png'

type StepStatus = 'pending' | 'process' | 'finished' | 'rejected'

export default function MyRegistration({ userRegisterInfo }: any) {
  const { registrationBean } = userRegisterInfo
  const steps = [
    { status: 'finished', title: '上传资料' },
    { status: 'process', title: '资料驳回' },
    { status: 'pending', title: '作品评选' },
    { status: 'pending', title: '证书发放' }
  ] as const

  return (
    <div className="p-7 bg-white shadow-sm m-[20px] rounded-[28px]">
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="col-span-1 aspect-square bg-gray-100 rounded-lg overflow-hidden">
          <img src={registrationBean.generateImage} className="w-full h-full object-cover" alt="参赛作品" />
        </div>
        <div className="col-span-2 pl-4">
          <h3 className="text-[16px] font-medium mb-2">卡通创作活动作品征集</h3>

          {/* <div className="flex items-center text-gray-600 text-sm mb-2 ">
            <img src="https://img.icons8.com/ios/50/000000/paint-palette.png" className="w-4 h-4 mr-2" alt="主题图标" />
            <span className="font-medium">参赛主题：</span>
            <span>{}</span>
          </div> */}

          <div className="flex items-center text-[#272D53 ] text-[12px] mb-4 mt-8">
            <img src={iconPen} className="w-4 h-4 mr-2" alt="组别图标" />
            <span>参赛组别</span>
            <span className="text-[rgba(0,0,0,0.08)] mx-[8px]">|</span>
            <span>少儿组</span>
          </div>

          {/* <button
            className="text-white text-sm rounded-[56px] px-3 py-1 w-[143px] h-[38px]"
            style={{ background: 'linear-gradient(180deg, #84DD6D 0%, #57C43B 100%)' }}
            onClick={() => console.log('重新上传')}
          >
            修改资料
          </button> */}
        </div>
      </div>

      <Steps
        direction="horizontal"
        activeColor="#4d53ed" // 当前步骤颜色
        inactiveColor="#eff0f6" // 未完成步骤颜色
        className="my-registration-steps"
        active={0}
      >
        {steps.map((step, index) => (
          <Steps.Item key={index}>
            <span className={`text-sm ${step.status === 'process' ? 'font-medium' : ''}`}>{step.title}</span>
          </Steps.Item>
        ))}
      </Steps>
    </div>
  )
}
