import React, { useEffect, useRef, useState } from 'react'
import { useInterval } from 'react-use'
import progressImg from '@/assets/images/board/progress.png'

interface Props {
  // 是否显示
  state?: boolean
  // 进度条下方文字
  txt?: string | React.ReactNode
  // 最大进度
  scale?: number
  // 默认进度
  defaultProgress?: number
  // 跑满最大进度的时间
  time: number
  // 不自己跑进度条
  quiet?: boolean
}

export default function Index({ state = true, time = 2000, scale = 95, defaultProgress = 0, quiet = false, txt = '正在生成中请稍等', ...restProps }: Props) {
  // console.log('defaultProgress', defaultProgress, exProgress, time)
  const tRef = useRef<NodeJS.Timeout | null>(null)
  const t2Ref = useRef<NodeJS.Timeout | null>(null)
  const [progress, setProgress] = useState(0)
  const [show, setShow] = useState(false)
  const [progressControl, setProgressControl] = useState({
    isStart: false, // 是否已经开始
    runnig: false, // 是否运行
    speed: 200 // 步进一次的时间
  })

  useEffect(() => {
    setProgress(Math.min(scale, defaultProgress))
  }, [scale, defaultProgress])

  useEffect(() => {
    if (!state) {
      setProgressControl({
        isStart: false,
        runnig: false,
        speed: 200
      })
      setProgress(100)
      // t2Ref.current = setTimeout(() => {
      setShow(false)
      // }, 200);
    } else {
      setShow(true)
      setProgress(Math.min(scale, defaultProgress))
      setProgressControl({
        isStart: true,
        runnig: quiet ? false : true,
        speed: time / (scale - Math.min(scale, defaultProgress))
      })
      tRef.current = setTimeout(() => {
        setProgressControl((v) => ({
          ...v,
          runnig: false
        }))
      }, time)
    }
    return () => {
      tRef.current && clearTimeout(tRef.current)
      t2Ref.current && clearTimeout(t2Ref.current)
    }
  }, [state])

  // 假进度
  useInterval(
    () => {
      progress < 100 && setProgress((v) => v + 1)
    },
    progressControl.runnig && progress < 100 ? progressControl.speed : null
  )

  return show ? (
    <div className="w-full h-full flex_center flex-col absolute top-0 z-10 font-normal text-[31px] text-white leading-[48px] md:text-[2.99vw]" {...restProps}>
      <div className="w-[313px] h-[43px] mb-[30px] rounded-[22px] md:w-[23.44vw] md:h-[3.23vw] md:mb-[2.86vw] md:rounded-[2.12vw] relative">
        <div className="absolute top-[-52px] w-[45px] h-[41px] md:top-[-4.264vw] md:w-[3.69vw] md:h-[3.362vw]" style={{ left: `${Math.min(100, progress * 0.87)}%` }}>
          <img className="w-[45px] h-[41px] md:w-[3.69vw] md:h-[3.362vw] absolute left-0 top-0" src={progressImg} />
          <div className="text-[18px] mt-[6px] md:text-[1.476vw] md:mt-[0.492vw] text-[#FFFFFF] leading-none text-center z-10 relative">{progress}%</div>
        </div>
        <div className="w-full relative h-full rounded-full flex_center overflow-hidden">
          <div className="w-[99.99%] relative h-[99.99%] bg-black rounded-full"></div>
          <div className="ai_progress_linear h-[104%] w-[10%] absolute z-10 left-[-3%] top-[-2%]"></div>
          <div className="absolute z-20 left-[-1%] top-[-2%] ai_progress_linear h-[104%] w-[102%] rounded-full" style={{ left: `${Math.min(100, progress * 0.87 - 88)}%` }}></div>
        </div>
      </div>
      {txt ? <div className="text-center font-normal text-[33px] leading-[1.5] md:text-[2.34vw] text-[#B4BFD9] flex">{txt}</div> : null}
    </div>
  ) : null
}
