import { useLocation, useNavigate } from 'react-router-dom'
import { HexColorPicker } from 'react-colorful'
import diImg from '@/assets/images/board/di_1.png'
import { AnimatePresence, motion } from 'framer-motion'
import './index.css'
import { useState } from 'react'

export const fullAnimate = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 10 },
  transition: {
    duration: 0.25,
    type: 'spring',
    damping: 40,
    stiffness: 600
  }
}

const colors = ['#F5222D', '#FA8C16', '#FADB14', '#8BBB11', '#52C41A', '#13A8A8']
const colors2 = ['#1677FF', '#2F54EB', '#722ED1', '#EB2F96', '#edbcbc', '#edd6bc']

// 1	001暖色调	e8aea3	f7dfc4	d5601d	384e8c	056b67	c4a7c4
// 2	002暖色调	f3ece3	22699e	df503a	f1b799	d0a541	409b68
// 3	003暖色调	fae4e9	fcfdfe	76a0d4	77a1d4	f3b516	ea5f2c
// 4	004冷色调	3e68a5	e794b8	6ec3cc	989ec8	89cae4	a6bcc4
// 5	005冷色调	afd3d6	3c6db1	c687a1	32948d	fefefe	070405
// 6	006冷色调	2557a6	142d87	192648	5aa9de	dba4c8	e52b63
// 7	007互补色	ed6f37	1f59a0	1da774	d76fa7	f8fbfb	c4a7c4
// 8	008互补色	f3d522	724497	ee7e1c	99ceaf	43ab83	6ea3d7
// 9	009互补色	ef8f8f	15915f	f0e92e	adbe2f	8ec7c7	040101
// 10	010冷色调	80bed7	171618	fbfdfd	d87359	efd55f	e7ada2

const ColorCard = [
  {
    title: '001暖色调',
    bgColor: '#e8aea3',
    colorList: ['#f7dfc4', '#d5601d', '#384e8c', '#056b67', '#c4a7c4']
  },
  {
    title: '002暖色调',
    bgColor: '#f3ece3',
    colorList: ['#22699e', '#df503a', '#f1b799', '#d0a541', '#409b68']
  },
  {
    title: '003暖色调',
    bgColor: '#fae4e9',
    colorList: ['#fcfdfe', '#76a0d4', '#77a1d4', '#f3b516', '#ea5f2c']
  },
  {
    title: '004冷色调',
    bgColor: '#3e68a5',
    colorList: ['#e794b8', '#6ec3cc', '#989ec8', '#89cae4', '#a6bcc4']
  },
  {
    title: '005冷色调',
    bgColor: '#afd3d6',
    colorList: ['#3c6db1', '#c687a1', '#32948d', '#fefefe', '#070405']
  },
  {
    title: '006冷色调',
    bgColor: '#2557a6',
    colorList: ['#142d87', '#192648', '#5aa9de', '#dba4c8', '#e52b63']
  },
  {
    title: '007互补色',
    bgColor: '#ed6f37',
    colorList: ['#1f59a0', '#1da774', '#d76fa7', '#f8fbfb', '#c4a7c4']
  },
  {
    title: '008互补色',
    bgColor: '#f3d522',
    colorList: ['#724497', '#ee7e1c', '#99ceaf', '#43ab83', '#6ea3d7']
  },
  {
    title: '009互补色',
    bgColor: '#ef8f8f',
    colorList: ['#15915f', '#f0e92e', '#adbe2f', '#8ec7c7', '#040101']
  },
  {
    title: '010冷色调',
    bgColor: '#80bed7',
    colorList: ['#171618', '#fbfdfd', '#d87359', '#efd55f', '#e7ada2']
  }
]

interface IColor {
  onChange: (color: string, type?: 0 | 1) => void
  value: string
  show: boolean
  close: () => void
  left?: number | string
  top?: number | string
  bottom?: number | string
  right?: number | string
  arrowAlign?: 'left' | 'right' | 'center'
}

export default function Index({ close, onChange, value, show, left, top, bottom, right, arrowAlign = 'right' }: IColor) {
  const [check, setCheck] = useState(0)
  return (
    <AnimatePresence>
      {show ? (
        <>
          <div className="w-screen h-screen fixed top-0 left-0 z-40" onClick={close}></div>
          <motion.div {...fullAnimate} className="absolute z-50  w-[295px]  hex-color" style={{ left, top, bottom, right }}>
            {/* <img className="w-full h-full absolute z-0 top-0 left-0" src={diImg} alt="" /> */}
            <div className="shadow-[0_0_0_1px_rgba(17,20,24,0.1),0_2px_4px_rgba(17,20,24,0.1),0_8px_24px_rgba(17,20,24,0.2)] rounded-[8px] w-full">
              <div
                aria-hidden="true"
                className={`absolute  h-[30px] w-[30px] bp5-popover-arrow ${top ? 'top-[-11px]' : 'bottom-[-15px]'} ${
                  arrowAlign === 'left' ? 'left-[27px]' : arrowAlign === 'right' ? 'right-[27px]' : 'left-[50%] ml-[-15px]'
                }`}
              >
                <svg viewBox="0 0 30 30" className="rotate-90">
                  <path
                    style={{ fillOpacity: 0.1 }}
                    className="fill-[#111418]"
                    d="M8.11 6.302c1.015-.936 1.887-2.922 1.887-4.297v26c0-1.378-.868-3.357-1.888-4.297L.925 17.09c-1.237-1.14-1.233-3.034 0-4.17L8.11 6.302z"
                  ></path>
                  <path className="fill-[#fff]" d="M8.787 7.036c1.22-1.125 2.21-3.376 2.21-5.03V0v30-2.005c0-1.654-.983-3.9-2.21-5.03l-7.183-6.616c-.81-.746-.802-1.96 0-2.7l7.183-6.614z"></path>
                </svg>
              </div>
              <div className="relative w-full rounded-[8px] bg-white py-[18px]">
                <div className="w-[258px] mx-auto">
                  <div className="w-[258px] h-[38px] rounded-[19px] bg-[#eff3f9] flex relative shadow-[rgb(204,219,232)_3px_3px_6px_0px_inset,rgba(255,255,255,0.5)_-3px_-3px_6px_1px_inset]">
                    <div
                      className={`transition-all duration-[0.25s] w-[129px] h-[38px] rounded-[19px] absolute top-0 ${check === 0 ? 'left-0' : 'left-[50%]'}`}
                      style={{
                        background: 'linear-gradient( 180deg, #8381E7 0%, #5C62EA 100%)'
                      }}
                    ></div>
                    <div
                      onClick={() => setCheck(0)}
                      className={`cursor-pointer transition-all duration-[0.25s] flex-1 font-normal flex_center z-10 text-[13px] text-right not-italic ${
                        check === 0 ? 'text-white' : 'text-[#272D53]'
                      }`}
                    >
                      调色板
                    </div>
                    <div
                      onClick={() => setCheck(1)}
                      className={`cursor-pointer transition-all duration-[0.25s] flex-1 font-normal flex_center z-10 text-[13px] text-right not-italic ${
                        check === 1 ? 'text-white' : 'text-[#272D53]'
                      }`}
                    >
                      配色方案
                    </div>
                  </div>
                  {/* 调色板 */}
                  {check === 0 ? (
                    <>
                      <HexColorPicker color={value} onChange={onChange} />
                      <div className="z-50 relative flex justify-between items-center w-[258px] mx-auto flex-row mb-[10px]">
                        {colors.map((item, i) => (
                          <div
                            key={i}
                            style={{ backgroundColor: item }}
                            onClick={() => onChange(item)}
                            className={`anim_btn w-[26px] h-[26px] rounded box-border ${value === item ? 'border-[2px] border-[#0E63FF] border-solid' : ''}`}
                          ></div>
                        ))}
                      </div>
                      <div className="z-50 relative flex justify-between items-center w-[258px] mx-auto flex-row">
                        {colors2.map((item, i) => (
                          <div
                            key={i}
                            style={{ backgroundColor: item }}
                            onClick={() => onChange(item)}
                            className={`anim_btn w-[26px] h-[26px] rounded box-border ${value === item ? 'border-[2px] border-[#0E63FF] border-solid' : ''}`}
                          ></div>
                        ))}
                      </div>
                    </>
                  ) : (
                    <div className="mt-[13px]">
                      {/* 色卡 */}
                      {ColorCard.map((item, i) => (
                        <div key={i} className="mb-[8px]">
                          <div className="font-normal text-[9px] text-[#636880] leading-[13px] text-left not-italic">{item.title}</div>
                          <div className="mt-[4px] flex justify-between">
                            <div
                              onClick={() => onChange(item.bgColor, 1)}
                              className={`cursor-pointer anim_btn w-[90px] h-[25px] border rounded-[4px] border-solid box-border font-normal text-[9px] text-[rgba(255,255,255,0.4)] text-right not-italic flex_center ${
                                value === item.bgColor ? 'border-[4px] border-[#0E63FF] border-solid' : 'border-[#EEEEEE]'
                              }`}
                              style={{
                                backgroundColor: item.bgColor
                              }}
                            >
                              背景色
                            </div>
                            {item.colorList.map((color, j) => (
                              <div
                                key={j}
                                style={{ backgroundColor: color }}
                                onClick={() => onChange(color, 0)}
                                className={`cursor-pointer anim_btn w-[25px] h-[25px] border rounded-[4px] border-solid box-border ${
                                  value === color ? 'border-[4px] border-[#0E63FF] border-solid' : 'border-[#EEEEEE]'
                                }`}
                              ></div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </>
      ) : null}
    </AnimatePresence>
  )
}
