import { useNavigate, useSearchParams } from 'react-router-dom'
import { useEffect, useRef, useState } from 'react'
import '@/utils/fabric-eraser-brush.js'
import Modal from '@/components/Modal'
import 'react-circular-progressbar/dist/styles.css'
import vipImg from '@/assets/images/vip/vip.png'
import ret2Img from '@/assets/images/vip/ret.png'
import bgImg from '@/assets/images/vip/bg.png'
import zfbImg from '@/assets/images/vip/zfb.png'
import wxImg from '@/assets/images/vip/wx.png'
import checkImg from '@/assets/images/vip/check.png'
import checkOutImg from '@/assets/images/vip/checkout.png'
import { getUserinfo } from '@/api/global'
import { useAsyncFn } from 'react-use'
import Spin from '@/components/Spin'
import { borwserEnv, throttle } from '@/utils/tool'
import { getOpenId, h5Alipay, perquisiteList, wechatJs } from '@/api/pay'
import { toast } from 'react-toastify'
import { weixinPay } from 'weixin-units'
import weixinLogin from '@/utils/weixinLogin'
import { useAtom } from 'jotai'
import { memberState, vipEndState } from '@/store/global'
import Div100vh from 'react-div-100vh'

const PayType = (() => {
  const type = [
    {
      name: '支付宝',
      img: zfbImg,
      value: 0
    },
    {
      name: '微信',
      img: wxImg,
      value: 1
    }
  ]
  if (borwserEnv.isWechat) {
    return type
  }
  return [type[0]]
})()

interface Package {
  templateCode: string
  originalPrice: number
  discountPrice: number
  title: string
}

export default function Index({
  mode
}: {
  // 0 默认 有到期提示
  // 1 无到期提示
  // 2 体验结束
  mode?: number
}) {
  const navigate = useNavigate()
  const [member, setMember] = useAtom(memberState)
  const [user, setUser] = useState<{
    avatar: string
    nickname: string
  } | null>(null)
  const [vipEnd, setVipEnd] = useAtom(vipEndState)
  const [check, setCheck] = useState(PayType[0].value)
  const [templateCode, setTemplateCode] = useState('')
  const [step, setStep] = useState(mode === 1 ? 1 : 0) // 0 会员订购 1 支付 2 支付成功
  const [searchParams] = useSearchParams()
  const openidRef = useRef(localStorage.openid || '')
  const [packageList, setPackageList] = useState<Package[]>([])

  useEffect(() => {
    perquisiteList().then((res) => {
      if (res.code !== 200) {
        toast(res.msg)
        return ''
      }
      setPackageList(res.data)
      setTemplateCode(res.data[0].templateCode)
    })
    getUserinfo().then((res) => {
      const member = res.data.member
      // 已登录是会员 && 已过期 && 不是1模式 (每次提示)
      if (member.memberFlag && member.expire && mode !== 1) {
        setVipEnd(true)
      }
      // 已登录是会员 && 未过期 && 剩余天数小于等于7 && 未提示 && 不是1模式 (只提示一次)
      if (member.memberFlag && !member.expire && member.remainDay <= 7 && !localStorage.VIPExpirationReminders && mode !== 1) {
        localStorage.VIPExpirationReminders = true
        setVipEnd(true)
      }
      setMember(member)
      setUser({
        avatar: res.data.avatar,
        nickname: res.data.nickname
      })
    })
    if (borwserEnv.isWechat && !openidRef.current) {
      if (searchParams.get('code')) {
        getOpenId({ wechatCode: searchParams.get('code') }).then((openidRes) => {
          console.log('openidRes', openidRes)
          if (openidRes.code !== 200) {
            toast(openidRes.msg)
            return ''
          }
          localStorage.openid = openidRes.data
          openidRef.current = openidRes.data
        })
      } else {
        weixinLogin({ appid: 'wx561e3b0b98ec9469', redirect: window.location.href })
      }
    }
  }, [])

  const [payData, payFetch] = useAsyncFn<() => Promise<any>>(async () => {
    toast('提交中…')
    try {
      if (check === 1) {
        if (borwserEnv.isWechat) {
          const orderRes = await wechatJs({
            channel: import.meta.env.VITE_APP_CHANNEL,
            templateCode,
            openId: openidRef.current,
            addressId: 0
          })
          console.log('orderRes', orderRes)
          if (orderRes.code !== 200) {
            toast(orderRes.msg)
            return ''
          }
          const wxPayres = await weixinPay({ ...JSON.parse(orderRes.data.resultData), appId: 'wx561e3b0b98ec9469' })
          console.log('wxPayres', wxPayres)
          navigate(0)
        } else {
          toast('请在微信中打开')
        }
      } else {
        // h5Alipay
        const orderRes = await h5Alipay({
          channel: import.meta.env.VITE_APP_CHANNEL,
          templateCode,
          addressId: 0,
          targetUrl: window.location.origin + window.location.pathname
        })
        console.log('orderRes', orderRes)
        if (orderRes.code !== 200) {
          toast(orderRes.msg)
          return ''
        }
        let url = orderRes.data.resultData
        console.log('url', url)
        window.location.href = url
      }
      return null
    } catch (error) {
      toast(error as string)
    }
  }, [check, templateCode])

  return (
    <Modal show={vipEnd} mode={2}>
      <Div100vh className="h-screen w-screen float-right relative flex justify-end">
        <div
          className="w-full h-full absolute z-0 left-0 top-0"
          onClick={() => {
            member?.expire ? '' : setVipEnd(false)
          }}
        ></div>
        <div className="h-full w-[440px] z-10 rounded-[20px_0px_0px_20px] bg-white mr-[-40px] overflow-hidden">
          <div className="h-full w-[400px]">
            {/* 提醒 */}
            {step === 0 ? (
              <div className="mx-[28px] mt-[32px]">
                <div className="h-[24px]">
                  <img
                    className="anim_btn cursor-pointer w-[25px] h-[24px]"
                    src={ret2Img}
                    alt=""
                    onClick={() => {
                      if (member?.expire) {
                        navigate('/')
                        setVipEnd(false)
                      } else setVipEnd(false)
                    }}
                  />
                  <div className="font-medium text-[16px] text-[#272D53]"></div>
                </div>
                <div className="flex_center flex-col mt-[79px]">
                  <img className="w-[100px] h-[115px]" src={vipImg} alt="" />
                  <div className="mt-[12px] font-medium text-[20px] text-[#272D53]">
                    {mode === 2 ? '免费AI绘画体验已结束' : member?.expire ? '您的会员权益已到期' : `您的会员权益${member?.remainDay}天后到期`}
                  </div>
                  <div className="mt-[12px] font-normal text-[16px] text-[#636880]">{mode === 2 ? '购买会员，抒发无尽创意' : '立即续费，抒发无尽创意'}</div>
                  <div className="mt-[80px] flex justify-between w-full">
                    {member?.expire || mode === 2 ? (
                      <div
                        className="cancel_btn anim_btn mr-[12px] cursor-pointer w-[120px] h-[52px] rounded-[26px] font-medium text-[16px] text-[#272D53] flex_center"
                        onClick={() => {
                          setVipEnd(false)
                          navigate('/')
                        }}
                      >
                        退出创作
                      </div>
                    ) : null}
                    <div
                      className="theme_btn anim_btn cursor-pointer flex-1 h-[52px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] rounded-[26px] font-medium text-[16px] text-white flex_center"
                      onClick={() => {
                        setStep(1)
                      }}
                    >
                      {mode === 2 ? '购买会员' : '立即续费'}
                    </div>
                  </div>
                </div>
              </div>
            ) : null}
            {step === 1 ? (
              <div className="flex flex-col h-full">
                {/* 会员订购 */}
                <div className="h-[180px] w-full relative flex flex-col">
                  <img className="w-full h-full absolute left-0 top-0 z-0" src={bgImg} alt="" />
                  <div className="h-[24px] flex_center mx-[28px] mt-[32px] z-10">
                    <img
                      className="anim_btn cursor-pointer w-[25px] h-[24px]"
                      src={ret2Img}
                      alt=""
                      onClick={() => {
                        mode === 1 ? setVipEnd(false) : setStep(0)
                      }}
                    />
                    <div className="flex-1 flex_center font-medium text-[16px] text-[#272D53]">会员订阅</div>
                    <div className="w-[25px] h-[24px]"></div>
                  </div>
                  <div className="z-10 mt-[9px] h-[80px] flex items-center mx-[28px]">
                    <img className="w-[50px] h-[50px]" src={user?.avatar} alt="" />
                    <div className="ml-[16px] flex-1">
                      <div className="font-medium text-[16px] text-[#272D53] leading-[23px]">{user?.nickname}</div>
                      <div className="mt-[8px] font-normal text-[12px] text-[#636880] leading-[12px]">{member?.memberFlag ? `会员有效期：${member?.expireTime}` : member?.expireTime}</div>
                    </div>
                    <img className="w-[70px] h-[80px]" src={vipImg} />
                  </div>
                </div>
                <div className="flex-1 z-20 mt-[-27px] rounded-[20px_20px_0_0] bg-white">
                  <div className="pt-[24px] mx-[28px] h-full relative">
                    <div className="font-medium text-[16px] text-[#272D53]">会员套餐</div>
                    <div className="flex justify-between mt-[16px]">
                      {packageList.map((item, index) => (
                        <div
                          key={index}
                          onClick={() => {
                            setTemplateCode(item.templateCode)
                          }}
                          className={`cursor-pointer w-[108px] h-[120px] border rounded-[10px] border-solid flex flex-col items-center ${
                            templateCode === item.templateCode ? 'buy_card border-[#F09761]' : 'border-[#CCCCCC]'
                          }`}
                        >
                          <div key={index} className="font-normal text-[16px] text-[#272D53] mt-[20px] leading-[23px]">
                            {item.title}
                          </div>
                          <div className="font-[bold] text-[16px] text-[#272D53] mt-[4px] leading-[33px]">
                            <span>¥</span>
                            <span className="text-[28px]">{item.discountPrice}</span>
                          </div>
                          <div className="font-normal text-[12px] text-[#C37343] line-through mt-[4px] leading-[17px]">原价¥{item.originalPrice}</div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-[40px] w-[344px] rounded-[15px] bg-[#f8f8f8] px-[16px]">
                      {PayType.map((item, index) => (
                        <div key={index} onClick={() => setCheck(item.value)} className="h-[60px] flex items-center border-b-[1px] boder-[#00000060] last:border-none">
                          <img className="w-[28px] h-[28px]" src={item.img} alt="" />
                          <div className="ml-[8px] flex-1 font-normal text-[16px] text-[#272D53] leading-[23px]">{item.name}</div>
                          <img className="w-[20px] h-[20px] cursor-pointer anim_btn" src={check === item.value ? checkImg : checkOutImg} alt="" />
                        </div>
                      ))}
                    </div>
                    <div className="w-full absolute bottom-[28px] h-[52px]">
                      <div className="w-[344px] h-[52px] rounded-[26px] overflow-hidden relative">
                        <Spin
                          state={payData.loading}
                          className="anim_btn theme_btn w-[344px] h-[52px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] rounded-[26px] font-medium text-[16px] text-white flex_center"
                          onClick={throttle(payFetch, 300)}
                        >
                          确认并支付
                        </Spin>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : null}
          </div>
        </div>
      </Div100vh>
    </Modal>
  )
}
