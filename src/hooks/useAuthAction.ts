import { loginModalState, userState } from '@/store/global'
import { useAtomValue, useSetAtom } from 'jotai'
import { useCallback } from 'react'

const useAuthAction = (actionFn: () => any) => {
  const setLoginModal = useSetAtom(loginModalState)
  const user = useAtomValue(userState)

  const handleClick = useCallback(() => {
    console.log(user)
    if (user) {
      actionFn()
    } else {
      console.log('未登录')
      setLoginModal(true)
    }
  }, [user, actionFn])

  return handleClick
}

export default useAuthAction
