@import 'tailwindcss/base.css';
@import 'tailwindcss/components.css';
@import 'tailwindcss/utilities.css';

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .anim_btn {
    -webkit-tap-highlight-color: transparent;
    @apply active:scale-90 transition cursor-pointer;
  }

  .flex_center {
    @apply flex justify-center items-center;
  }
}

.toast-container {
  /* width: auto !important; */
  min-width: 200px !important;
}

.toast-class {
  padding: 4px !important;
  min-height: 60px !important;
}

.toast-class .Toastify__close-button > svg {
  width: 21px;
  height: 24px;
}

.toast-body-class .Toastify__toast-icon {
  width: 20px;
  margin-inline-end: 5px;
}

.skeleton {
  background-image: linear-gradient(90deg, #fff 25%, #f2f2f2 37%, #fff 63%);
  list-style: none;
  background-size: 400% 100%;
  background-position: 100% 50%;
  animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0 50%;
  }
}

/* 取消按钮渐变 */
.cancel_btn {
  background: linear-gradient(180deg, #ffffff 0%, #f2f2f2 100%);
  border: 1px solid #9b9b9b;
}

/* 蓝色按钮渐变 */
.theme_btn {
  background: linear-gradient(180deg, #8381e7 0%, #5c62ea 100%);
}

/* 购买卡片渐变 */
.buy_card {
  background: linear-gradient(115deg, #fff5ee 0%, #ffffff 52%, #fff5ee 100%);
}

/* 头像边框渐变 */
.gradient_style {
  background: linear-gradient(0deg, #ffefd2 0%, #dbaf64 100%);
}

.darw_bg {
  background-image: url('@/assets/images/board/bg.png');
}

.share_screen_bg {
  background-image: url('@/assets/images/board/shareScreenBg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

/* 兼容 */
.touch_callout {
  -webkit-touch-callout: none;
}

/* 兼容 */
.blur-glass-effect {
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.5);
  /* padding: 20px; */
  color: #fff;
}

.bp5-popover-arrow:before {
  @apply h-[20px] w-[20px] shadow-[1px_1px_6px_rgba(17,20,24,0.2)] content-[""] block absolute rotate-45 m-[5px] rounded-[1px];
}

.bg-linear {
  background: linear-gradient(180deg, #f7fbfe 0%, #f1f5f9 100%);
}

.drag {
  cursor: move;
  -webkit-app-region: drag;
}
.home {
  background-image: url('@/assets/images/newBoard/home.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.ai-pen {
  background-image: url('@/assets/images/newBoard/AIpen.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.ai-next {
  background-image: url('@/assets/images/newBoard/next.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.ai-prev {
  background-image: url('@/assets/images/newBoard/prev.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.ai-eraser {
  background-image: url('@/assets/images/newBoard/eraser.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

// 水墨山河
.play-theme-1 {
  position: relative;
  background-image: url('@/assets/images/display/bg_1.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;

  .frame {
    position: absolute;
    top: 24px;
    left: 214px;
    width: 1300px;
    height: 1035px;
    background-image: url('@/assets/images/display/cover_1.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
  }
  .board {
    position: absolute;
    top: 152px;
    left: 232px;
  }
}
// 非遗剪纸
.play-theme-2 {
  position: relative;
  background-image: url('@/assets/images/display/bg_2.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  .frame {
    position: absolute;
    top: 162px;
    left: 436px;
    width: 1020px;
    height: 776px;
    background-image: url('@/assets/images/display/cover_2.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
  }
  .board {
    position: absolute;
    top: 23px;
    left: 23px;
  }
}
// 敦煌飞天
.play-theme-3 {
  position: relative;
  background-image: url('@/assets/images/display/bg_3.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  .frame {
    position: absolute;
    top: 86px;
    left: 304px;
    width: 1238px;
    height: 938px;
    background-image: url('@/assets/images/display/cover_3.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
  }
  .board {
    position: absolute;
    top: 94px;
    left: 131px;
  }
}
// 多彩花卉
.play-theme-4 {
  position: relative;
  background-image: url('@/assets/images/display/bg_4.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  .frame {
    position: absolute;
    top: 38px;
    left: 300px;
    width: 1250px;
    height: 1016px;
    background-image: url('@/assets/images/display/cover_4.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
  }

  .board {
    position: absolute;
    top: 139px;
    left: 142px;
  }
}

.drag-icon-1 {
  background-image: url('@/assets/images/newBoard/here_1.png');
  background-repeat: no-repeat;
  background-position: center;
  pointer-events: none;
}
.drag-icon-2 {
  background-image: url('@/assets/images/newBoard/here_2.png');
  background-repeat: no-repeat;
  background-position: center;
  pointer-events: none;
}
.drag-icon-3 {
  background-image: url('@/assets/images/newBoard/here_3.png');
  background-repeat: no-repeat;
  background-position: center;
  pointer-events: none;
}
.drag-icon-4 {
  background-image: url('@/assets/images/newBoard/here_4.png');
  background-repeat: no-repeat;
  background-position: center;
  pointer-events: none;
}

.setting-content {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(auto-fit, minmax(422px, 1fr));
}
.setting-bg {
  background-image: url('@/assets/images/display/set_bg.jpg');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.sbml-ai {
  background-image: url('@/assets/images/display/ml_ai.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.currentStyle {
  background: linear-gradient(180deg, #666666 0%, rgba(255, 255, 255, 0) 100%);
  div {
    background: linear-gradient(330deg, #0052d4 0%, #4364f7 39%, #6fb1fc 100%);
  }
}

.qrcode-bg {
  background-image: url('@/assets/images/display/qrcode_bg.png');
  background-repeat: no-repeat;
  background-position: center;
}
