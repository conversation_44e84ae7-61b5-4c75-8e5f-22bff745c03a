import { useEffect, useRef, useState } from 'react'
import { Popup } from 'react-vant'
import FixedLeft from '@/components/FixedLeft'
import Pay from './pay'
import RegistrationInfo from './registrationInfo'
import './index.css'
import activation_codeImg from './act.webp'
import AcButton from '@/components/AcButton'
import MyRegistration from '@/components/MyRegistration'
import CertificatePage from '@/pages/certificate'
import MyWorks from './myWorks'
import { getCompetitionInfo, postRegistration } from '@/api/competition'
import { toast } from 'react-toastify'

type ApplyView = 'apply' | 'registerInfo' | 'myWorks' | 'myRegistration' | 'certificate' | ''
export interface WorkItem {
  id: number
  title: string
  userImag?: string
  generateImage: string
  prompt: string
  styleId: number
  styleName: string
  recommendFlag: number
  userShowBean: {
    avatar: string
    nickName: string
  }
}
enum PayStatus {
  SUCCESS = 1,
  FAIL = 2
}
export default function Apply() {
  // 支付状态
  const [isPay, setIsPay] = useState(false)
  // 支付弹窗状态
  const [needPay, setNeedPay] = useState(false)

  const [registerInfo, setRegisterInfo] = useState(false)
  const [applyModalVisible, setApplyModalVisible] = useState(false)
  const [registerInfoModalVisible, setRegisterInfoVisible] = useState(false)
  const [myWorksModalVisible, setMyWorksVisible] = useState(false)
  const [myRegistrationModalVisible, setMyRegistrationVisible] = useState(false)
  const [curView, setCurView] = useState<ApplyView>('')
  const [userRegisterInfo, setUserRegisterInfo] = useState<any>({})
  // 在Apply组件顶部添加状态
  const [selectedWorkItem, setSelectedWorkItem] = useState<WorkItem>()
  const [formData, setFormData] = useState<any>({})
  const [isRegistered, setIsRegistered] = useState(false)

  const showApplyModal = () => {
    setCurView('apply')
    setApplyModalVisible(true)
  }
  const showRegisterInfoModal = () => {
    setCurView('registerInfo')
    setRegisterInfoVisible(true)
  }
  const showMyWorksModal = () => {
    setCurView('myWorks')
    setMyWorksVisible(true)
  }
  const showMyRegistrationModal = () => {
    setCurView('myRegistration')
    setMyRegistrationVisible(true)
  }
  const myRegistrationModal = () => (
    <FixedLeft
      modelVisible={myRegistrationModalVisible}
      title="我的报名"
      hasReturnBtn={!isRegistered}
      onClose={() => {
        setMyRegistrationVisible(false)
        if (!isRegistered) {
          showApplyModal()
        }
      }}
    >
      <MyRegistration userRegisterInfo={userRegisterInfo} />
    </FixedLeft>
  )
  // 进入页面时判断是否支付过，没有支付过则弹出支付弹窗,支付过则显示报名页面
  // useEffect(() => {
  //   // api判断是否支付过 todo
  //   getCompetitionInfo().then((res) => {
  //     console.log(res, 'res')
  //     if (res.code === 200) {
  //       setUserRegisterInfo(res.data)
  //       if (res.data.payStatus === PayStatus.SUCCESS) {
  //         setIsPay(true)
  //       }
  //       console.log(res.data.registrationBean.checkStatus, 'res.data.registrationBean.checkStatus')
  //       setIsRegistered(!!res.data.registrationBean.checkStatus)
  //     }
  //   })
  // }, [])
  useEffect(() => {
    console.log(isPay, 'isPay')
    if (localStorage.token) {
      const urlParams = new URLSearchParams(window.location.search)
      getUserCompetitionInfo().then(() => {
        if (urlParams.get('modalIsShow')) {
          onClickHandler()
        } else if (isPay && urlParams.toString()) {
          setNeedPay(false)
          console.log(isRegistered, 'isRegistered')

          if (isRegistered) {
            showMyRegistrationModal()
          } else {
            showApplyModal()
          }
        }
      })
    }

    // if (isPay && urlParams.get('total_amount')) {
    //   setNeedPay(false)

    //   if (isRegistered) {
    //     showMyRegistrationModal()
    //   } else {
    //     showApplyModal()
    //   }
    // }
  }, [isPay])

  const onClickHandler = async () => {
    // 打开弹窗
    console.log(localStorage.token)

    if (localStorage.token) {
      try {
        const res = await getCompetitionInfo()
        console.log(res, 'res')

        if (res.code === 200) {
          setUserRegisterInfo(res.data)
          if (res.data.payStatus === PayStatus.SUCCESS) {
            setIsPay(true)
          }
          console.log(res.data.registrationBean.checkStatus, !!res.data.registrationBean.checkStatus, 'res.data.registrationBean.checkStatus')
          const curIsRegistered = !!res.data.registrationBean.checkStatus
          setIsRegistered(curIsRegistered)
          console.log('isRegistered', isRegistered)
          if (curIsRegistered) {
            showMyRegistrationModal()
          } else {
            showApplyModal()
          }
        }
      } catch (error: any) {
        console.error('获取信息失败', error.message)
      }
      return
    }
    console.log('isRegistered', isRegistered)

    if (isRegistered) {
      showMyRegistrationModal()
    } else {
      showApplyModal()
    }
  }
  const applyBtn = () => {
    return (
      <AcButton className="mb-[20px]" onClick={onClickHandler}>
        查看详情
      </AcButton>
    )
  }
  const getUserCompetitionInfo = async () => {
    // api获取用户信息
    try {
      const res = await getCompetitionInfo()
      console.log(res, 'res')

      if (res.code === 200) {
        setUserRegisterInfo(res.data)
        if (res.data.payStatus === PayStatus.SUCCESS) {
          setIsPay(true)
        }
        console.log(res.data.registrationBean.checkStatus, !!res.data.registrationBean.checkStatus, 'res.data.registrationBean.checkStatus')
        setIsRegistered(!!res.data.registrationBean.checkStatus)
        console.log('isRegistered', isRegistered)
      }
    } catch (error: any) {
      console.error('获取信息失败', error.message)
    }
  }
  const onBtnClickHandler = async () => {
    // 支付过，跳转到报名信息填写
    console.log('isPay', isPay)
    if (isPay) {
      // 跳转到报名页面，表单填写
      showRegisterInfoModal()
    } else {
      await getUserCompetitionInfo()
      setNeedPay(true)
    }
  }
  const applyModal = () => {
    const onApplyClose = () => {
      // 关闭弹窗
      setApplyModalVisible(false)
    }
    return (
      <FixedLeft title="参赛信息" modelVisible={applyModalVisible} onClose={onApplyClose}>
        <div className="apply-modal-content">
          <AcButton className="fixed bottom-5" onClick={onBtnClickHandler}>
            {isPay ? '立即报名' : '购买获得参赛资格'}
          </AcButton>
          <img src={activation_codeImg} alt="activation_code" className="apply-modal-content-img" />
        </div>
        <Popup visible={needPay} style={{ height: '30%' }} position="bottom" onClose={() => setNeedPay(false)}>
          <Pay productTemplateCode={userRegisterInfo.productTemplateCode} />
        </Popup>
      </FixedLeft>
    )
  }
  const registerInfoModal = () => {
    const onRegisterInfoModalClose = () => {
      // 关闭弹窗
      setRegisterInfoVisible(false)
      showApplyModal()
    }
    const onFormFinish = (data: any) => {
      // 表单提交
      console.log('表单提交')
      const postData = {
        ...data,
        competitionId: userRegisterInfo.competition.competitionId
        // id: userRegisterInfo.registrationBean.workId
      }

      postRegistration(postData)
        .then((res) => {
          console.log(res, 'res')

          if (res.code === 200) {
            toast('报名成功')
            // 改变支付状态
            setIsRegistered(true)
            getUserCompetitionInfo().then(() => {
              showMyRegistrationModal()
            })
          }
        })
        .catch((err) => {
          toast(err.msg)
          console.log('报名失败', err)
        })
      // setMyRegistrationVisible(true)
      // setCurView('myRegistration')
      // 提交成功后跳转证书视图
      // setCurView('certificate')
      // 关闭当前弹窗
      // setRegisterInfoVisible(false)
    }

    return (
      <FixedLeft
        modelVisible={registerInfoModalVisible}
        title={registerInfo ? '报名页' : '赛事详情'}
        backgroundColor="#f3f3f3"
        onClose={() => {
          setFormData({})
          setSelectedWorkItem(undefined)
          onRegisterInfoModalClose()
        }}
      >
        <RegistrationInfo
          initialData={formData}
          onDataChange={setFormData}
          onFormFinish={onFormFinish}
          uploadMyWorks={showMyWorksModal}
          selectedWork={selectedWorkItem}
          onDeleteWorkItem={() => setSelectedWorkItem(undefined)}
        ></RegistrationInfo>
      </FixedLeft>
    )
  }

  // 修改myWorksModal部分
  const myWorksModal = () => {
    const onBack = () => {
      setMyWorksVisible(false)
      showRegisterInfoModal()
    }
    return (
      <FixedLeft hasReturnBtn hasCloseBtn={false} modelVisible={myWorksModalVisible} title="我的作品" onBack={onBack}>
        <MyWorks
          onSelect={(e) => {
            setFormData({
              ...formData,
              myWorks: [
                {
                  url: e?.generateImage
                }
              ]
            })
            setSelectedWorkItem(e)
            onBack()
          }} // 新增props
          selectedWorkItem={selectedWorkItem} // 新增props
        />
      </FixedLeft>
    )
  }
  const currentViewComponent = () => {
    switch (curView) {
      case 'apply':
        return applyModal()
      case 'registerInfo':
        return registerInfoModal()
      case 'myWorks':
        return myWorksModal()
      case 'myRegistration':
        return myRegistrationModal()
      case 'certificate':
        return <CertificatePage />
      default:
    }
  }
  return (
    <div className="w-full flex justify-center items-end apply-banner h-[100svh] darw_bg">
      {applyBtn()}
      {currentViewComponent()}
    </div>
  )
}
