import { sleep } from '@/utils/tool'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useAsyncFn } from 'react-use'
import { userWorksList } from '@/api/board'
import { WorkItem } from '.'
import { Loading } from 'react-vant'
interface Props {
  onSelect: (workItem?: WorkItem) => void // 新增props
  selectedWorkItem?: WorkItem // 新增props
}
export default function MyWorks({ onSelect, selectedWorkItem }: Props) {
  const [page, setPage] = useState(1)
  const [isDone, setIsDone] = useState(false)
  const [worksList, setWorksList] = useState<WorkItem[]>([])

  useEffect(() => {
    worksItemFetch()
  }, [page])

  // 我的作品 todo api中获取我的作品
  const [worksItemData, worksItemFetch] = useAsyncFn<() => Promise<{ worksList: WorkItem[]; hasNest: boolean }>>(async () => {
    await sleep(500)
    const res = await userWorksList(page)
    if (res.code !== 200) {
      toast(res.msg)
      return ''
    }
    if (!res.data.hasNest) {
      setIsDone(true)
    }
    if (page === 1) {
      setWorksList(res.data.worksList)
    } else {
      setWorksList((v) => [...v, ...res.data.worksList])
    }
    return res.data
  }, [page])

  // 新增选中切换逻辑
  const toggleSelection = (workItem: WorkItem) => {
    onSelect(workItem) // 同步选择状态到父组件 并且返回上个组件
  }

  const isSameWorks = (workItem: WorkItem) => {
    return workItem.id === selectedWorkItem?.id // 比较id是否相同
  }
  return (
    <div className="grid grid-cols-2 gap-8 p-8">
      {worksItemData.loading && worksList.length === 0 && (
        <div className="col-span-2 h-[80vh] flex items-center justify-center">
          <Loading type="spinner" color="#3f45ff" size="24px">
            加载中...
          </Loading>
        </div>
      )}

      {/* 添加空状态占位 */}
      {!worksItemData.loading && worksList.length === 0 && (
        <div className="col-span-2 flex flex-col items-center justify-center h-[60vh] gap-4">
          <div className="w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-20 h-20 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
          <p className="text-gray-500 text-sm">暂无作品，快去创作吧~</p>
        </div>
      )}
      {worksList.map((item) => (
        <div
          key={item.id}
          className={`group relative overflow-hidden rounded-[20px] shadow-lg transition-all duration-300 hover:scale-105
        ${isSameWorks(item) ? 'ring-4 ring-green-400 ring-opacity-80' : ''}`}
          onClick={() => toggleSelection(item)}
        >
          <div className="aspect-square bg-gray-100 relative">
            <img src={item.generateImage} alt="作品" className="h-full w-full object-cover" />
          </div>
          {/* 选中图标 */}
          {isSameWorks(item) && (
            <div className="absolute top-1 right-1 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
              <div className="text-white text-sm">✓</div>
            </div>
          )}
          {/* 图片底部遮罩层 */}
          <div className="absolute bottom-0 left-0 right-0 h-[42px] bg-black/60 flex items-center px-4 gap-3 backdrop-blur-sm">
            {/* 头像 */}
            <img src={item.userShowBean.avatar} className="w-[28px] h-[28px] rounded-full object-cover" alt="用户头像" />
            {/* 作品名称 */}
            <span className="text-white text-sm font-medium truncate flex-1 text-left">{item.userShowBean.nickName}</span>
          </div>
        </div>
      ))}
      {/* 加载更多指示器 */}
      {!worksItemData.loading && !isDone ? (
        <div className="col-span-2 flex justify-center py-4">
          {' '}
          <div
            onClick={() => setPage((v) => v + 1)}
            className="text-[#3F51B5] px-6 py-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors cursor-pointer text-sm font-medium shadow-sm hover:shadow-md"
          >
            加载更多
          </div>
        </div>
      ) : null}
    </div>
  )
}
