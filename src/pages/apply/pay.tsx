import { borwserEnv, throttle } from '@/utils/tool'
import { useEffect, useRef, useState } from 'react'
import checkImg from '@/assets/images/vip/check.png'
import checkOutImg from '@/assets/images/vip/checkout.png'
import zfbImg from '@/assets/images/vip/zfb.png'
import wxImg from '@/assets/images/vip/wx.png'
import { useAsyncFn } from 'react-use'
import { toast } from 'react-toastify'
import { getOpenId, h5Alipay, perquisiteList, wechatJs } from '@/api/pay'
import { useNavigate } from 'react-router-dom'
import { weixinPay } from 'weixin-units'
import { Checkbox } from 'react-vant'
import Spin from '@/components/Spin'
const PayType = (() => {
  const type = [
    {
      name: '支付宝',
      img: zfbImg,
      value: 0
    },
    {
      name: '微信',
      img: wxImg,
      value: 1
    }
  ]
  if (borwserEnv.isWechat) {
    return type
  }
  return [type[0]]
})()
const Pay = (props: any) => {
  const navigate = useNavigate()
  const [check, setCheck] = useState(PayType[0].value)
  const [checkboxChecked, setCheckboxChecked] = useState(false)

  const [templateCode, setTemplateCode] = useState('')
  const openidRef = useRef(localStorage.openid || '')

  useEffect(() => {
    setTemplateCode(props.productTemplateCode)

    // if (borwserEnv.isWechat && !openidRef.current) {
    //   if (searchParams.get('code')) {
    //     getOpenId({ wechatCode: searchParams.get('code') }).then((openidRes) => {
    //       console.log('openidRes', openidRes)
    //       if (openidRes.code !== 200) {
    //         toast(openidRes.msg)
    //         return ''
    //       }
    //       localStorage.openid = openidRes.data
    //       openidRef.current = openidRes.data
    //     })
    //   } else {
    //     weixinLogin({ appid: 'wx561e3b0b98ec9469', redirect: window.location.href })
    //   }
    // }
  }, [])

  const checkboxRef = useRef(checkboxChecked)
  checkboxRef.current = checkboxChecked
  const [payData, payFetch] = useAsyncFn<() => Promise<any>>(async () => {
    if (!checkboxRef.current) {
      toast('请确认同意以下协议')
      return
    }
    toast('提交中…')
    try {
      if (check === 1) {
        if (borwserEnv.isWechat) {
          const orderRes = await wechatJs({
            channel: import.meta.env.VITE_APP_CHANNEL,
            templateCode,
            openId: openidRef.current,
            addressId: 0
          })
          console.log('orderRes', orderRes)
          if (orderRes.code !== 200) {
            toast(orderRes.msg)
            return ''
          }
          const wxPayres = await weixinPay({ ...JSON.parse(orderRes.data.resultData), appId: 'wx561e3b0b98ec9469' })
          console.log('wxPayres', wxPayres)
          navigate(0)
        } else {
          toast('请在微信中打开')
        }
      } else {
        // h5Alipay
        const orderRes = await h5Alipay({
          channel: import.meta.env.VITE_APP_CHANNEL,
          templateCode,
          addressId: 0,
          targetUrl: window.location.origin + window.location.pathname
        })
        console.log('orderRes', orderRes)
        if (orderRes.code !== 200) {
          toast(orderRes.msg)
          return ''
        }
        let url = orderRes.data.resultData
        window.location.href = url
      }
      return null
    } catch (error) {
      toast(error as string)
    }
  }, [check, templateCode])
  const onCheckboxChangeHandler = (checked: boolean) => {
    setCheckboxChecked(checked)
  }
  return (
    <div className="flex justify-center items-center">
      <div className="mt-[40px] w-[344px] rounded-[15px] bg-[#f8f8f8] px-[16px]">
        {PayType.map((item, index) => (
          <div key={index} onClick={() => setCheck(item.value)} className="h-[60px] flex items-center border-b-[1px] boder-[#00000060] last:border-none">
            <img className="w-[28px] h-[28px]" src={item.img} alt="" />
            <div className="ml-[8px] flex-1 font-normal text-[16px] text-[#272D53] leading-[23px]">{item.name}</div>
            <img className="w-[20px] h-[20px] cursor-pointer anim_btn" src={check === item.value ? checkImg : checkOutImg} alt="" />
          </div>
        ))}
      </div>
      <div className="w-full absolute bottom-[30px]  flex  flex-col justify-center items-center">
        <div className="w-[344px] h-[52px] mb-[20px] rounded-[26px] overflow-hidden relative">
          <Spin
            state={payData.loading}
            className="anim_btn theme_btn w-[344px] h-[52px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] rounded-[26px] font-medium text-[16px] text-white flex_center"
            onClick={throttle(payFetch, 300)}
          >
            确认协议并支付
          </Spin>
        </div>
        <Checkbox checked={checkboxChecked} onChange={onCheckboxChangeHandler}>
          <div className="text-[#999999] text-[12px] ">同意 《会员服务协议》 《隐私协议》</div>
        </Checkbox>
      </div>
    </div>
  )
}

export default Pay
