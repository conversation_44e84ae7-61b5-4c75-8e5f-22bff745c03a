import { Input, Uploader, Form, Picker, DatetimePicker, Area } from 'react-vant'
import AcFormItem from '@/components/AcFormItem'
import AcButton from '@/components/AcButton/index'
import { toast } from 'react-toastify'

import { areaList } from '@vant/area-data'
import './index.css'
import AuthAgreementModal from '@/components/AuthAgreementModal'
import { useEffect, useState } from 'react'
import { WorkItem } from './index'
import uploadClient from '@/utils/upload'
import uploadImg from '@/pages/apply/upload.webp'
interface Props {
  children?: React.ReactNode
  initialData?: any
  selectedWork?: WorkItem
  containerRef?: any
  onDataChange?: (data: any) => void
  onFormFinish: (data: any) => void
  uploadMyWorks: () => void
  onDeleteWorkItem?: () => void
}

const registrationColumns = [
  { text: '个人', value: 1 },
  { text: '机构', value: 2 }
]
const genderColumns = [
  { text: '男', value: 1 },
  { text: '女', value: 2 }
]
const registrationGroupColumns = [
  { text: '少儿组', value: 1 },
  { text: '少年组', value: 2 }
]
const getProvinceCode = () => {
  const res = []
  for (const key in areaList.province_list) {
    res.push({
      text: areaList.province_list[key],
      value: key
    })
  }
  return res
}
const provinceColumns = getProvinceCode()
export default function RegistrationInfo({ onFormFinish, uploadMyWorks, onDeleteWorkItem, onDataChange, containerRef, initialData, selectedWork }: Props) {
  const [form] = Form.useForm()
  const [showAuth, setShowAuth] = useState(false)
  const [registrationData, setRegistrationData] = useState<any>({
    name: '',
    idCard: '',
    gender: '',
    birthday: '',
    provinceCode: '',
    cityCode: '',
    countyCode: '',
    address: '',
    phone: '',
    registrationType: 1,
    registrationGroup: 1,
    myWorks: [{}]
  })
  useEffect(() => {
    if (selectedWork) {
      form.setFieldsValue({
        myWorks: [{ url: selectedWork.generateImage }]
      })
    }
  }, [selectedWork])
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData)
    }
  }, [initialData])

  const delFromData = (values: any) => {
    const { myWorks, ...restValues } = values
    const registrationData = {
      ...restValues,
      birthday: values.birthday.getTime(),
      workId: selectedWork?.id,
      workTitle: selectedWork?.title,
      provinceName: provinceColumns.find((item) => item.value === values.provinceCode)?.text
    }
    setRegistrationData(registrationData)
    console.log('registrationData', registrationData)
  }

  const onUploaderChange = (e: any) => {
    e.preventDefault()
    uploadMyWorks()
  }
  const handleSubmit = () => {
    form
      .validateFields()
      .then((res) => {
        delFromData(res)
        setShowAuth(true)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  const deleteSelectWorkItem = () => {
    form.setFieldsValue({ myWorks: [] }) // 清空表单字段
    onDeleteWorkItem?.() // 通知父组件清空选中状态
  }

  const onUploaderIdCard = async (file: File) => {
    const imgres = await uploadClient.fileUpload([file])
    form.setFieldsValue({ idCard: imgres[0].url })
    return { url: imgres[0].url }
  }

  const onDeleteIdCard = () => {
    form.setFieldsValue({ idCard: '' })
  }
  console.log(containerRef, 'containerRef')

  return (
    <>
      <AuthAgreementModal
        visible={showAuth}
        containerRef={containerRef}
        onAgree={() => {
          setShowAuth(false)
          onFormFinish(registrationData)
        }}
        onCancel={() => setShowAuth(false)}
      />
      <Form
        colon
        form={form}
        onValuesChange={() => {
          onDataChange?.(form.getFieldsValue())
        }}
        footer={
          <div className="flex justify-center m-4">
            <AcButton onClick={handleSubmit}>提交信息</AcButton>
          </div>
        }
      >
        <AcFormItem className="idCard" name="idCard" label="证件照" desc="请上传1寸证件照，尺寸1MB-5MB之间" rules={[{ required: true, message: '请上传证件照' }]}>
          <div className="flex">
            <Uploader
              showUpload={true}
              maxCount={1}
              className="mt-2"
              maxSize={5 * 1024 * 1024} // 添加5MB限制
              onOversize={() => toast('文件大小不能超过5MB')}
              upload={onUploaderIdCard}
              onDelete={onDeleteIdCard}
            >
              <img className="w-[45px] h-[45px]" src={uploadImg} alt="上传" />
            </Uploader>
          </div>
        </AcFormItem>
        <AcFormItem rules={[{ required: true, message: '请填写姓名' }]} name="name" label="姓名">
          <Input placeholder="与身份证姓名保持一致" />
        </AcFormItem>
        <AcFormItem
          rules={[{ required: true, message: '请填写姓名' }]}
          isLink
          trigger="onConfirm"
          onClick={(_, action) => {
            action?.current?.open()
          }}
          name="gender"
          label="性别"
        >
          <Picker popup columns={genderColumns}>
            {(val) => {
              const selected = genderColumns.find((item) => item.value === +val)
              return selected ? selected.text : '请选择性别'
            }}
          </Picker>
        </AcFormItem>
        <AcFormItem
          isLink
          name="birthday"
          label="出生日期"
          trigger="onConfirm"
          onClick={(_, action) => {
            action?.current?.open()
          }}
          rules={[{ required: true, message: '请选择日期' }]}
        >
          <DatetimePicker popup type="date" minDate={new Date(1990, 0, 1)} maxDate={new Date()}>
            {(val: Date) => (val ? `${val.getFullYear()}年${val.getMonth() + 1}月${val.getDate()}日` : '请选择日期')}
          </DatetimePicker>
        </AcFormItem>
        <AcFormItem
          isLink
          name="provinceCode"
          label="省份"
          rules={[{ required: true, message: '请填写省份' }]}
          trigger="onConfirm"
          onClick={(_, action) => {
            action?.current?.open()
          }}
        >
          <Picker popup columns={provinceColumns}>
            {(val) => {
              const selected = provinceColumns.find((item) => item.value === val)
              return selected ? selected.text : '请选择省份'
            }}
          </Picker>
        </AcFormItem>
        <AcFormItem
          rules={[{ required: true, message: '请填写参赛方式' }]}
          isLink
          trigger="onConfirm"
          onClick={(_, action) => {
            action?.current?.open()
          }}
          name="registrationType"
          label="参赛方式"
        >
          <Picker popup columns={registrationColumns}>
            {(val) => {
              const selected = registrationColumns.find((item) => item.value === +val)
              return selected ? selected.text : '请选择参赛方式'
            }}
          </Picker>
        </AcFormItem>
        {form.getFieldValue('registrationType') === 2 && (
          <AcFormItem rules={[{ required: true, message: '请填写机构名称' }]} name="agencyName" label="机构名称">
            <Input placeholder="请输入所属机构" />
          </AcFormItem>
        )}
        <AcFormItem rules={[{ required: true, message: '请填写收件人姓名' }]} name="receiveUserName" label="收件人姓名">
          <Input placeholder="请输入姓名" />
        </AcFormItem>
        <AcFormItem
          rules={[
            { required: true, message: '请填写手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
          ]}
          name="receivePhone"
          label="收件人手机号"
        >
          <Input placeholder="请输入11位手机号" />
        </AcFormItem>
        <AcFormItem
          rules={[
            { required: true, message: '请填写地址' },
            { max: 100, message: '地址长度不能超过100字符' }
          ]}
          name="receiveAddress"
          label="收件地址"
        >
          <Input placeholder="请输入收件地址" />
        </AcFormItem>
        <AcFormItem
          rules={[{ required: true, message: '请填写参赛组别' }]}
          isLink
          trigger="onConfirm"
          onClick={(_, action) => {
            action?.current?.open()
          }}
          name="registrationGroup"
          label="参赛组别"
        >
          <Picker popup columns={registrationGroupColumns}>
            {(val) => {
              const selected = registrationGroupColumns.find((item) => item.value === +val)
              return selected ? selected.text : '请选择参赛组别'
            }}
          </Picker>
        </AcFormItem>
        <AcFormItem className="idCard" name="myWorks" label="选择参赛作品" rules={[{ required: true, message: '请上传参赛作品' }]}>
          <Uploader maxCount={1} value={selectedWork ? [{ url: selectedWork.generateImage }] : []} onDelete={deleteSelectWorkItem} onClickUpload={onUploaderChange}>
            <img className="w-[45px] h-[45px]" src={uploadImg} alt="上传" />
          </Uploader>
        </AcFormItem>
        <AcFormItem name="description" label="创作说明" rules={[{ required: true, message: '请填写创作说明' }]}>
          <Input.TextArea rows={3} autoSize maxLength={140} showWordLimit />
        </AcFormItem>
      </Form>
    </>
  )
}
