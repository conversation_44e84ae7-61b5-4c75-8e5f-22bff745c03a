import { useNavigate, useSearchParams } from 'react-router-dom'
import retImg from '@/assets/images/board/ret.png'
import prevImg from '@/assets/images/board/prev.png'
import nextImg from '@/assets/images/board/next.png'
import pictureImg from '@/assets/images/board/picture.png'
import imgImg from '@/assets/images/board/img.png'
import downloadImg from '@/assets/images/board/download.png'
import clearImg from '@/assets/images/board/clear.png'
import close2Img from '@/assets/images/board/close2.png'
import delImg from '@/assets/images/board/del.png'
import descImg from '@/assets/images/board/desc.png'
import examImg from '@/assets/images/board/exam.png'
import { useAtom } from 'jotai'
import { useCallback, useEffect, useRef, useState } from 'react'
import { fabric } from 'fabric'
import '@/utils/fabric-eraser-brush.js'
import useGetState from '@/hooks/useGetState'
import { throttle, debounce, sleep, dataURLToFile, getBase64Info, base64ToFile } from '@/utils/tool'
import { useAsyncFn, useToggle, useUpdateEffect } from 'react-use'
import Modal, { defAnimate, maskAnimate } from '@/components/Modal'
import ImgPlaceholder from '@/components/ImgPlaceholder'
import styleCheckImg from '@/assets/images/board/style-check.png'
import styleImg from '@/assets/images/board/style.png'
import Div100vh from 'react-div-100vh'
import uploadClient from '@/utils/upload'
import { toast } from 'react-toastify'
import { aiStyle, audio2word, img2img, saveWorks, translate, worksDetail, getDrawId } from '@/api/board'
import MaskLoading from '@/components/MaskLoading'
import loadingGif from '@/assets/images/global/loading.png'
import jianImg from '@/assets/images/board/jian.png'
import Recorder from 'recorder-core/recorder.mp3.min'
import Spin from '@/components/Spin'
import waveGif from '@/assets/images/board/wave.gif'
import modal3Img from '@/assets/images/board/modal3.png'
import pen1Img from '@/assets/images/board/pen1.png'
import pigmentImg from '@/assets/images/board/pigment.png'
import iconImg from '@/assets/images/board/icon.png'
import { AnimatePresence, motion } from 'framer-motion'
import md5 from 'md5'
import useAuthAction from '@/hooks/useAuthAction'
import { WorksDetail } from '../works'
import Swatches from '@/components/Swatches'
import { examSumit } from '@/api/board'
import 'react-circular-progressbar/dist/styles.css'
import { getUUID } from '@/utils/tool'
import { vipEndState } from '@/store/global'
import { PiMicrophone } from 'react-icons/pi'
import vipImg from '@/assets/images/vip/vip.png'
import ret2Img from '@/assets/images/vip/ret.png'
import Vip from '@/components/Vip'

interface Prompt {
  md5: string
  chineseContent: string
  englishContent: string
}

interface Style {
  id: number
  icon: string
  title: string
}

export default function Index() {
  const navigate = useNavigate()
  const recorder = useRef<any>(null)
  const toastId = useRef<any>(null)
  const controller = useRef<AbortController | null>(null) // 中断请求
  const lastUserImage = useRef('') // 上一次用户图片
  const lastEnglishContent = useRef('') // 上一次描述词
  const [canRecording, setCanRecording] = useState(false) // 录音状态
  const descriptionInput = useRef<HTMLInputElement>(null) // 描述词输入框
  const [promptModal, setPromptModal] = useToggle(false) // 描述词弹窗
  const [inputText, setInputText] = useState('') // 输入框文字
  const canvasRef = useRef(null) // 画板dom
  const parentRef = useRef<HTMLDivElement>(null) // 画板父容器
  const fileInput = useRef<HTMLInputElement>(null) // 选择图片dom
  const captureInput = useRef<HTMLInputElement>(null) // 拍照dom
  const canvasClass = useRef<fabric.Canvas | null>(null) // 画板实体
  const ctxRef = useRef<CanvasRenderingContext2D | null>(null) // 画板实体
  const [originalDimensions, setOriginalDimensions, getOriginalDimensions] = useGetState({ width: 0, height: 0 }) // 缩放画板上一次的尺寸
  const [history, setHistory, getHistory] = useGetState<string[]>([`{"version":"5.3.0","objects":[]}`]) // 画板步骤记录器
  const [currentStep, setCurrentStep, getCurrentStep] = useGetState(0) // 当前步骤
  const [color, setColor, getColor] = useGetState<string>('#f8531b') // 画笔颜色
  const [drawColor, setDrawColor, getDrawColor] = useGetState<string>('#f8531b') // 画笔颜色
  const [colorShow, setColorShow] = useToggle(false) // 画笔颜色弹窗
  const [bgColor, setBgColor, getBgColor] = useGetState<string>('#FFFFFF') // 画板背景颜色
  const [bgColorShow, setBgColorShow] = useToggle(false) // 画板背景颜色弹窗
  const [chooseStyleState, setChooseStyleState] = useToggle(false) // 选择风格弹窗状态
  const [style, setStyle, getStyle] = useGetState<{ current: Style | null; choose: Style | null; old: Style | null }>({
    current: null,
    choose: null,
    old: null
  }) // 风格
  const [prompt, setPrompt, getPrompt] = useGetState<Prompt[]>([]) // 描述
  const [generateImg, setGenerateImg] = useState<string>('') // 生成图片
  const [voice, setVoice] = useState({
    base64: '',
    duration: 0,
    audioType: 'wav'
  }) // 录音
  const [searchParams] = useSearchParams()
  const imgToImgQueuing = useRef(false) // 队列(图生图必须等上一次完成才请求)
  const generateFetchState = useRef(false) // 生成图片loading状态
  const [examStatus, setExamStatus] = useState(false)
  const [tool, setTool, getTool] = useGetState(0) // 画笔工具 0 画笔 1 油漆桶 2 背景 3 橡皮 4 清空 5 拍照 6 相册 7 提交 8 录制 9 上一步 10 下一步 11 色卡
  const lastPenColor = useRef('#F5222D') // 上一次画笔颜色
  const worksId = useRef<any>(null)

  const [isRecording, setIsRecording] = useState(true)
  const [isVideo, setIsVideo] = useState(true) // 录制结束弹窗,false为显示
  const [videoURL, setVideoURL] = useState('')
  const mediaRecorderRef = useRef<any>(null)
  const [chunks, setChunks] = useState<Blob[]>([])
  const progress = useRef<any>(0)
  const [uploadSatus, setUploadSatus] = useState('start')
  const [isPaused, setIsPaused] = useState<boolean>(false)
  const [uploadTask, setUploadTask] = useState<any>(null) // 上
  const videoFile = useRef<any>(null)
  const drawId = useRef<any>(null)
  const [experienceEnd, setExperienceEnd] = useState(false)
  const [vipEnd, setVipEnd] = useAtom(vipEndState)
  // 0 默认 有到期提示
  // 1 无到期提示
  // 2 体验结束
  const [mode, setMode] = useState(0)
  const microphonePermission = useRef(false)
  const [microphonePermissionLoading, setMicrophonePermissionLoading] = useState(false)

  // 作品详情
  const [worksDetailState, worksDetailFetch] = useAsyncFn<() => Promise<WorksDetail>>(async () => {
    await sleep(500)
    const res = await worksDetail(searchParams.get('id'))
    if (res.code !== 200) {
      toast(res.msg)
      return
    }
    setGenerateImg(res.data.generateImage)
    addImg(res.data.userImage, false)
    return { ...res.data, prompt: JSON.parse(res.data.prompt) }
  }, [])

  // 获取样式
  const [styleState, styleFetch] = useAsyncFn<() => Promise<Style[]>>(async () => {
    const res = await aiStyle()
    if (res.code !== 200) {
      toast(res.msg)
      return
    }
    setStyle({
      current: res.data[0],
      choose: res.data[0],
      old: res.data[0]
    })
    return res.data
  }, [])

  useEffect(() => {
    if (!chooseStyleState) {
      if (style.current?.id !== style.old?.id) {
        setStyle({ ...style, old: style.current })
        if (history.length > 1) {
          debounceHandlePathCreated()
        }
      }
    }
  }, [chooseStyleState])
  useEffect(() => {
    getDrawId().then((res) => {
      if (res.code == 200) {
        drawId.current = res.data
      }
    })
  }, [])
  // canvas 导出 base64,上传 oss 后调接口图生图
  const [handlePathCreatedState, handlePathCreatedFetch] = useAsyncFn<() => Promise<string>>(async () => {
    try {
      const canvas = canvasClass.current
      if (!canvas) return
      canvas.renderAll()
      const dataURL = canvas.toDataURL({
        format: 'png',
        quality: 0.8
      })
      // console.log('dataURL', dataURL)

      const imgInfo = await getBase64Info(dataURL)
      // console.log(imgInfo)

      const imgfile = dataURLToFile(dataURL, 'image.png')
      // console.log(imgfile)
      
      const imgres = await uploadClient.fileUpload([imgfile])
      // console.log(imgres)
      lastUserImage.current = imgres[0].url
      const res = await img2img({
        styleId: getStyle().current?.id,
        imageUrl: lastUserImage.current,
        prompt: getPrompt()
          .map((v) => v.englishContent)
          .join(','),
        width: imgInfo.width,
        height: imgInfo.height,
        uniqueId: drawId.current
      })
      controller.current === null
      console.log(res)
      // 体验结束,每次生成都弹窗提示
      if (res.code === 577) {
        // toast(res.msg)
        setMode(2)
        setVipEnd(true)
        return
      }
      // 会员过期,弹窗会在vip里面执行,这里不做处理
      if (res.code === 578) {
        toast(res.msg)
        return
      }
      // 如果队列标识为true,则继续调用生成图
      if (imgToImgQueuing.current) {
        imgToImgQueuing.current = false
        handlePathCreatedFetch()
      } else {
        setGenerateImg(res.data.imageUrl)
      }
      return res.data.imageUrl
    } catch (error) {
      // console.log(error)
    }
  }, [])

  useEffect(() => {
    generateFetchState.current = handlePathCreatedState.loading
  }, [handlePathCreatedState])

  // 画板 canvas 导出 base64 防抖处理 排队处理
  const debounceHandlePathCreated = useCallback(
    debounce(() => {
      // 图生图loading状态，将队列标识设为true，等待上一次完成后继续调用生成图
      if (generateFetchState.current) {
        imgToImgQueuing.current = true
      } else {
        handlePathCreatedFetch()
      }
    }, 300),
    []
  )

  // 点击画板位置
  const handleCanvasClick = (event: { e: Event }) => {
    const tool = getTool()
    // console.log('tool', tool)
    if (tool === 1) {
      if (!canvasClass.current) {
        return
      }
      const pointer = canvasClass.current.getPointer(event.e)
      const x = pointer.x
      const y = pointer.y

      // console.log('点击位置 x：', x, ' y：', y)

      // 执行油漆桶算法
      floodFillNew(x, y, [255, 255, 0, 255]) // 填充红色
    } else if (tool === 2) {
      const canvas = canvasClass.current
      if (canvas) {
        canvas.setBackgroundColor(getColor(), canvas.renderAll.bind(canvas))
        debounceHandlePathCreated()
      }
    }
  }

  // 画板 canvas 导出 base64 防抖处理 排队处理
  const debounceHandleCanvasClick = useCallback(debounce(handleCanvasClick, 300), [])

  // 油漆桶算法
  const floodFillNew = (x: number, y: number, fillColor: any[]) => {
    if (!canvasClass.current || !ctxRef.current) {
      return
    }
    const w = canvasClass.current.width || 800
    const h = canvasClass.current.height || 600
    const imageData = ctxRef.current.getImageData(0, 0, w * devicePixelRatio, h * devicePixelRatio)
    // console.log('高', h, '宽', w, '数据长度', x, y, imageData.data.length)
    const data = imageData.data
    floodFillScanline(data, Math.round(x), Math.round(y), w, h, fillColor)
  }

  // 扫描线填充算法
  const floodFillScanline = (data: any[] | Uint8ClampedArray, xx: number, yy: number, width: number, height: number, newColor: any[]) => {
    // console.log('floodFillScanline', tool)
    if (!canvasClass.current) {
      return
    }
    const oldColor = getColorAtPixel(data, xx, yy, width)
    // console.log(data, xx, yy, width, height, newColor)
    // console.log('oldColor', oldColor)
    // console.log(colorsMatch(newColor, oldColor))
    if (oldColor[0] === undefined || oldColor[1] === undefined || oldColor[2] === undefined || oldColor[3] === undefined) {
      return
    }
    if (colorsMatch(newColor, oldColor)) return
    const stack = [[xx, yy]]
    let x1: number
    let spanAbove: boolean
    let spanBelow: boolean
    // console.log('floodFillScanline')
    // let count: number = 0
    // let lineCount: number = 0
    while (stack.length) {
      let xStart: number

      // @ts-ignore
      const [x, y] = stack.pop()
      x1 = x
      while (x1 > 0 && colorsMatch(getColorAtPixel(data, x1, y, width), oldColor)) x1--
      x1++
      xStart = x1
      spanAbove = false
      spanBelow = false
      while (x1 < width && colorsMatch(getColorAtPixel(data, x1, y, width), oldColor)) {
        setColorAtPixel(data, x1, y, width, newColor)
        if (!spanAbove && y > 0 && colorsMatch(getColorAtPixel(data, x1, y - 1, width), oldColor)) {
          // console.log('A1', x1, y - 1)
          //这里判断出了上行的元素可以被染色，可能为了修改screen的访存连续性，所以这里没修改。而且你改了上行的值，却没考虑其四周，会有完备性的问题。
          stack.push([x1, y - 1])
          spanAbove = true
        } else if (spanAbove && y > -1 && !colorsMatch(getColorAtPixel(data, x1, y - 1, width), oldColor)) {
          spanAbove = false //不压入重复过多的元素
        }
        if (!spanBelow && y < height - 1 && colorsMatch(getColorAtPixel(data, x1, y + 1, width), oldColor)) {
          // console.log('A2', x1, y + 1)
          stack.push([x1, y + 1])
          spanBelow = true
        } else if (spanBelow && y < height - 2 && !colorsMatch(getColorAtPixel(data, x1, y + 1, width), oldColor)) {
          spanBelow = false
        }
        x1++
        // count++
      }
      // console.log(xStart)
      // console.log('floodFillScanline', xStart, x1, y)

      const line1 = new fabric.Line([xStart - 1, y, x1 + 1, y], {
        stroke: getDrawColor(),
        strokeWidth: 1
      })
      canvasClass.current.add(line1)
      // lineCount++
    }
  }

  // 获取像素颜色
  const getColorAtPixel = (data: any[] | Uint8ClampedArray, x: number, y: number, width: number) => {
    const index = Math.round((y * (width * devicePixelRatio) + x) * 4 * devicePixelRatio)
    return [data[index], data[index + 1], data[index + 2], data[index + 3]]
  }

  // 设置像素颜色
  const setColorAtPixel = (data: any[] | Uint8ClampedArray, x: number, y: number, width: number, color: any[]) => {
    const index = Math.round((y * (width * devicePixelRatio) + x) * 4 * devicePixelRatio)
    data[index] = color[0]
    data[index + 1] = color[1]
    data[index + 2] = color[2]
    data[index + 3] = color[3]
  }

  // 对比是否颜色相同
  const colorsMatch = (color1: any[], color2: any[]) => {
    return color1[0] === color2[0] && color1[1] === color2[1] && color1[2] === color2[2] && color1[3] === color2[3]
    // return (color1[0] || color1[1] || color1[2])
  }

  // 画板初始化
  useEffect(() => {
    document.addEventListener(
      'touchmove',
      function (event) {
        if (event.touches.length > 1) {
          event.preventDefault()
        }
      },
      { passive: false }
    )
    // 获取风格
    styleFetch()

    // 创建画板
    const fabricCanvas = new fabric.Canvas(canvasRef.current, { isDrawingMode: true })
    fabricCanvas.freeDrawingBrush.color = drawColor
    fabricCanvas.freeDrawingBrush.width = 8
    canvasClass.current = fabricCanvas

    ctxRef.current = fabricCanvas.getContext()

    const parentElement = parentRef.current
    if (parentElement) {
      const initialWidth = parentElement.clientWidth
      const initialHeight = parentElement.clientHeight
      setOriginalDimensions({ width: initialWidth, height: initialHeight })

      fabricCanvas.setWidth(initialWidth)
      fabricCanvas.setHeight(initialHeight)
    }

    // 画板画一笔调用生成图监听
    fabricCanvas.on('path:created', debounceHandlePathCreated)
    // 画板步骤监听
    fabricCanvas.on('mouse:up', () => saveState(fabricCanvas))

    // 监听画布点击事件
    fabricCanvas.on('mouse:down', debounceHandleCanvasClick)
    // 画板缩放
    const resizeCanvas = () => {
      const parentElement = parentRef.current
      const getDimensions = getOriginalDimensions()
      if (parentElement && getDimensions.width && getDimensions.height) {
        const scale = parentElement.clientWidth / getDimensions.width
        setOriginalDimensions({ width: parentElement.clientWidth, height: parentElement.clientHeight })
        fabricCanvas.setWidth(parentElement.clientWidth)
        fabricCanvas.setHeight(parentElement.clientHeight)
        // console.log(scale, parentElement.clientWidth, parentElement.clientHeight)
        fabricCanvas.getObjects().forEach((obj) => {
          obj.scaleX = obj.scaleX! * scale
          obj.scaleY = obj.scaleY! * scale
          obj.left = obj.left! * scale
          obj.top = obj.top! * scale
          obj.setCoords()
        })

        fabricCanvas.renderAll()
      }
    }
    // 画板缩放防抖处理
    const throttleResizeCanvas = throttle(resizeCanvas, 300) // 300ms debounce delay
    // 监听窗口变化
    window.addEventListener('resize', throttleResizeCanvas)

    // 回显
    if (searchParams.get('id')) {
      worksDetailFetch()
    }

    return () => {
      window.removeEventListener('resize', throttleResizeCanvas)
      fabricCanvas.off('path:created', debounceHandlePathCreated) // Remove event listener
      // fabric-with-erasing 有bug,销毁就报错
      try {
        fabricCanvas.dispose()
      } catch (error) {}
    }
  }, [])

  // 画笔颜色
  useEffect(() => {
    const canvas = canvasClass.current
    if (canvas) {
      if (tool === 3) {
        // @ts-ignore
        canvas.freeDrawingBrush = new fabric.EraserBrush(canvas)
        canvas.freeDrawingBrush.width = 20
        // setTool(3)
      } else if (tool === 1 || tool === 2) {
        canvas.freeDrawingBrush.width = 0
      } else {
        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas)
        canvas.freeDrawingBrush.color = drawColor
        // @ts-ignore
        // canvas.freeDrawingBrush.limitedToCanvasSize = true
        canvas.freeDrawingBrush.width = 10
        lastPenColor.current = drawColor
        // setTool(0)
      }
    }
  }, [drawColor, tool])

  // 画板背景颜色
  useUpdateEffect(() => {
    const canvas = canvasClass.current
    if (canvas) {
      canvas.setBackgroundColor(bgColor, canvas.renderAll.bind(canvas))
      debounceHandlePathCreated()
    }
  }, [bgColor])

  // 描述词变化掉图生图,至少有一个笔画才调用生图
  useUpdateEffect(() => {
    const englishContent = prompt.map((v) => v.englishContent).join(',')
    if (englishContent !== lastEnglishContent.current && lastUserImage.current) {
      debounceHandlePathCreated()
      lastEnglishContent.current = englishContent
    }
  }, [prompt])

  // 画板步骤记录
  const saveState = (canvas: fabric.Canvas) => {
    const history = getHistory()
    const currentStep = getCurrentStep()
    const state: string = JSON.stringify(canvas.toJSON())
    const historyCopy = history.slice(0, currentStep + 1)
    historyCopy.push(state)
    setHistory(historyCopy)
    setCurrentStep(currentStep + 1)
  }

  // 画板步骤回退
  const historyState = (index: number) => {
    const canvas = canvasClass.current
    if (!canvas) return
    canvas.loadFromJSON(history[index], () => {
      canvas.renderAll()
      setCurrentStep(index)
      debounceHandlePathCreated()
    })
  }

  // H5选择图片
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files![0]
    if (!file) return
    // 创建一个指向文件的URL
    const fileUrl = URL.createObjectURL(file)
    // 使用fabric.Image.fromURL来加载图片
    addImg(fileUrl)
  }

  // 考试
  const startRecording = useAuthAction(async () => {
    if (!handlePathCreatedState.value) {
      toast('还未生成图片,继续绘画吧')
      return
    }
    setIsVideo(false)
    // 屏幕录制暂时隐藏
    // if (user) {
    //   if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
    //     setExamStatus(true)
    //     return
    //   }
    //   const stream = await navigator.mediaDevices.getDisplayMedia({
    //     video: true,
    //     audio: true
    //   })

    //   // 检查流是否有效
    //   if (!stream || stream.getTracks().length === 0) {
    console.error('未能获取到有效的媒体流')
    //     return
    //   }
    //   mediaRecorderRef.current = new MediaRecorder(stream)
    //   mediaRecorderRef.current?.start(2000)
    //   // 监听音视频轨道的结束事件
    //   stream.getTracks().forEach((track) => {
    //     track.onended = () => {
    // console.log('共享已结束，自动停止录制')
    //       mediaRecorderRef.current?.stop()
    //       setIsRecording(false)
    //     }
    //   })
    //   mediaRecorderRef.current.ondataavailable = (event: any) => {
    //     setIsRecording(false)
    //     if (event.data.size > 0) {
    //       setChunks((prev) => [...prev, event.data])
    //     }
    //   }
    //   setIsRecording(true)
    // }
  })

  const addImg = (fileUrl: string, create = true) => {
    // console.log(fileUrl, create)
    fabric.Image.fromURL(
      fileUrl,
      (img) => {
        const canvas = canvasClass.current
        // 确保canvas已经挂载
        if (!canvas) return
        // 清理之前的图片（如果有的话）
        if (canvas) {
          canvas.clear()
        }
        // 设置图片属性并添加到canvas
        const w = canvas.width || 0
        const h = canvas.height || 0
        const iw = img.width || 0
        const ih = img.height || 0
        // 计算缩放比例以适应canvas
        const scaleW = w / iw
        const scaleH = h / ih
        let scale = Math.min(scaleW, scaleH) // 选择较小的比例以确保图片完全在canvas内
        // 设置图片的尺寸
        img.scale(scale).set({
          left: (w - iw * scale) / 2, // 居中
          top: (h - ih * scale) / 2 // 居中
        })
        // 添加到canvas
        canvas.add(img)
        // 渲染canvas
        // canvas.renderAll()
        if (create) {
          debounceHandlePathCreated()
          saveState(canvas)
        }
      },
      { crossOrigin: 'Anonymous' }
    )
  }

  // 描述词change
  const inputChange = (e: any) => {
    // if (prompt.join('').length + e.target.value.length <= 200) {
    setInputText(e.target.value)
    // } else {
    // Taro.showToast({
    //   title: `仅支持输入200字`,
    //   icon: "none",
    // });
    // }
  }

  // 描述词确认
  const descriptionAdd = (e: any, type = 'onKeyDown') => {
    // console.log(e.keyCode, e)
    // 按下回车
    if (e.keyCode === 13 || type === 'onBlur') {
      // if (e.target.value.trim().length + prompt.join('').length > 200) {
      // e.stopPropagation();
      // return
      // }
      if (e.target.value.trim() === '') {
        // if (changeIndex !== -1) {
        //   setChangeIndex(-1)
        //   descriptionInputFocus()
        //   if (type !== 'onBlur') {
        //     setDescription((v) => {
        //       v.splice(changeIndex, 1)
        // console.log(v)
        //       return v
        //     })
        //   }
        // }
        return
      }
      setInputText('')
      // if (changeIndex !== -1) {
      //   setDescription((v) => {
      //     v[changeIndex] = e.target.value.trim()
      //     return v
      //   })
      // } else {
      const text = e.target.value.trim()
      const md5Str = md5(text)
      const index = prompt.findIndex((item) => item.md5 === md5Str)
      if (index === -1) {
        translateFetch(md5Str, text)
        setPrompt((v) => {
          const newPrompt = [...v, { md5: md5Str, chineseContent: text, englishContent: '' }]
          return newPrompt
        })
      } else {
        setPrompt([...prompt, { chineseContent: text, englishContent: prompt[index].englishContent, md5: md5Str }])
      }
      // }
      // setChangeIndex(-1)
      descriptionInputFocus()
    }
    // 按下退格删除
    if (e.keyCode === 8) {
      if (e.target.value.trim() === '') {
        setPrompt(prompt.slice(0, prompt.length - 1))
      }
    }
  }

  // 描述词输入框聚焦
  const descriptionInputFocus = () => {
    // setTimeout(() => {
    descriptionInput.current?.focus()
    // }, 500);
  }

  // 录音
  const microphoneClick = async (state: boolean) => {
    // if (prompt.join('').length >= 200) {
    //   Taro.showToast({
    //     title: `仅支持输入200字`,
    //     icon: 'none'
    //   })
    //   return
    // }

    // 安卓、H5 录音
    if (state) {
      // setCanRecording(true)
      if (!recorder.current) {
        recorder.current = Recorder({
          type: 'mp3',
          sampleRate: 16000,
          bitRate: 16
        })
      }
      try {
        if (microphonePermissionLoading) {
          return
        }
        setMicrophonePermissionLoading(true)
        console.log('microphonePermission.current 1', microphonePermission.current)
        if(microphonePermission.current) {
          setCanRecording(true)
        }
        recorder.current.open(
          () => {
            console.log('microphonePermission.current 2', microphonePermission.current)
            if (!microphonePermission.current) {
              microphonePermission.current = true
              recorder.current.close()
            } else {
              recorder.current.start()
              setCanRecording(true)
            }
            setMicrophonePermissionLoading(false)
          },
          (msg: any, isUserNotAllow: any) => {
            console.log('getPermission', msg, isUserNotAllow, microphonePermission.current)
            // H5 只能在https或localhost下使用麦克风
            // console.log('H5 只能在https或localhost下使用麦克风', window.location.origin)
            toast.dismiss(toastId.current)
            if (isUserNotAllow) {
              toastId.current = toast('请先允许该网页使用麦克风')
            } else {
              microphonePermission.current = true
            }
            recorder.current.close()
            setMicrophonePermissionLoading(false)
          }
        )
      } catch (error) {
        console.log(error)
        recorder.current.close()
        setCanRecording(false)
        microphonePermission.current = false
        setMicrophonePermissionLoading(false)
      }
    } else {
      recorder.current.stop(
        (blob: Blob, duration: number) => {
          const reader = new FileReader()
          reader.onloadend = () => {
            const base64 = reader.result as string

            // test play
            // const localUrl = (window.URL || webkitURL).createObjectURL(blob);
            // const audio = document.createElement("audio");
            // document.body.prepend(audio);
            // audio.controls = true;
            // audio.src = localUrl;
            // audio.play();

            // console.log('H5录音:', reader.result, '时长:' + duration + 'ms')
            setVoice({ base64, duration, audioType: 'mp3' })
            setCanRecording(false)
          }
          reader.readAsDataURL(blob)
          recorder.current.close()
        },
        () => {
          // console.log(code)
          // Taro.showToast({
          //   title: "录音失败:" + msg,
          //   icon: "none",
          // });
          // toast('录音失败:' + msg)
          recorder.current.close() //可以通过stop方法的第3个参数来自动调用close
          setCanRecording(false)
        }
      )
    }
  }

  // 删除描述词
  const delDescription = (e: any, i: number) => {
    e.stopPropagation()
    setPrompt(prompt.filter((_, index) => index !== i))
  }

  // 录音转文字
  useEffect(() => {
    // console.log(voice)
    if (voice.base64) {
      voiceFetch(voice)
    }
  }, [voice])

  // 翻译
  const [translateState, translateFetch] = useAsyncFn(async (md5, text) => {
    const res = await translate({ text, from: 'zh', to: 'en' })
    // console.log(res)
    if (res.code !== 200) {
      return
    }
    setPrompt((v) => {
      const newPrompt = v.map((item) => {
        if (item.md5 === md5) {
          item.englishContent = res.data
        }
        return item
      })
      return newPrompt
    })
    return
  }, [])

  // 录音转文字 & 文字转词组
  const [voiceState, voiceFetch] = useAsyncFn(async (data) => {
    const audioFile = await base64ToFile(data.base64, '1.mp3')
    // console.log(audioFile)

    const audioRes = await uploadClient.fileUpload([audioFile])
    // console.log(audioRes)

    const res = await audio2word({
      audioUrl: audioRes[0].url
    })
    // console.log(res)
    if (res.code !== 200) {
      return ''
    }
    if (!res.data.chineseContent) {
      // Taro.showToast({
      //   title: `没有识别到语音内容,请重新录制`,
      //   icon: "none",
      // });
      toast('没有识别到语音内容,请重新录制')
      return
    }
    // setPrompt((v) => {
    //   const all = [...v, res.data.chineseContent]
    //   // all 删除数组中的字符加起来超过200个的字符
    //   let count = 0
    //   let newStr: string[] = []
    //   all.forEach((item) => {
    //     count += item.length
    //     if (count <= 200) {
    //       newStr.push(item)
    //     } else {
    //       const remain = 200 - (count - item.length)
    //       newStr.push(item.slice(0, remain))
    //     }
    //   })
    //   return newStr
    // })
    setPrompt((v) => [...v, { md5: md5(res.data.chineseContent), chineseContent: res.data.chineseContent, englishContent: res.data.englishContent }])
    return res.data
  }, [])

  // 保存图片
  const [generateState, generateFetch] = useAsyncFn(async () => {
    if (!handlePathCreatedState.value) {
      toast('还未生成图片,继续绘画吧')
      return
    }
    const res = await saveWorks({
      // title: '',
      userImage: lastUserImage.current,
      generateImage: handlePathCreatedState.value,
      prompt: JSON.stringify(prompt),
      styleId: style.current?.id,
      uniqueId: drawId.current
    })
    if (res.code !== 200) {
      toast(res.msg)
      return
    }
    worksId.current = res.data
    if (res.data) {
      examSumit({
        worksId: res.data,
        videoUrl: videoURL
      })
    }
    navigate(`/works?id=${res.data}`)
    return
  }, [handlePathCreatedState, prompt, style])

  // 保存按钮校验是否登录
  const generateFetchFix = useAuthAction(generateFetch)
  const fileUpload = async (files: File[]) => {
    const { OSSClient, preUrl, cdnUrl } = await uploadClient.getOSSClient()
    const promises = files.map(async (file) => {
      const uuid = getUUID()
      const filePath = `${preUrl}/${uuid}/${file.name}`
      try {
        let percentNum = 0
        const task = await OSSClient.multipartUpload(filePath, file, {
          progress: (percent) => {
            percentNum = percent * 100
          }
        })
        setUploadTask(task)

        return {
          name: file.name,
          url: `${cdnUrl}/${filePath}`,
          percent: percentNum
        }
      } catch (error) {
        console.error(`Failed to upload file: ${file.name}`, error)
        throw error
      }
    })
    return await Promise.all(promises)
  }
  const cancelUpload = async () => {
    if (uploadTask) {
      await uploadTask.abort()
      // console.log('上传已取消')
    }
  }

  const pauseUpload = () => {
    if (uploadTask) {
      uploadTask.pause()
      // console.log('上传已暂停')
    }
  }

  const resumeUpload = async () => {
    if (uploadTask) {
      setIsPaused(false)
      // 这里需要重新开始上传，不能直接恢复
      await fileUpload(uploadTask.file) // 需要确保你有文件引用
      // console.log('上传已恢复')
    }
  }
  const examConfirm = () => {
    generateFetchFix()
    // setIsRecording(true)
  }
  const WorksHandle = (status: any, del: any) => {
    setUploadSatus(status)
    if (status == 'cannel') {
      pauseUpload()
    } else if (status == 'start') {
      resumeUpload()
    } else if (status == 'delete') {
      cancelUpload()
    } else if (status == 'isSupper') {
      setExamStatus(false)
    }
    // 删除作品
    if (del) {
      closeHandle()
    }
  }

  const closeHandle = () => {
    setIsVideo(true)
    setIsRecording(true)
    setTool(0)
  }
  const clearCanvas = () => {
    setTool(4)
    const canvas = canvasClass.current
    if (canvas) {
      canvas.clear()
      setGenerateImg('')
      setHistory([`{"version":"5.3.0","objects":[]}`])
      setCurrentStep(0)
    }
  }
  
  window.addEventListener('load', function () {
    var content = document.getElementById('content') as any
    content.addEventListener(
      'touchmove',
      function (event: any) {
        event.preventDefault()
      },
      false
    )
  })
  return (
    <div id="content" className="select-none fixed overflow-hidden touch-pan-x  top-0 left-0 right-0 w-full h-screen darw_bg bg-center bg-no-repeat bg-cover pt-[16px]">
      <div className="fixed w-full h-full flex flex-col items-center">
        <div className="bg-white flex items-center h-[84px] rounded-[10px] w-[1022px] mt-[20px] mx-auto">
          {/* 返回 */}
          <img onClick={() => navigate(localStorage.fromRouter || '/')} className="w-[53px] h-[53px] anim_btn ml-[16px]" src={retImg} alt="" />

          {/* 咒语 */}
          <img onClick={setPromptModal} className="w-[53px] h-[53px] ml-[42px]" src={descImg} alt="" />

          {/* 风格 */}
          <div
            onClick={setChooseStyleState}
            style={{
              backgroundImage: `url(${style.current?.icon})`,
              backgroundSize: 'cover'
            }}
            className="w-[53px] h-[53px] flex justify-center  bg-[#000000] text-[#fff] text-[8px] rounded-[12px] flex-col items-center relative ml-[8px] overflow-hidden"
          >
            <div className="w-[53px] h-[53px] z-20 whitespace-nowrap flex_center text-[8px] leading-none rounded-[5px] text-white bg-[#00000035] mt-[1px]">{style.current?.title}</div>
          </div>

          {/* 提交 */}
          <div className={`flex_center w-[53px] h-[53px] ml-[8px] ${tool == 7 ? 'w-[53px] rounded-[7px] bg-[#eff3f9]' : ''}`}>
            <img
              onClick={() => {
                startRecording()
                setTool(7)
              }}
              className="anim_btn w-[53px] h-[53px]"
              src={downloadImg}
              alt=""
            />
          </div>

          {/* 拍照 */}
          <div className={`${tool == 5 ? 'w-[53px] rounded-[13px] bg-[#F9F3FF]' : ''} flex_center w-[53px] h-[53px]`}>
            <div
              className="w-full h-full flex_center"
              onClick={() => {
                captureInput.current && captureInput.current.click()
                setTool(5)
              }}
            >
              <img className="anim_btn w-[38px] h-[38px]" src={pictureImg} alt="" />
            </div>
            <input onChange={handleFileChange} type="file" ref={captureInput} capture hidden />
          </div>

          {/* 相册 */}
          <div className={`flex_center w-[53px] h-[53px] ${tool == 6 ? 'w-[53px] rounded-[13px] bg-[#F9F3FF]' : ''}`}>
            <div
              className="w-full h-full flex_center"
              onClick={() => {
                fileInput.current && fileInput.current.click()
                setTool(6)
              }}
            >
              <img className="anim_btn w-[38px] h-[38px]" src={imgImg} alt="" />
            </div>
            <input onChange={handleFileChange} type="file" ref={fileInput} accept="image/*" hidden />
          </div>

          {/* 录制 */}
          <div className={`flex_center w-[53px] h-[53px] ${tool == 8 ? 'w-[53px] rounded-[13px] bg-[#FFF1F6]' : ''}`}>
            <div
              className="w-full h-full flex_center"
              onClick={() => {
                setTool(8)
                generateFetchFix()
              }}
            >
              <img className="anim_btn w-[38px] h-[38px]" src={examImg} alt="" />
            </div>
          </div>

          <div className="flex-1 h-full"></div>

          {/* box2 */}
          <div className="w-[106px] h-full flex_center">
            {/* 上一步 */}
            <div className={`flex_center w-[53px] h-[53px] ${tool == 9 ? 'w-[53px] rounded-[13px] bg-[#FFF8DC]' : ''}`}>
              <div
                className="w-full h-full flex_center"
                onClick={() => {
                  historyState(currentStep - 1)
                  setTool(9)
                }}
              >
                <img className="anim_btn w-[38px] h-[38px]" src={prevImg} alt="" />
              </div>
            </div>

            {/* 下一步 */}
            <div className={`flex_center w-[53px] h-[53px] ${tool == 10 ? 'w-[53px] rounded-[13px] bg-[#FFF8DC]' : ''}`}>
              <div
                className="w-full h-full flex_center"
                onClick={() => {
                  historyState(currentStep + 1)
                  setTool(10)
                }}
              >
                <img className="anim_btn w-[38px] h-[38px]" src={nextImg} alt="" />
              </div>
            </div>
          </div>

          <div className="w-[27px] flex_center h-full">
            <div className="w-[1px] h-[30px] bg-[#DDDDDD]"></div>
          </div>

          {/* box3 */}
          <div className="w-[265px] h-full flex_center mr-[16px]">
            {/* 画笔 */}
            <div className={`relative flex_center w-[53px] h-[53px] ${tool == 0 ? 'w-[53px] rounded-[13px] bg-[#EDFBE9]' : ''}`}>
              <div
                className="w-full h-full flex_center"
                onClick={() => {
                  // setDrawColor(lastPenColor.current)
                  // console.log(tool)
                  if (tool === 0) setColorShow()
                  setTool(0)
                  setBgColorShow(false)
                }}
              >
                <img className="anim_btn w-[38px] h-[38px]" src={pen1Img} alt="" />
              </div>
            </div>

            {/* 背景色 */}
            <div className={`relative flex_center w-[53px] h-[53px]  ${tool == 2 ? 'w-[53px] rounded-[13px] bg-[#EDF8FE]' : ''}`}>
              {/* <Swatches close={() => setBgColorShow(false)} show={bgColorShow} onChange={setBgColor} value={bgColor} /> */}
              <div
                className="w-full h-full flex_center"
                onClick={() => {
                  if (tool === 2) setBgColorShow()
                  setTool(2)
                  setColorShow(false)
                }}
              >
                <img className="anim_btn w-[38px] h-[38px]" src={pigmentImg} alt="" />
              </div>
            </div>

            {/* 油漆桶 */}
            {/* <div className={`flex_center w-[53px] h-[53px] ${tool == 1 ? 'w-[53px] rounded-[13px] bg-[#EDF8FE]' : ''}`}>
              <div
                className="w-full h-full flex_center"
                onClick={() => {
                  setTool(1)
                }}
              >
                <img className="anim_btn w-[38px] h-[38px]" src={paintImg} alt="" />
              </div>
            </div> */}

            {/* 橡皮 */}
            <div className={`flex_center w-[53px] h-[53px] ${tool == 3 ? 'w-[53px] rounded-[13px] bg-[#FFF8DC]' : ''}`}>
              <div
                className="w-full h-full flex_center"
                onClick={() => {
                  setTool(3)
                }}
              >
                <img className="anim_btn w-[38px] h-[38px]" src={delImg} alt="" />
              </div>
            </div>

            {/* 删除 */}
            <div className={`flex_center w-[53px] h-[53px] ${tool == 4 ? 'w-[53px] rounded-[13px] bg-[#EDF8FE]' : ''}`}>
              <div className="w-full h-full flex_center" onClick={clearCanvas}>
                <img className="anim_btn w-[38px] h-[38px]" src={clearImg} alt="" />
              </div>
            </div>

            {/* 色卡 */}
            <div className={`relative flex_center w-[53px] h-[53px] ${tool == 11 ? 'w-[53px] rounded-[13px] bg-[#F9F3FF]' : ''}`}>
              <Swatches
                close={colorShow ? () => setColorShow(false) : () => setBgColorShow(false)}
                show={colorShow || bgColorShow}
                onChange={(color, type) => {
                  setColor(color)
                  setDrawColor(color)
                  console.log(tool, type)
                  if ((tool === 0 && type !== 1) || (tool === 1 && type !== 1) || type === 0) {
                    // setDrawColor(color)
                    setTool(tool === 1 ? 1 : 0)
                  } else {
                    setTool(2)
                    setBgColor(color)
                  }
                }}
                value={color}
              />
              <div
                className="w-full h-full flex_center"
                onClick={() => {
                  if (tool === 2) {
                    setBgColorShow(true)
                  } else {
                    setColorShow(true)
                  }
                }}
              >
                <div
                  className="w-[25px] h-[25px] rounded-full shadow-[0_0_0_1px_rgba(17,20,24,0.1),0_2px_4px_rgba(17,20,24,0.1),0_8px_24px_rgba(17,20,24,0.2)]"
                  style={{ backgroundColor: color }}
                ></div>
                {/* <img className="anim_btn w-[38px] h-[38px]" src={colorImg} alt="" /> */}
              </div>
            </div>
          </div>
        </div>
        <div className="flex-1 flex items-center w-[1022px] justify-between">
          <div className="mb-[30px] w-[504px] h-[504px] bg-white rounded-[32px] flex_center">
            <div ref={parentRef} className="w-[490px] h-[490px] rounded-[32px] relative">
              {handlePathCreatedState.loading ? (
                <div className="w-full h-full left-0 top-0 absolute z-10 flex_center bg-[#00000035] rounded-[32px]">
                  <img className="w-[28px] h-[28px] animate-spin" src={loadingGif} />
                </div>
              ) : !generateImg ? (
                <div className="w-full h-full text-center bg-[#F7F6FB] rounded-[26px] flex_center flex-col box-border inline-block">
                  <div className="flex justify-center">
                    <img className="w-[85px] h-[63px]" src={jianImg} />
                  </div>
                  <div className="text-[#5567E7] text-[37px]">在右边画一画吧</div>
                </div>
              ) : null}
              {generateImg ? <img className="w-full h-full rounded-[26px]" src={generateImg} alt="" /> : null}
            </div>
          </div>
          <div className="mb-[30px] w-[504px] h-[504px] flex_center bg-white border-[#fff] rounded-[32px]">
            <div className="w-[490px] h-[490px] rounded-[32px] overflow-hidden">
              <canvas className="rounded-[32px]" ref={canvasRef} />
            </div>
          </div>
        </div>
      </div>
      {/* 选择样式 */}
      <Modal show={chooseStyleState} mode={1}>
        <Div100vh className="flex justify-end items-center flex-col w-screen">
          <div
            className="w-full h-full absolute z-0 left-0 top-0"
            onClick={() => {
              setChooseStyleState()
              setStyle((v) => ({ ...v, choose: v.current }))
            }}
          ></div>
          <div
            className="blur-glass-effect bg-[#FFFFFF40] w-full px-[15px] relative z-10 rounded-t-[10px] mb-[-50px] pb-[50px]"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
            }}
          >
            <div className="bg-[#000] w-[109px] h-[31px] rounded-full mt-[16px] text-[9px] text-white flex_center mb-[15px]">绘画风格</div>
            <div className="mx-auto relative flex flex-wrap">
              {styleState.value?.map((item, index) => (
                <div key={index} onClick={() => setStyle((v) => ({ ...v, choose: item }))} className="w-[109px] h-[134px] relative anim_btn mr-[20px] mb-[20px]">
                  <img className="w-full h-full absolute z-0" src={style.choose?.id === item.id ? styleCheckImg : styleImg} />
                  <div className="w-full h-full flex items-center flex-col relative">
                    <div className="relative mt-[6px] w-[99px] h-[98px]">
                      <ImgPlaceholder width="80%" className="w-full h-full rounded-[3px]" src={item.icon} />
                    </div>
                    <div className={`font-normal text-center mt-[5px] text-[13px] leading-1 ${style.choose?.id === item.id ? 'text-white' : 'text-black'}`}>{item.title}</div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-[36px] mb-[23px] flex_center">
              <div
                className="w-[169px] h-[42px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] text-[13px] text-white text-center leading-[42px] rounded-[8px] theme_btn anim_btn"
                onClick={() => {
                  setTimeout(() => {
                    document.body.scrollTop = 0
                    document.documentElement.scrollTop = 0
                    window.scrollTo({
                      top: 0,
                      left: 0
                    })
                  }, 1000)
                  setChooseStyleState()
                  setStyle((v) => ({ ...v, current: v.choose }))
                }}
              >
                确定
                {/* <img className="w-full h-full" src={nextbtnImg}></img> */}
              </div>
            </div>
          </div>
        </Div100vh>
      </Modal>

      <Modal show={promptModal} mode={1}>
        <Div100vh className="flex touch-none justify-end items-center flex-col w-screen select-none">
          <div className="w-full h-full absolute z-0 left-0 top-0" onClick={setPromptModal}></div>

          {/* 录音弹窗 */}
          <AnimatePresence>
            {canRecording ? (
              <>
                <motion.div
                  {...maskAnimate}
                  onClick={() => {
                    // if (versionInfo.versionNumber >= 40508) {
                    //   e.stopPropagation()
                    //   e.preventDefault()
                    // }
                  }}
                  className="fixed bg-[#00000050] w-full h-full top-0 left-0 z-[701]"
                ></motion.div>
                <motion.div
                  {...defAnimate}
                  className="w-full h-full top-0 left-0 flex_center
                    fixed z-[702]"
                >
                  <div className="w-[359.5px] h-[134.5px] relative flex_center">
                    <img className="w-full h-full absolute z-0" src={modal3Img} />
                    <img className="w-[270.5px] h-[24px] mt-[32px] relative" src={waveGif} />
                  </div>
                </motion.div>
              </>
            ) : null}
          </AnimatePresence>

          <div
            className="blur-glass-effect bg-[#FFFFFF40] w-full px-[15px] relative z-10 rounded-t-[5px] mb-[-50px] pb-[50px]"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
            }}
          >
            <div className="bg-[#000] w-[109px] h-[31px] rounded-full mt-[16px] text-[10px] text-white flex_center mb-[15px]">添加咒语</div>

            {/* 描述词 */}
            <div className="mx-auto relative flex flex-wrap">
              {prompt.map((item, index) => (
                <div key={index} className="bg-white inline-block min-h-[20px] rounded-[5px] mr-[11px] mb-[11px]">
                  <div className="w-full h-full flex_center">
                    <div className={`font-normal text-black text-[13px] ml-[15px] leading-none mr-[7px]`}>{item.chineseContent}</div>
                    <img onClick={(e) => delDescription(e, index)} className="w-[15px] h-[15px] mr-[5px] anim_btn" src={close2Img} />
                  </div>
                </div>
              ))}
              <div className="flex-1 min-w-[100px] flex">
                <input
                  ref={descriptionInput}
                  onKeyDown={descriptionAdd}
                  onChange={inputChange}
                  value={inputText}
                  onBlur={(e) => descriptionAdd(e, 'onBlur')}
                  placeholder="请输入描述词"
                  className={`w-full select-text bg-transparent placeholder:text-[#FFF] text-[13px] text-white border-0 focus-visible:outline-none focus:outline-none`}
                />
              </div>
            </div>

            <div className="mt-[18px] mb-[23px] h-[52px] flex_center select-none">
              {/* 录音按钮 */}
              <Spin
                // bgColor="transparent"
                state={voiceState.loading || microphonePermissionLoading}
                onTouchStart={(e) => {
                  // if (versionInfo.versionNumber < 40508) {
                  e.preventDefault()
                  e.stopPropagation()
                  // console.log('开始录音 onTouchStart', canRecording)
                  if (!voiceState.loading) {
                    // 开始录音
                    microphoneClick(true)
                  }
                  // }
                }}
                onTouchEnd={(e) => {
                  // if (versionInfo.versionNumber < 40508) {
                  e.preventDefault()
                  e.stopPropagation()
                  // console.log('结束录音 onTouchEnd', canRecording)
                  if (!voiceState.loading) {
                    // 结束录音
                    setTimeout(() => microphoneClick(false), 200)
                  }
                  // }
                }}
                onClick={(e) => {
                  // if (versionInfo.versionNumber >= 40508 && !canRecording) {
                  // console.log('录音 onClick')
                  //   e.stopPropagation()
                  //   e.preventDefault()
                  //   // 开始录音
                  //   microphoneClick(true)
                  // }
                  e.stopPropagation()
                  e.preventDefault()
                }}
                ignoreClick={(e) => {
                  e.stopPropagation()
                  e.preventDefault()
                }}
                className={`w-[50px] h-[50px] select-none rounded-full flex_center anim_btn mr-[30px] relative ${canRecording ? 'z-[702]' : ''}`}
              >
                <div
                  style={{
                    background: 'linear-gradient( 180deg, #8381E7 0%, #5C62EA 100%)'
                  }}
                  className="w-full h-full touch_callout pointer-events-none select-none bg-cover flex_center relative"
                >
                  <PiMicrophone className="text-[30px]" />
                </div>
              </Spin>

              <div className="w-[169px] h-[42px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] text-[13px] text-white text-center leading-[42px] rounded-[8px] theme_btn anim_btn" onClick={setPromptModal}>
                {/* <img className="w-full h-full" src={nextbtnImg}></img> */}
                确定
              </div>
            </div>
          </div>
        </Div100vh>
      </Modal>

      <Modal show={!isVideo}>
        {uploadSatus == 'start' ? (
          <div className="w-[542px] text-center h-[443px] pt-[20px] px-[24px] bg-[#F6F6F6] rounded-[21px]">
            <div style={{ color: '#272D53' }}>提交作品</div>
            <div>
              <div>
                {generateImg && lastUserImage.current ? (
                  <div className="flex justify-around">
                    <div className="border-[5px] border-[#fff] rounded-[10px]">
                      <img className="rounded-[10px]" src={generateImg} alt="" />
                    </div>
                    <div className="border-[5px] border-[#fff] rounded-[10px]">
                      <img className="rounded-[10px]" src={lastUserImage.current} alt="" />
                    </div>
                  </div>
                ) : null}
              </div>
              <div className="pt-[5px]" style={{ color: '#262E53' }}>
                *提交后作品将自动保存
              </div>
              <div className="flex pt-[21px] justify-center">
                <div className="w-[169px] h-[42px] border leading-[42px] text-[13px] text-[rgba(0,0,0,0.8)] mr-[51px] rounded-[21px] border-solid border-[#9b9b9b] cancel_btn" onClick={closeHandle}>
                  关闭
                </div>
                <div className="w-[169px] h-[42px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] leading-[42px] text-[13px] text-white rounded-[21px] theme_btn" onClick={examConfirm}>
                  我知道了
                </div>
              </div>
            </div>
          </div>
        ) : uploadSatus == 'cannel' ? (
          <div className="w-[542px] text-center h-[443px] pt-[67px] px-[24px] bg-[#F6F6F6] rounded-[21px]">
            <div className="flex justify-center">
              <img className="w-[101px] h-[101px]" src={iconImg} alt="" />
            </div>
            <div className="pt-[38px] text-[#262E53] text-[19px]">取消上传后无法续传</div>
            <div className="text-[#262E53] text-[19px]">请确认是否取消上传</div>
            <div className="flex mt-[80px] justify-center">
              <div
                onClick={() => WorksHandle('start', false)}
                className="w-[169px] h-[42px] border leading-[42px] text-[13px] text-[rgba(0,0,0,0.8)] mr-[51px] rounded-[21px] border-solid border-[#9b9b9b] cancel_btn"
              >
                继续
              </div>
              <div onClick={() => WorksHandle('delete', false)} className="w-[169px] h-[42px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] leading-[42px] text-[13px] text-white rounded-[21px] theme_btn">
                取消上传
              </div>
            </div>
          </div>
        ) : (
          <div className="w-[542px] text-center h-[443px] pt-[67px] px-[24px] bg-[#F6F6F6] rounded-[21px]">
            <div className="flex justify-center">
              <img className="w-[101px] h-[101px]" src={iconImg} alt="" />
            </div>
            <div className="pt-[38px] text-[#262E53] text-[19px]">删除后录制测评将会永久删除不可恢复。</div>
            <div className="text-[#262E53] text-[19px]">请确认是否删除测评。</div>
            <div className="flex mt-[80px] justify-center">
              <div
                className="w-[169px] h-[42px] border leading-[42px] text-[13px] text-[rgba(0,0,0,0.8)] mr-[51px] rounded-[21px] border-solid border-[#9b9b9b] cancel_btn"
                onClick={() => WorksHandle('start', false)}
              >
                继续
              </div>
              <div className="w-[169px] h-[42px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] leading-[42px] text-[13px] text-white rounded-[21px] theme_btn" onClick={() => WorksHandle('delete', true)}>
                删除
              </div>
            </div>
          </div>
        )}
      </Modal>
      <Modal show={examStatus}>
        <div className="w-[542px] text-center h-[443px] pt-[67px] px-[24px] bg-[#F6F6F6] rounded-[21px]">
          <div className="flex justify-center">
            <img className="w-[101px] h-[101px]" src={iconImg} alt="" />
          </div>
          <div className="pt-[38px] text-[#262E53] text-[19px]">当前浏览器版本不支持屏幕录制，</div>
          <div className="text-[#262E53] text-[19px]">请下载最新版浏览器</div>
          <div className="flex mt-[109px] justify-center">
            <div className="w-[169px] h-[42px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] leading-[42px] text-[13px] text-white rounded-[21px] theme_btn" onClick={() => WorksHandle('isSupper', false)}>
              确定
            </div>
          </div>
        </div>
      </Modal>
      <Modal show={experienceEnd} mode={2}>
        <Div100vh className="h-screen w-screen float-right relative flex justify-end">
          <div
            className="w-full h-full absolute z-0 left-0 top-0"
            onClick={() => {
              setExperienceEnd(false)
              navigate('/')
            }}
          ></div>
          <div className="h-full w-[440px] z-10  rounded-[20px_0px_0px_20px] bg-white mr-[-40px]">
            <div className="h-full w-[400px] pt-[32px]">
              <div className="mx-[28px]">
                <div className="h-[24px]">
                  <img
                    className="anim_btn cursor-pointer w-[25px] h-[24px]"
                    src={ret2Img}
                    onClick={() => {
                      setExperienceEnd(false)
                      navigate('/')
                    }}
                  />
                  <div className="font-medium text-[16px] text-[#272D53]"></div>
                </div>
                <div className="flex_center flex-col mt-[79px]">
                  <img className="w-[100px] h-[115px]" src={vipImg} alt="" />
                  <div className="mt-[12px] font-medium text-[20px] text-[#272D53]">免费AI绘画体验已结束</div>
                  <div className="mt-[12px] font-normal text-[16px] text-[#636880]">购买会员，抒发无尽创意</div>
                  <div className="mt-[80px] flex justify-between w-full">
                    <div
                      className="cancel_btn anim_btn cursor-pointer w-[120px] h-[52px] rounded-[26px] font-medium text-[16px] text-[#272D53] flex_center"
                      onClick={() => {
                        setExperienceEnd(false)
                        navigate('/')
                      }}
                    >
                      退出创作
                    </div>
                    <div
                      className="theme_btn anim_btn cursor-pointer w-[212px] h-[52px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] rounded-[26px] font-medium text-[16px] text-white flex_center"
                      onClick={() => {
                        setVipEnd(true)
                        // window.location.href = `/activity/landing?${import.meta.env.VITE_APP_CHANNEL}`
                      }}
                    >
                      购买会员
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Div100vh>
      </Modal>
      <Vip mode={mode} />
      <MaskLoading state={generateState.loading} time={2000} />
    </div>
  )
}
