import { Image, Button } from 'react-vant'
import FixedLeft from '@/components/FixedLeft'
import { downloadImage } from '@/utils/downloadImage'

export default function CertificatePage() {
  // 添加关闭逻辑处理
  const handleClose = () => {
    // 这里添加返回逻辑，例如：
    // navigate(-1)
  }

  return (
    <FixedLeft
      title="荣誉证书"
      modelVisible={true} // 必需属性
      onClose={handleClose} // 必需属性
    >
      <div className="p-4">
        <div className="aspect-[3/4] bg-gray-50 rounded-lg overflow-hidden mb-6">
          <Image src="https://picsum.photos/600/800?random=1" alt="荣誉证书" fit="contain" className="w-full h-full" />
        </div>
        <Button round type="primary" onClick={() => downloadImage('https://picsum.photos/600/800?random=1', '我的荣誉证书')}>
          保存到相册
        </Button>
      </div>
    </FixedLeft>
  )
}
