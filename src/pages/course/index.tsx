import { useCallback, useEffect, useRef, useState } from 'react'
import { courseList } from '@/api/course'
import { Course, checkCourseState, checkIndexState, checkVideoState, minFloatState } from '@/store/video'
import { toast } from 'react-toastify'
import { useAsyncFn, useUpdateEffect } from 'react-use'
import { useGetAtom } from '@/hooks/useGetAtom'
import React from 'react'
import { debounce } from '@/utils/tool'
import { useAtom, useAtomValue } from 'jotai'
import { loginModalState, userState } from '@/store/global'
import ImgPlaceholder from '@/components/ImgPlaceholder'
import useAuthAction from '@/hooks/useAuthAction'
import { useSearchParams } from 'react-router-dom'

export default function Index() {
  const [check, setCheck] = useState(0)
  const [checkCourse, setCheckCourse, getCheckCourse] = useGetAtom(checkCourseState)
  const [checkVideo, setCheckVideo, getCheckVideo] = useGetAtom(checkVideoState)
  const [checkIndex, setCheckIndex, getCheckIndex] = useGetAtom(checkIndexState)
  const [isProgrammaticScroll, setIsProgrammaticScroll] = useState(false)
  const rightPaneRef = useRef<any>(null)
  const leftPaneItemsRef = useRef([])
  const minFloat = useAtomValue(minFloatState)
  const user = useAtomValue(userState)
  const [loginModal, setLoginModal] = useAtom(loginModalState)
  const [searchParams] = useSearchParams()

  const handleLeftClick = (index: number) => {
    setCheck(index)
    setIsProgrammaticScroll(true)
    if (rightPaneRef.current && rightPaneRef.current.children[index]) {
      rightPaneRef.current.children[index].scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest', top: '300px' })
      // 如果你需要在滚动之前先移动100px
      //  window.scrollBy(0, -100);
    }
  }
  // 禁止页面拖动
  document.addEventListener(
    'touchmove',
    function (event) {
      // 只允许竖向滚动
      if (Math.abs(event.touches[0].clientX - event.touches[1]?.clientX) > Math.abs(event.touches[0].clientY - event.touches[1]?.clientY)) {
        event.preventDefault() // 阻止横向拖动
      }
    },
    { passive: false }
  )

  const handleRightScroll = () => {
    if (isProgrammaticScroll) {
      setIsProgrammaticScroll(false)
      return
    }

    const rightPane = rightPaneRef.current
    if (!rightPane) return

    const scrollTop = rightPane.scrollTop
    const children: any[] = Array.from(rightPane.children)
    let index = 0

    for (let i = 0; i < children.length; i++) {
      console.log(children[i].offsetTop, scrollTop)
      if (children[i].offsetTop <= scrollTop + 200) {
        index = i
      } else {
        break
      }
    }

    setCheck(index)
  }

  // 防抖处理 排队处理
  const debounceHandleRightScroll = useCallback(debounce(handleRightScroll, 300), [])

  useEffect(() => {
    courseListFetch()
    return () => {}
  }, [])

  const [courseListData, courseListFetch] = useAsyncFn<() => Promise<Course[]>>(async () => {
    const res = await courseList()
    if (res.code !== 200) {
      toast(res.msg)
      return ''
    }
    const course = res.data[0]
    setCheckCourse(course)
    if (course.payStatus) {
      setCheckIndex(0)
    } else {
      leftPaneItemsRef.current = course.preShow.map(() => React.createRef())
    }
    return res.data
  }, [])

  useUpdateEffect(() => {
    if (checkIndex !== -1) {
      const list = checkCourse?.videoList || []
      setCheckVideo(list[checkIndex])
    } else {
      setCheckVideo(null)
    }
  }, [checkIndex])

  useUpdateEffect(() => {
    if (!loginModal && user) {
      window.location.reload()
    }
  }, [loginModal])

  const exchange = useAuthAction(() => {
    window.location.href = `/activity/landing?${import.meta.env.VITE_APP_CHANNEL}`
  })

  return (
    <div className="w-screen h-[100svh] darw_bg">
      {!checkCourse?.payStatus ? (
        <div className="darw_bg box-border overflow-hidden">
          <div className="w-full h-[712px] flex flex-col items-center">
            <div className="w-[970px] h-[60px] bg-white rounded-[16px] flex justify-between items-center mt-[10px]">
              <div className="ml-[24px] font-semibold text-[16px] text-[#272D53] leading-[23px] text-left not-italic">AI创作宝盒</div>
              <div
                onClick={exchange}
                className="mr-[24px] anim_btn cursor-pointer  flex_center w-[160px] h-[40px] shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] rounded-[20px] font-medium text-[13px] text-white leading-[18px] text-right not-italic"
                style={{ background: 'linear-gradient(180deg, #8381e7 0%, #5c62ea 100%)' }}
              >
                立即购买
              </div>
            </div>
            <div className="w-[970px] h-[570px] flex mt-[16px]">
              <div className="h-full rounded-[16px] flex-1 mr-[16px] bg-slate-400 overflow-hidden">
                <div className="h-full overflow-y-auto" ref={rightPaneRef} onScroll={debounceHandleRightScroll}>
                  {checkCourse?.preShow?.map((item, i) => (
                    <div key={i} className="bg-[#dddddd] relative">
                      <ImgPlaceholder className="w-full" src={item.url} alt="" />
                    </div>
                  ))}
                </div>
              </div>
              <div className="h-full rounded-[16px] w-[115px] bg-white pt-[18px]">
                {checkCourse?.preShow?.map((item, i) => (
                  <div
                    key={i}
                    ref={leftPaneItemsRef.current[i]}
                    onClick={() => handleLeftClick(i)}
                    className={`anim_btn relative h-[28px] my-[10px] flex_center  font-normal text-[12px] text-[#636880] leading-[17px] text-left not-italic ${
                      check === i ? 'text-[#272D53] font-bold bg-[#F4F4FD]' : 'text-[#636880]'
                    }`}
                  >
                    {check === i ? (
                      <div
                        style={{
                          background: 'linear-gradient(180deg, #8381e7 0%, #5c62ea 100%)'
                        }}
                        className="absolute top-0 left-0 w-[4px] h-[28px] rounded-[0px_50px_50px_0px]"
                      ></div>
                    ) : null}
                    {item.name}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="w-screen h-full darw_bg flex justify-center relative pt-[32px]">
          <div className="w-[205px] h-[585px] overflow-x-hidden rounded-[11px] overflow-y-auto mr-[25px] pr-[10px]">
            {checkCourse?.videoList.map((item, i) => (
              <div
                key={i}
                onClick={() => {
                  item.status === 1 && item.videoUrl && setCheckIndex(i)
                }}
                className={`rounded-[11px] relative w-full mb-[18px] last:mb-0 bg-white`}
              >
                <img className="h-[148px] w-[205px] object-cover rounded-[11px]" src={item.cover} alt="" />
                <div className="absolute w-full bg-white bottom-[0px] flex py-[6px] items-center px-[8px] rounded-[0_0_11px_11px]">
                  <div
                    className={`w-[34px] h-[34px] text-[20px] mr-[8px] flex justify-center items-center rounded-[17.5px]  ${
                      checkVideo?.courseVideoId === item.courseVideoId ? 'text-[#fff] bg-[#5D63EA]' : 'bg-[#636880] text-[#fff]'
                    }`}
                  >
                    {String(i + 1).padStart(2, '0')}
                  </div>
                  <div className={`bg-white text-[20px] flex_center flex-1 ${checkVideo?.courseVideoId === item.courseVideoId ? 'text-[#5D63EA]' : 'text-[#636880]'}`}>{item.title}</div>
                </div>
              </div>
            ))}
          </div>
          <div className="w-[780px] h-[585px] flex_center">
            <div id="video_box" className="w-[780px] h-[585px] rounded-[10px] overflow-hidden relative">
              {minFloat ? (
                <div className="w-full h-full bg-black pt-[50px]">
                  <div className="text-white text-[20px] ml-[50px]">以”画中画“模式播放</div>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
