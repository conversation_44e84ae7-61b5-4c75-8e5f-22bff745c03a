import { useNavigate } from 'react-router-dom'
import noLoginImg from '@/assets/images/my/no_login.png'

export default function Index() {
  const navigate = useNavigate()

  return (
    <div className="w-screen h-screen flex_center flex-[20px] text-[18px]">
      <div className="w-full flex_center flex-col mt-[112px]">
        <img className="w-[129px] h-[90px]" src={noLoginImg} alt="" />
        <div className="text-[#6855A9] text-[16px] mt-[20px]">
          对不起，页面找不到了。
          <span className="text-blue-600" onClick={() => navigate('/')}>
            {/* <span className="text-blue-600" onClick={() => navigate('/', { replace: true })}> */}
            回首页
          </span>
        </div>
      </div>
    </div>
  )
}
