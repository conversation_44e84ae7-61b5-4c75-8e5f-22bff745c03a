import { Swiper, SwiperSlide } from 'swiper/react'
import 'swiper/css'
import { EffectCoverflow, Pagination } from 'swiper/modules'
import 'swiper/css/effect-coverflow'
import 'swiper/css/pagination'
import './index.css'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useAsyncFn, useUpdateEffect } from 'react-use'
import { getHome, getWorks } from '@/api/home'
import ImgPlaceholder from '@/components/ImgPlaceholder'
import advertImg from '@/assets/images/home/<USER>'
import workImg from '@/assets/images/home/<USER>'
import { sleep } from '@/utils/tool'
import loadingGif from '@/assets/images/global/loading.png'
import { AnimatePresence, motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import useInfiniteScroll from 'react-infinite-scroll-hook'

interface TopAdverts {
  image?: string
  type?: string
  url?: string
}
interface BottomAdvert {
  image?: string
  type?: string
  url?: string
}
interface UserShowBean {
  avatar: string
  nickName: string
}
interface AwardWorks {
  title?: string
  image?: string
  userImage?: string
  userShowBean?: UserShowBean
  worksId?: number
}
interface HotWorks {
  title?: string
  image?: string
  userImage?: string
  userShowBean?: UserShowBean
  worksId?: number
}

interface HomeData {
  topAdverts: TopAdverts[]
  bottomAdvert: BottomAdvert
  awardWorks: AwardWorks
  hotWorks: HotWorks[]
}

export default function Index() {
  const [page, setPage] = useState(0)
  const [worksList, setWorksList] = useState<HotWorks[]>([{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}])
  const [topAdverts, setTopAdverts] = useState<TopAdverts[]>([{}])
  const [bottomAdvert, setBottomAdvert] = useState<BottomAdvert>({})
  const [awardWorks, setAwardWorks] = useState<AwardWorks>({})
  const [isDone, setIsDone] = useState(false)
  const [aniIndex, setAniIndex] = useState(0)
  const navigate = useNavigate()

  useEffect(() => {
    homeFetch()
  }, [])

  useUpdateEffect(() => {
    worksFetch()
  }, [page])

  const [homeData, homeFetch] = useAsyncFn<() => Promise<HomeData>>(async () => {
    await sleep(100)
    const res = await getHome()
    if (res.code !== 200) {
      toast(res.msg)
      return ''
    }
    const hotWorks = [{}, {}, {}, {}, res.data.hotWorks[0], res.data.hotWorks[1], res.data.hotWorks[2], {}, {}, {}, {}, ...res.data.hotWorks.slice(3)]
    setWorksList(hotWorks)
    setTopAdverts(res.data.topAdverts)
    setBottomAdvert(res.data.bottomAdvert)
    setAwardWorks(res.data.awardWorks)
    return { ...res.data }
  }, [])

  const [worksData, worksFetch] = useAsyncFn(async () => {
    await sleep(2000)
    const res = await getWorks({ page })
    if (res.code !== 200) {
      toast(res.msg)
      return ''
    }
    if (!res.data.hasNest) {
      setIsDone(true)
    }
    setAniIndex(worksList.length)
    setWorksList((v) => [...v, ...res.data.worksList])
    return res.data.worksList
  }, [page])

  const [sentryRef] = useInfiniteScroll({
    loading: worksData.loading,
    hasNextPage: !isDone,
    onLoadMore: () => {
      setPage((v) => v + 1)
    },
    // When there is an error, we stop infinite loading.
    // It can be reactivated by setting "error" state as undefined.
    disabled: !!worksData.error,
    // `rootMargin` is passed to `IntersectionObserver`.
    // We can use it to trigger 'onLoadMore' when the sentry comes near to become
    // visible, instead of becoming fully visible on the screen.
    rootMargin: '0px 0px 400px 0px'
  })

  return (
    <div className="min-h-screen darw_bg pt-[16px] pb-[137px]" id="content">
      <Swiper
        effect={'coverflow'}
        grabCursor={true}
        centeredSlides={true}
        slidesPerView={'auto'}
        spaceBetween={150}
        coverflowEffect={{
          rotate: 0,
          stretch: 0,
          depth: 300,
          modifier: 1,
          slideShadows: true
        }}
        // loop
        pagination={true}
        modules={[EffectCoverflow, Pagination]}
        className="w-[1028px]"
      >
        {topAdverts.map((item, i) => (
          <SwiperSlide key={i} className={`relative bg-white w-[1028px]`}>
            <a href={item.url} className={`inline-block relative w-full h-full bg-[#dddddd] ${!item.image ? '' : 'skeleton'}`}>
              {item.image ? <ImgPlaceholder width="20%" className="w-full h-full object-cover" src={item.image} /> : null}
            </a>
          </SwiperSlide>
        ))}
      </Swiper>

      <div className="mt-[16px] w-[1028px] relative flex flex-wrap mx-auto">
        <div
          onClick={() => {
            if (bottomAdvert.type === 'work') {
              navigate(`/works?id=${bottomAdvert?.url}`)
            } else {
              window.location.href = bottomAdvert.url || ''
            }
          }}
          className={`inline-block bg-[#dddddd] absolute z-10 w-[284px] h-[274px] top-0 left-0 rounded-[21px] border-[7px] border-[#fff] overflow-hidden ${bottomAdvert.image ? '' : 'skeleton'}`}
        >
          <img className="w-[81px] h-[29px] absolute top-[-2px] left-[-2px] z-10" src={workImg} alt="" />
          {bottomAdvert.image ? <ImgPlaceholder className="w-full h-full object-cover" src={bottomAdvert.image} alt="" /> : null}
          <div className="absolute bottom-0 left-0 z-10 w-full h-[35px] bg-[#00000075] flex items-center">
            <div className="bg-slate-200 rounded-full w-[25px] h-[25px] gradient_style flex_center relative ml-[10px] mr-0">
              <img className="w-[20px] h-[20px] rounded-full" src="https://img.aigc.metaleap.com/template/image/art_avatar.png?x-oss-process=image/resize,l_256/format,webp" alt="" />
            </div>
            <div className="absolute bottom-0 h-[35px] w-[316px] left-[2px] z-10 tetx-[9px] text-white scale-75 truncate flex items-center">中国美院AI中心</div>
          </div>
        </div>
        <div
          onClick={() => navigate(`/works?id=${awardWorks?.worksId}`)}
          className={`bg-[#dddddd] absolute z-10 border-[7px] border-[#fff] w-[294px] h-[274px] top-0 left-[288px] rounded-[21px] overflow-hidden ${awardWorks.image ? '' : 'skeleton'}`}
        >
          <img className="w-[81px] h-[29px] absolute top-[-2px] left-[-2px] z-10" src={workImg} alt="" />
          {awardWorks.image ? (
            <>
              <ImgPlaceholder className="w-full h-full object-cover" src={awardWorks.image} alt="" />
              <div className="absolute bottom-0 left-0 z-10 w-full h-[35px] bg-[#00000075] flex items-center">
                <div className="bg-slate-200 rounded-full w-[25px] h-[25px] gradient_style flex_center relative ml-[10px] mr-0">
                  <img className="w-[20px] h-[20px] rounded-full" src={awardWorks.userShowBean?.avatar} alt="" />
                </div>
                <div className="absolute bottom-0 h-[35px] w-[316px] left-[2px] z-10 tetx-[9px] text-white scale-75 truncate flex items-center">{awardWorks.userShowBean?.nickName}</div>
              </div>
            </>
          ) : null}
        </div>
        <AnimatePresence>
          {worksList.map((item, i) => (
            <motion.div
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.1, delay: (i - aniIndex) * 0.02 }}
              key={i}
              onClick={() => navigate(`/works?id=${item?.worksId}`)}
              className={`bg-[#dddddd] overflow-hidden relative w-[133px] h-[132px] border-[4px] border-[#fff] rounded-[16px] text-[9px] ${i > 7 ? 'mb-[18px]' : 'mb-[9px]'} ${
                (i + 1) % 7 === 0 ? '' : 'mr-[15px]'
              } ${item.image ? '' : 'skeleton'}`}
            >
              {item.image ? (
                <>
                  <ImgPlaceholder width="70%" className="w-full h-full object-cover" src={item.image} />
                  <div className="absolute bottom-0 left-0 z-10 w-full h-[22px] bg-[#00000075] flex items-center">
                    <div className="bg-slate-200 rounded-full w-[18px] h-[18px] gradient_style flex_center relative ml-[10px] mr-0">
                      <img className="w-[15px] h-[15px] rounded-full" src={item.userShowBean?.avatar} alt="" />
                    </div>
                    <div className="absolute bottom-0 h-[22px] w-[135.5px] left-[16px] z-10 text-[14px] flex_center text-white scale-75 truncate">{item.userShowBean?.nickName}</div>
                  </div>
                </>
              ) : null}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {homeData.loading ? null : (
        <div className="mt-[20px] mb-[20px] flex_center text-[16px]">
          {isDone ? (
            <div className="text-[#999]">已经加载完了~</div>
          ) : worksData.loading ? (
            <div className="flex_center">
              <img className="w-[28px] h-[28px] animate-spin" src={loadingGif} />
            </div>
          ) : null}
        </div>
      )}

      {(homeData.loading || !isDone) && <div ref={sentryRef}></div>}
    </div>
  )
}
