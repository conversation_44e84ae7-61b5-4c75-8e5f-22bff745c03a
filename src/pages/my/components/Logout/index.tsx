import ret2Img from '@/assets/images/vip/ret.png'
import { useAsyncFn } from 'react-use'
import { toast } from 'react-toastify'
import logoutImg from '@/assets/images/vip/logout.png'
import { useAtom, useSetAtom } from 'jotai'
import { serveModalState, userState } from '@/store/global'
import { logout } from '@/api/global'
import Spin from '@/components/Spin'

export default ({ close }: { close: () => void }) => {
  const setUser = useSetAtom(userState)
  const [serveModal, setServeModal] = useAtom(serveModalState)

  const [logoutData, logoutFetch] = useAsyncFn(async () => {
    const res = await logout()
    if (res.code !== 200) {
      toast(res.msg)
      return ''
    }
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    setUser(null)
    close()
    setServeModal(false)
    return null
  }, [])

  return (
    <div className="mx-[28px] pt-[32px] h-full flex flex-col">
      <div className="h-[24px] flex_center">
        <img className="anim_btn cursor-pointer w-[25px] h-[24px]" src={ret2Img} alt="" onClick={close} />
        <div className="flex-1 text-center font-medium text-[16px] text-[#272D53]">提示</div>
        <div className="w-[25px] h-[24px]"></div>
      </div>
      <div className="flex items-center flex-col mt-[178px] relative flex-1">
        <img className="w-[243px] h-[190px]" src={logoutImg} alt="" />
        <div className="mt-[-9px] font-medium text-[21px] text-[#272D53] leading-[30px] ">是否确认退出当前账号</div>

        <div className="w-full absolute bottom-[28px] h-[52px]">
          <div className="w-full h-full rounded-[26px] overflow-hidden relative flex justify-between">
            <div className="theme_btn anim_btn cursor-pointer w-[176px] h-full shadow-[0px_2_2px_0px_rgba(0,0,0,0.1)] rounded-[26px] font-medium text-[16px] text-white flex_center" onClick={close}>
              取消
            </div>
            <Spin state={logoutData.loading} className="cancel_btn anim_btn cursor-pointer w-[176px] h-full rounded-[26px] font-medium text-[16px] text-[#272D53] flex_center" onClick={logoutFetch}>
              确定
            </Spin>
          </div>
        </div>
      </div>
    </div>
  )
}
