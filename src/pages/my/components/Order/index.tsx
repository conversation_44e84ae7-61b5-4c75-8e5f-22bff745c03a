import React, { UIEvent } from 'react'
import ret2Img from '@/assets/images/vip/ret.png'
import { useCallback, useEffect, useState } from 'react'
import menuSelectImg from '@/assets/images/vip/menu-select.png'
import copyImg from '@/assets/images/vip/copy.png'
import { useAsyncFn, useCopyToClipboard, useUpdateEffect } from 'react-use'
import { toast } from 'react-toastify'
import { sleep } from '@/utils/tool'
import { listStatus } from '@/api/pay'
import ImgPlaceholder from '@/components/ImgPlaceholder'
import loadingGif from '@/assets/images/global/loading.png'
import useGetState from '@/hooks/useGetState'
import { FiLoader } from 'react-icons/fi'

const menu = [
  {
    title: '全部',
    val: -1
  },
  {
    title: '待收货',
    val: 6
  },
  {
    title: '已完成',
    val: 7
  }
  // {
  //   title: '已取消',
  //   val: 3
  // }
]

// 0 创建订单
// 1 等待支付
// 2 已支付
// 3 已取消
// 4 物流待发货
// 5 物流发货中
// 6 待收货oo
// 7 已完成
// 8 退货中
// 9 退款中
// 10 已退款
// 11 已退货退款
// 12 支付订单创建失败
const orderType: { [key: number]: string } = {
  // 0: '创建订单',
  // 1: '等待支付',
  // 2: '已支付',
  // 3: '已取消',
  // 4: '物流待发货',
  // 5: '物流发货中',
  // 6: '待收货',
  // 7: '已完成',
  // 8: '退货中',
  // 9: '退款中',
  // 10: '已退款',
  // 11: '已退货退款',
  // 12: '支付订单创建失败'
  2: '待收货',
  3: '已取消',
  4: '待收货',
  5: '待收货',
  6: '待收货',
  7: '已完成',
  12: '已取消'
}

// "id": 93,
// "orderId": "20241151400004",
// "status": 11,
// "productName": "中国美术学院AI中心AI课程创作宝盒",
// "logisticsNo": "",
// "logisticsCompany": "",
// "logisticsStatus": 1,
// "goodsType": 1
interface OrderList {
  id: number
  orderId: string
  status: number
  productName: string
  logisticsNo: string
  logisticsCompany: string
  logisticsStatus: number
  goodsType: number
  goodsImage: string
}

export default ({ close }: { close: () => void }) => {
  const [activeLeft, setActiveLeft] = useState(0)
  const [chooseMenu, setChooseMenu] = useState(menu[0].val)
  const [orderList, setOrderList, getOrderList] = useGetState<OrderList[]>([])
  const [_, copyToClipboard] = useCopyToClipboard()
  const [isDone, setIsDone] = useState(false)
  const [aniIndex, setAniIndex] = useState(0)

  useEffect(() => {
    computeLeft()
    window.addEventListener('resize', computeLeft)
    return () => {
      window.removeEventListener('resize', computeLeft)
    }
  }, [chooseMenu])

  const computeLeft = useCallback(() => {
    const index = menu.findIndex((item) => item.val === chooseMenu)
    const dom = document.getElementById(`orderMenu`)
    const w = dom!.clientWidth
    // 13.5 是小圆点的宽度
    // 768 是设计稿高度
    const l = (460 / menu.length / 2 - 13.5 / 2) / 768
    console.log(w, document.body.offsetHeight)
    setActiveLeft((w / menu.length) * index + document.body.offsetHeight * l)
  }, [chooseMenu])

  useEffect(() => {
    orderListFetch()
  }, [])

  // 复制
  const copy = (item: OrderList) => {
    toast.success(`复制成功`)
    copyToClipboard(item.logisticsNo || item.orderId)
  }

  const [orderListData, orderListFetch] = useAsyncFn(async () => {
    await sleep(500)
    const list = getOrderList()
    const minId = list.length ? list[list.length - 1].id || '' : ''
    const res = await listStatus({ status: chooseMenu, minId })
    if (res.code !== 200) {
      toast(res.msg)
      return ''
    }
    if (res.data.length < 10) {
      setIsDone(true)
    }
    setAniIndex(list.length)
    if (list.length === 0) {
      setOrderList(res.data)
    } else {
      setOrderList((v) => [...v, ...res.data])
    }
    return null
  }, [chooseMenu])

  useUpdateEffect(() => {
    setOrderList([])
    setIsDone(false)
    orderListFetch()
  }, [chooseMenu])

  const scroll = (e: UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target as HTMLDivElement
    console.log(scrollTop, scrollHeight, clientHeight, orderListData.loading, isDone)
    if (scrollTop + clientHeight >= scrollHeight - 30 && !orderListData.loading && !isDone) {
      orderListFetch()
    }
  }

  return (
    <div className="w-full h-full flex flex-col">
      <div id="orderMenu" className="w-full h-[111px] bg-white relative">
        <div className="mx-[28px] h-[24px] flex_center mt-[34px]">
          <img className="anim_btn cursor-pointer w-[25px] h-[24px]" src={ret2Img} alt="" onClick={close} />
          <div className="flex-1 text-center font-medium text-[16px] text-[#272D53]">订单物流</div>
          <div className="w-[25px] h-[24px]"></div>
        </div>
        <div className="h-[24px] mt-[20px] flex relative">
          {menu.map((item, index) => (
            <div onClick={() => setChooseMenu(item.val)} key={index} className="flex-1 flex_center">
              <div
                className={`cursor-pointer anim_btn text-[17px] leading-[24px] transition-[color,font-weight] duration-[0.25s] ${
                  chooseMenu === item.val ? 'font-medium  text-[#272D53]' : 'font-normal text-[#999999]'
                }`}
              >
                {item.title}
              </div>
            </div>
          ))}
        </div>
        <div className="absolute bottom-[4px] transition-[left,width] duration-[0.25s] w-[13px] h-[2px]" style={{ left: activeLeft }}>
          <img src={menuSelectImg} alt="" />
        </div>
      </div>
      <div className="flex-1 w-full bg-[#F8F8F8] pt-[13px] overflow-y-scroll overflow-x-hidden" onScroll={scroll}>
        {orderList.map((item, index) => (
          <div key={index} className="mx-[13px] bg-white rounded-[16px] mb-[13px] flex_center">
            <div className="ml-[8px] my-[8px] w-[63px] h-[63px] border rounded-[11px] border-solid border-[#EEEEEE]">
              <ImgPlaceholder src={item.goodsImage} alt="" />
            </div>
            <div className="ml-[8px] flex-1">
              <div className="font-normal text-[17px] text-[#272D53] leading-[24px] truncate">{item.productName}</div>
              <div className="flex items-center mt-[13px]">
                {item.logisticsNo ? (
                  <div className="font-normal text-[13px] text-[#999999] leading-[17px]">物流单号：{item.logisticsNo}</div>
                ) : (
                  <div className="font-normal text-[13px] text-[#999999] leading-[17px]">订单编号：{item.orderId}</div>
                )}
                <img className="w-[13px] h-[13px] ml-[4px]" onClick={() => copy(item)} src={copyImg} alt="" />
              </div>
            </div>
            <div className="mr-[8px] ml-[16px] font-normal text-[13px] text-[#5D63EA] leading-[17px]">{orderType[item.status] || '未知'}</div>
          </div>
        ))}

        <div className="h-[64px] flex_center mb-[13px]">
          {!orderListData.loading && isDone && orderList.length ? (
            <div className="mt-[20px] mb-[20px] flex_center">
              <div className="text-[#999] text-[17px]">已经加载完了~</div>
            </div>
          ) : null}

          {orderListData.loading ? (
            <div className="mt-[20px] mb-[20px] flex_center">
              <div className="flex_center">
                <FiLoader className="text-[30px] text-[#888] animate-spin" />
              </div>
            </div>
          ) : null}

          {!orderListData.loading && isDone && !orderList.length ? (
            <div className="relative w-full flex_center flex-col">
              {/* <img className="w-[417px] h-[278px]" src={no_workImg} alt="" /> */}
              <div className="text-[#636880] text-[17px]">你还没有订单哦~</div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  )
}
