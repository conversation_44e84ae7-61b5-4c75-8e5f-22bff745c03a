import '@/utils/fabric-eraser-brush.js'
import Modal from '@/components/Modal'
import 'react-circular-progressbar/dist/styles.css'
import ret2Img from '@/assets/images/vip/ret.png'
import orderImg from '@/assets/images/vip/order.png'
import nextImg from '@/assets/images/vip/next.png'
import { useAtom, useSetAtom } from 'jotai'
import { serveModalState, userState } from '@/store/global'
import { useEffect, useState } from 'react'
import Order from '../Order'
import { AiOutlinePoweroff } from 'react-icons/ai'
import { logout } from '@/api/global'
import { useAsyncFn } from 'react-use'
import { toast } from 'react-toastify'
import Spin from '@/components/Spin'
import Div100vh from 'react-div-100vh'
import Logout from '../Logout'

const ServeList = [
  {
    title: '订单物流',
    desc: '',
    img: orderImg
  }
]

export default function Index() {
  const [serveModal, setServeModal] = useAtom(serveModalState)
  const [chooseServe, setChooseServe] = useState(-1)

  useEffect(() => {
    if (!serveModal) {
      setChooseServe(-1)
    }
  }, [serveModal])

  return (
    <Modal show={serveModal} mode={2}>
      <Div100vh className="h-screen w-screen float-right relative flex justify-end">
        <div className="w-full h-full absolute z-0 left-0 top-0" onClick={() => setServeModal(false)}></div>
        <div className="h-full w-[500px] z-10  rounded-[20px_0px_0px_20px] bg-white mr-[-40px] overflow-hidden">
          <div className="h-full w-[460px]">
            {/* 提醒 */}
            {chooseServe === -1 ? (
              <div className="mx-[28px] pt-[32px] h-full flex flex-col">
                <div className="h-[24px] flex_center">
                  <img className="anim_btn cursor-pointer w-[25px] h-[24px]" src={ret2Img} alt="" onClick={() => setServeModal(false)} />
                  <div className="flex-1 text-center font-medium text-[16px] text-[#272D53]">更多服务</div>
                  <div className="w-[25px] h-[24px]"></div>
                </div>
                <div className="flex flex-col mt-[41px] relative flex-1">
                  {ServeList.map((item, i) => (
                    <div key={i} className="w-full h-[38px] flex_center" onClick={() => setChooseServe(i)}>
                      <img className="w-[38px] h-[38px]" src={item.img} alt="" />
                      <div className="ml-[13px] flex-1 font-normal text-[17px] text-[#272D53] leading-[17px]">{item.title}</div>
                      <img className="w-[17px] h-[17px]" src={nextImg} alt="" />
                    </div>
                  ))}

                  <div className="absolute left-0 h-[55px] bottom-[30px] w-full flex_center ">
                    <div className="w-full h-[55px] rounded-[27px] overflow-hidden relative">
                      <div onClick={() => setChooseServe(1)} className="anim_btn cursor-pointer w-full h-[55px] border rounded-[27px] border-solid border-[#5D63EA] flex_center">
                        <AiOutlinePoweroff className="text-[#5D63EA] text-[16px]" />
                        <div className="ml-[10px] font-normal text-[17px] text-[#5D63EA]">退出登录</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : null}
            {chooseServe === 0 ? <Order close={() => setChooseServe(-1)} /> : null}
            {chooseServe === 1 ? <Logout close={() => setChooseServe(-1)} /> : null}
          </div>
        </div>
      </Div100vh>
    </Modal>
  )
}
