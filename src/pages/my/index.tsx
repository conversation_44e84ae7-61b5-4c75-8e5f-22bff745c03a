import noLoginImg from '@/assets/images/my/no_login.png'
import no_workImg from '@/assets/images/my/no_work.png'
import defHeadImg from '@/assets/images/my/def_head.png'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { exchangeModalState, loginModalState, memberState, serveModalState, userState, vipEndState } from '@/store/global'
import { useNavigate } from 'react-router-dom'
import { useAsyncFn, useUpdateEffect } from 'react-use'
import { userWorksList } from '@/api/board'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import loadingGif from '@/assets/images/global/loading.png'
import { sleep } from '@/utils/tool'
import { AnimatePresence, motion } from 'framer-motion'
import ImgPlaceholder from '@/components/ImgPlaceholder'
import useAuthAction from '@/hooks/useAuthAction'
import vip_iconImg from '@/assets/images/vip/vip_icon.png'
import vip_lapseImg from '@/assets/images/vip/vip_lapse.png'
import vip_useImg from '@/assets/images/vip/vip_use.png'
import redemptionImg from '@/assets/images/vip/redemption.png'
import serveImg from '@/assets/images/vip/serve.png'
import Vip from '@/components/Vip'
import Serve from './components/Serve'

interface WorksItem {
  id?: number
  title?: string
  userImage?: string
  generateImage?: string
  prompt?: string
  styleId?: number
  styleName?: string
  recommendFlag?: number
}

export default () => {
  const navigate = useNavigate()
  const setLoginModal = useSetAtom(loginModalState)
  const user = useAtomValue(userState)
  const [page, setPage] = useState(1)
  const [isDone, setIsDone] = useState(false)
  const [aniIndex, setAniIndex] = useState(0)
  const [worksList, setWorksList] = useState<WorksItem[]>([{}, {}, {}, {}, {}, {}])
  const [exchangeModal, setExchangeModal] = useAtom(exchangeModalState)
  const [member, setMember] = useAtom(memberState)
  const [vipEnd, setVipEnd] = useAtom(vipEndState)
  const [serveModal, setServeModal] = useAtom(serveModalState)

  useEffect(() => {
    if (user) {
      worksItemFetch()
    }
  }, [user])

  const [worksItemData, worksItemFetch] = useAsyncFn<() => Promise<{ worksList: WorksItem[]; hasNest: boolean }>>(async () => {
    await sleep(500)
    const res = await userWorksList(page)
    if (res.code !== 200) {
      toast(res.msg)
      return ''
    }
    if (!res.data.hasNest) {
      setIsDone(true)
    }
    setAniIndex(worksList.length)
    if (page === 1) {
      setWorksList(res.data.worksList)
    } else {
      setWorksList((v) => [...v, ...res.data.worksList])
    }
    return res.data
  }, [page])

  useUpdateEffect(() => {
    worksItemFetch()
  }, [page])

  const exchange = useAuthAction(() => setExchangeModal((v) => !v))

  return (
    <div className="w-full min-h-screen darw_bg pt-[32px] pb-[100px] overflow-x-hidden">
      <div className="w-[976px] h-[80px] flex m-auto">
        <div onClick={user ? undefined : () => setLoginModal(true)} className={`w-full h-full rounded-[20px] bg-white flex items-center ${user ? '' : 'anim_btn'}`}>
          <div className="w-[50px] h-[50px] rounded-full  ml-[20px] flex_center overflow-hidden">
            <img className="w-[50px] h-[50px]" src={user ? user.avatar : defHeadImg} alt="" />
          </div>
          {user ? (
            <div className="ml-[16px]">
              <div className="font-medium text-[16px] text-[#272D53] leading-[23px]">{user?.nickname}</div>
              {!member?.memberFlag ? (
                <div className="h-[20px] font-normal text-[12px] text-[#636880] leading-[12px] flex items-center">
                  购买会员，抒发无尽创意
                  <div
                    className="anim_btn ml-[7px] px-[8px] h-[20px] rounded-[4px] flex items-center justify-evenly font-medium text-[10px] text-[#67391D] leading-[16px]"
                    style={{
                      background: 'linear-gradient(90deg, #fddbc5 0%, #fdc8a7 100%)'
                    }}
                    onClick={() => setVipEnd(true)}
                  >
                    <img className="w-[12px] h-[12px]" src={vip_iconImg} />
                    购买会员
                  </div>
                </div>
              ) : null}
              {member?.memberFlag && member?.expire ? (
                <div className="flex items-center font-normal text-[12px] text-[#636880] leading-[12px]">
                  <img className="mr-[4px] w-[12px] h-[12px]" src={vip_lapseImg} />
                  会员已过期
                  <div onClick={() => setVipEnd(true)} className="ml-[12px] anim_btn px-[8px] h-[20px] rounded-[4px] bg-[#2A2624] font-medium text-[8px] text-[#FDC7A2] leading-[26px] flex_center">
                    立即续费
                  </div>
                </div>
              ) : null}
              {member?.memberFlag && !member?.expire ? (
                <div className="flex items-center font-normal text-[12px] text-[#636880] leading-[12px]">
                  <img className="mr-[4px] w-[12px] h-[12px]" src={vip_useImg} />
                  会员有效期：{member?.expireTime}
                  <div onClick={() => setVipEnd(true)} className="ml-[12px] anim_btn px-[8px] h-[20px] rounded-[4px] bg-[#2A2624] font-medium text-[8px] text-[#FDC7A2] leading-[26px] flex_center">
                    立即续费
                  </div>
                </div>
              ) : null}
            </div>
          ) : (
            <div className="ml-[18px] text-[#636880] text-[16px]">立即登录</div>
          )}
          {/* <div className="ml-[18px] text-[#636880] text-[16px]">{user?.nickname || '立即登录'}</div>
          {user ? <img onClick={() => navigate('/order')} className="w-[22px] h-[22px] ml-[18px]" src={toorderImg} alt="" /> : null} */}

          {user ? (
            <div className="flex flex-1 justify-end pr-[20px]">
              <div onClick={exchange} className="anim_btn cursor-pointer font-normal text-[12px] text-[#272D53] leading-[16px] h-[16px] flex_center">
                <img className="w-[16px] h-[16px] mr-[4px]" src={redemptionImg} alt="" />
                课程兑换
              </div>
              <div onClick={() => setServeModal(true)} className="anim_btn cursor-pointer ml-[20px] font-normal text-[12px] text-[#272D53] leading-[16px] h-[16px] flex_center">
                <img className="w-[16px] h-[16px] mr-[4px]" src={serveImg} alt="" />
                更多服务
              </div>
            </div>
          ) : null}
        </div>
      </div>
      <div className="w-[976px] mt-[20px] m-auto">
        {!user ? (
          <div className="w-full flex_center flex-col mt-[161px]">
            <img className="w-[172px] h-[126px]" src={noLoginImg} alt="" />
            <div className="text-[#636880] text-[13px] mt-[46px]">还没有登录呦~</div>
          </div>
        ) : (
          <>
            <div className="mt-[16px] relative flex flex-wrap">
              <AnimatePresence>
                {worksList.map((item, i) => (
                  <motion.div
                    onClick={() => navigate(`/works?id=${item.id}`)}
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.1, delay: (i - aniIndex) * 0.02 }}
                    key={i}
                    className={`bg-[#dddddd] overflow-hidden border-[4px] border-[#fff] relative w-[135px] h-[135px] rounded-[15px] mb-[5px] ${(i + 1) % 7 === 0 ? '' : 'mr-[5px]'} ${
                      item.generateImage ? '' : 'skeleton'
                    }`}
                  >
                    {item.generateImage ? (
                      <>
                        <ImgPlaceholder className="w-[135px] h-[135px] object-cover" src={item.userImage} />
                        <div className="absolute py-[5px] bottom-0 left-0 z-10 w-full  bg-[#00000075] flex items-center">
                          <div className="bg-slate-200 rounded-full w-[25px] h-[25px] gradient_style flex_center relative ml-[10px] mr-0">
                            <img className="w-[22px] h-[22px] rounded-full" src={user ? user.avatar : defHeadImg} alt="" />
                          </div>
                          <div className="text-[9px] flex_center text-white scale-75">{user.nickname}</div>
                        </div>
                      </>
                    ) : null}
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {!worksItemData.loading && isDone && worksList.length ? (
              <div className="mt-[20px] mb-[20px] flex_center">
                <div className="text-[#999]">已经加载完了~</div>
              </div>
            ) : null}

            {worksItemData.loading && page !== 1 ? (
              <div className="mt-[20px] mb-[20px] flex_center">
                <div className="flex_center">
                  <img className="w-[28px] h-[28px] animate-spin" src={loadingGif} />
                </div>
              </div>
            ) : null}

            {!worksItemData.loading && !isDone ? (
              <div className="mt-[20px] text-[18px] mb-[20px] flex_center">
                <div onClick={() => setPage((v) => v + 1)} className="text-[#3F51B5]">
                  加载更多
                </div>
              </div>
            ) : null}

            {!worksItemData.loading && isDone && !worksList.length ? (
              <div className="relative w-full flex_center flex-col mt-[112px]">
                <img className="w-[417px] h-[278px]" src={no_workImg} alt="" />
                <div className="absolute top-[123px] text-[#636880] text-[17px]">你还没有作品哦~</div>
              </div>
            ) : null}
          </>
        )}
      </div>
      {user ? <Vip mode={1} /> : null}
      <Serve />
    </div>
  )
}
