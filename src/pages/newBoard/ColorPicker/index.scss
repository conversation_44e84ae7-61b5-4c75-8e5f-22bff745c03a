.color-picker {
  .hex-color.react-colorful {
    width: 100%;
    .react-colorful__saturation {
      width: 100%;
      border-radius: 11px;
      margin-bottom: 15px;
      border: 0;
    }
    .react-colorful__last-control {
      height: 10px;
      border-radius: 10px;
      margin-bottom: 20px;
    }
    .react-colorful__pointer {
      width: 20px;
      height: 20px;
    }
  }
  .color-input-box {
    display: flex;
    font-size: 16px;
    justify-content: space-between;
    align-items: center;
    .color-input {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 120px;
      height: 38px;
      background: #fff;
      border-radius: 6px;
      padding: 6px 8px;
      .color-box {
        width: 26px;
        height: 26px;
        border-radius: 4px;
        background: #000;
      }
      input {
        width: 80px;
        height: 24px;
        border: none;
        font-size: 16px;
        padding: 0;
        text-align: center;
        outline: none;
        
      }
    }
  }
}
