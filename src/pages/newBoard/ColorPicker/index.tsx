import { useLocation, useNavigate } from 'react-router-dom'
import { RgbaColorPicker, HexColorPicker } from 'react-colorful'
import diImg from '@/assets/images/board/di_1.png'
import { AnimatePresence, motion } from 'framer-motion'
import './index.scss'
import { useState } from 'react'

interface IColor {
  show: boolean
  value: string
  onChange: (color: string, type?: 0 | 1) => void
}

const colors = ['#F5222D', '#FA8C16', '#FADB14', '#8BBB11', '#52C41A', '#13A8A8']
const colors2 = ['#1677FF', '#2F54EB', '#722ED1', '#EB2F96', '#edbcbc', '#edd6bc']

export default function Index({ onChange, value, show }: IColor) {
  // return (
  //   // <RgbaColorPicker
  //   //   onChange={(value) => {
  //   //     console.log(value)
  //   //   }}
  //   // />

  // )

  if (show) {
    return (
      <div className="color-picker">
        <HexColorPicker className="hex-color" color={value} onChange={onChange} />
        <div className="flex w-full h-auto justify-between mb-[15px] ">
          {colors.map((item) => (
            <div key={item} className="w-[26px] h-[26px] rounded-[4px] cursor-pointer" onClick={() => onChange(item)} style={{ background: item }}></div>
          ))}
        </div>

        <div className="flex w-full h-auto justify-between">
          {colors2.map((item) => (
            <div key={item} className="w-[26px] h-[26px] rounded-[4px] cursor-pointer" onClick={() => onChange(item)} style={{ background: item }}></div>
          ))}
        </div>
        {/* <div className="color-input-box">
          <span>色值</span>
          <div className="color-input">
            <div className="color-box" style={{ background: value }}></div>
            <input type="text" value={value} readOnly />
          </div>
        </div> */}
      </div>
    )
  }
}
