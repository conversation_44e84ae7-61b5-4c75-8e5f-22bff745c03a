import { useEffect, useRef, useCallback, useState } from 'react'
import { useNavigate, useSearchParams, useParams } from 'react-router-dom'
import useGetState from '@/hooks/useGetState'
import { FaPencil } from 'react-icons/fa6'
import { RiDeleteBinLine } from 'react-icons/ri'

import { useAsyncFn, useToggle, useUpdateEffect } from 'react-use'
import ColorPicker from './ColorPicker'
import { fabric } from 'fabric'
import '@/utils/fabric-eraser-brush.js'
import uploadClient from '@/utils/upload'
import { BodyType, ipcInvoke, ipcOn, isElectron } from '@/utils/electron'

import { throttle, debounce, sleep, dataURLToFile, getBase64Info, base64ToFile } from '@/utils/tool'
import { getDrawId, img2img } from '@/api/newBoard'

export default function Index() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  // const styleId = searchParams.get('styleId')
  const imgToImgQueuing = useRef(false) // 队列(图生图必须等上一次完成才请求)
  const generateFetchState = useRef(false) // 生成图片loading状态
  const sendDisplayViewMsg = useRef(true) // 是否通知预览端
  const abortControllerRef = useRef<AbortController | null>(null) // 用于取消请求的 AbortController
  const activeTouchesRef = useRef<number>(0)

  const { id: styleId, color: styleColor, iconName: dragIconClass } = JSON.parse(localStorage.themeStyle ?? '{}')

  const drawId = useRef<any>(null)
  const displayWinId = useRef<any>(null)
  const parentRef = useRef<HTMLDivElement>(null) // 画板 父级dom
  const canvasRef = useRef(null) // 画板dom
  const ctxRef = useRef<CanvasRenderingContext2D | null>(null) // 画板实体
  const canvasClass = useRef<any>(null) // 画板实例

  const [originalDimensions, setOriginalDimensions, getOriginalDimensions] = useGetState({ width: 0, height: 0 }) // 缩放画板上一次的尺寸
  const [penSize, setPenSize, getPenSize] = useGetState(30) // 画笔大小
  const [color, setColor, getColor] = useGetState<string>('#13A8A8')
  const [drawColor, setDrawColor, getDrawColor] = useGetState<string>('#13A8A8') // 画笔颜色
  const [history, setHistory, getHistory] = useGetState<string[]>([`{"version":"5.3.0","objects":[],"background":"${styleColor}"}`]) // 画板步骤记录器
  const [currentStep, setCurrentStep, getCurrentStep] = useGetState<number>(0)
  const [tool, setTool, getTool] = useGetState<'brush' | 'eraser'>('brush')
  const [placeholder, setPlaceholder] = useState(dragIconClass)

  useEffect(() => {
    const fabricCanvas = new fabric.Canvas(canvasRef.current, { isDrawingMode: true, backgroundColor: styleColor, selection: false, enablePointerEvents: true, fireRightClick: false })
    fabricCanvas.freeDrawingBrush.color = drawColor
    fabricCanvas.freeDrawingBrush.width = penSize
    canvasClass.current = fabricCanvas

    ctxRef.current = fabricCanvas.getContext()
    // 画板画一笔调用生成图监听 - 使用防抖
    fabricCanvas.on('path:created', debounceHandlePathCreated)
    fabricCanvas.on('mouse:down', (e: any) => {
      const targetTouches = e.e?.targetTouches?.length
      console.log('🚀 ~ fabricCanvas.on ~ mouse:down', e.e)
      if (targetTouches && targetTouches <= 1) {
        // fabricCanvas.freeDrawingBrush.color = drawColor
      } else if (targetTouches && targetTouches > 1) {
        e.e.preventDefault()
        // fabricCanvas.freeDrawingBrush.color = styleColor
      } else {
        // fabricCanvas.freeDrawingBrush.color = drawColor
      }
      setPlaceholder('')
    })
    // 画板步骤监听
    fabricCanvas.on('mouse:up', () => {
      console.log('🚀 ~ fabricCanvas.on ~ mouse:up')
      sendDisplayViewMsg.current = true
      saveState(fabricCanvas)
    })

    fabricCanvas.on('touch:gesture', function (e) {
      // 阻止默认的触摸手势行为，避免干扰绘图
      e.e.preventDefault()
    })

    const parentElement = parentRef.current
    if (parentElement) {
      const initialWidth = parentElement.clientWidth
      const initialHeight = parentElement.clientHeight
      setOriginalDimensions({ width: initialWidth, height: initialHeight })
      fabricCanvas.setWidth(initialWidth)
      fabricCanvas.setHeight(initialHeight)
    }
    // 画板缩放
    const resizeCanvas = () => {
      const parentElement = parentRef.current
      const getDimensions = getOriginalDimensions()
      if (parentElement && getDimensions.width && getDimensions.height) {
        const scale = parentElement.clientWidth / getDimensions.width
        setOriginalDimensions({ width: parentElement.clientWidth, height: parentElement.clientHeight })
        fabricCanvas.setWidth(parentElement.clientWidth)
        fabricCanvas.setHeight(parentElement.clientHeight)
        fabricCanvas.getObjects().forEach((obj) => {
          obj.scaleX = obj.scaleX! * scale
          obj.scaleY = obj.scaleY! * scale
          obj.left = obj.left! * scale
          obj.top = obj.top! * scale
          obj.setCoords()
        })

        fabricCanvas.renderAll()
      }
    }

    getDrawId().then((res) => {
      if (res.code == 200) {
        createPlayWindow(res.data)
        drawId.current = res.data
      }
    })
    // 画板缩放防抖处理
    const throttleResizeCanvas = throttle(resizeCanvas, 300)

    return () => {
      // 取消正在进行的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
        abortControllerRef.current = null
      }

      window.removeEventListener('resize', throttleResizeCanvas)
      fabricCanvas.off('path:created', debounceHandlePathCreated)
      fabricCanvas.off('mouse:up')
      // fabric-with-erasing 有bug,销毁就报错
      try {
        fabricCanvas.dispose()
      } catch (error) {
        console.log(error)
      }
    }
  }, [])

  const createPlayWindow = async (id: string) => {
    const inElectron = isElectron()
    displayWinId.current = 'display#' + id
    const url = '#/display/' + encodeURIComponent(id)

    if (inElectron) {
      const isDev = import.meta.env.MODE === 'development'
      const wins = await ipcInvoke('custom', { method: 'all-wins' })
      wins?.data.map((item: string) => {
        if (item !== 'mainWin') {
          ipcInvoke('win', { sendWinName: item, method: 'close' })
        }
      })
      const screen = await ipcInvoke('custom', {
        method: 'screen-info'
      })
      console.log('🚀 ~ createPlayWindow ~ screen:', screen.data)
      const [screen1, screen2] = screen.data
      const screenInfo = screen2?.bounds.x !== 0 || screen2?.bounds.y !== 0 ? screen2.bounds : screen1.bounds
      await ipcInvoke('custom', {
        method: 'create-win',
        data: {
          ...screenInfo,
          newWinName: displayWinId.current,
          path: url,
          frame: false,
          resizable: false,
          hasShadow: false,
          fullscreen: true,
          skipTaskbar: true,
          // alwaysOnTop: true,
          roundedCorners: false
        }
      })
      if (!isDev && screen2) {
        ipcInvoke('win', { sendWinName: displayWinId.current, method: 'setAlwaysOnTop' }, true, 'screen-saver')
      }
    } else {
      window.open(url)
    }
  }

  // 画板步骤记录
  const saveState = (canvas: fabric.Canvas) => {
    const history = getHistory()
    const currentStep = getCurrentStep()
    const state: string = JSON.stringify(canvas.toJSON())
    const historyCopy = history.slice(0, currentStep + 1)
    historyCopy.push(state)
    setHistory(historyCopy)
    setCurrentStep(currentStep + 1)
  }

  // 画板步骤回退
  const historyState = (index: number) => {
    const canvas = canvasClass.current
    if (!canvas) return
    canvas.loadFromJSON(history[index], () => {
      canvas.renderAll()
      if (index == 0) {
        if (abortControllerRef.current) {
          abortControllerRef.current.abort()
          abortControllerRef.current = null
        }
        sendDisplayViewMsg.current = false
        setPlaceholder(dragIconClass)
        ipcInvoke('custom', {
          method: 'msg',
          toWinName: displayWinId.current,
          data: { loaded: '' }
        })
      } else {
        setPlaceholder('')
        sendDisplayViewMsg.current = true
        debounceHandlePathCreated()
      }
      setCurrentStep(index)
    })
  }

  const clearCanvas = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }
    const canvas = canvasClass.current
    if (canvas) {
      canvas.clear()
      setCurrentStep(0)
      setPlaceholder(dragIconClass)
      setHistory([`{"version":"5.3.0","objects":[],"background":"${styleColor}"}`])
      canvas.setBackgroundColor(styleColor, canvas.renderAll.bind(canvas))
      // debounceHandlePathCreated()
      sendDisplayViewMsg.current = false
      ipcInvoke('custom', {
        method: 'msg',
        toWinName: displayWinId.current,
        data: { loaded: '' }
      })
    }
  }

  const toHome = () => {
    navigate('/')
    ipcInvoke('win', { sendWinName: displayWinId.current, method: 'close' })
  }

  const debounceHandlePathCreated = useCallback(
    debounce(() => {
      if (generateFetchState.current) {
        imgToImgQueuing.current = true
      } else {
        imgToImgQueuing.current = false
        handlePathCreatedFetch()
      }
    }, 500),
    []
  )

  const colorDifference = (canvas: any, ignoreTransparent = true) => {
    // 检查Canvas是否有内容
    if (!canvas || !canvas.getContext) return 0

    const ctx = canvas.getContext('2d')
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    const data = imageData.data
    const colorSet = new Set()

    // 遍历所有像素（每4个值为一个像素的RGBA）
    for (let i = 0; i < data.length; i += 4 * 30) {
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]
      const a = data[i + 3]

      // 忽略完全透明的像素（可选）
      if (ignoreTransparent && a === 0) {
        continue
      }

      // 将RGBA转换为唯一字符串（如"255,0,0,255"）
      const colorKey = `${r},${g},${b}`
      colorSet.add(colorKey)
    }
    function hasColorsExceedingTolerance(colors: any, tolerance = 10) {
      const n = colors.length

      // 处理边界情况
      if (n < 2) return false

      // 双重循环遍历所有颜色对（避免重复计算）
      for (let i = 0; i < n - 1; i++) {
        for (let j = i + 1; j < n; j++) {
          const color1 = colors[i]
          const color2 = colors[j]

          // 计算颜色距离（复用之前的函数）
          const distance = calculateColorTolerance(color1, color2)

          // 若超过容差，立即返回true
          if (distance > tolerance) {
            return true
          }
        }
      }

      // 所有颜色对均在容差范围内
      return false
    }

    /**
     * 计算两个RGB颜色的欧氏距离（容差）
     */
    function calculateColorTolerance(color1: any, color2: any) {
      const [r1, g1, b1] = color1.split(',').map(Number)
      const [r2, g2, b2] = color2.split(',').map(Number)
      const dr = r1 - r2
      const dg = g1 - g2
      const db = b1 - b2
      return Math.sqrt(dr * dr + dg * dg + db * db)
    }

    const result = hasColorsExceedingTolerance([...colorSet], 10)
    return result
  }

  // canvas 导出 base64,上传 oss 后调接口图生图
  const [handlePathCreatedState, handlePathCreatedFetch] = useAsyncFn<() => Promise<string>>(async () => {
    try {
      // 创建新的 AbortController
      abortControllerRef.current = new AbortController()
      const signal = abortControllerRef.current.signal

      const canvas = canvasClass.current
      if (!canvas) return

      const diff = colorDifference(canvasRef.current)

      if (!diff) {
        ipcInvoke('custom', {
          method: 'msg',
          toWinName: displayWinId.current,
          data: { loaded: '' }
        })
        return false
      }

      canvas.renderAll()
      if (sendDisplayViewMsg.current) {
        ipcInvoke('custom', {
          method: 'msg',
          toWinName: displayWinId.current,
          data: { loading: true }
        })
      }

      const dataURL = canvas.toDataURL({
        format: 'png',
        quality: 0.8
      })

      // const imgInfo = await getBase64Info(dataURL)
      const imgfile = dataURLToFile(dataURL, 'image.png')
      const [uploadImg] = await uploadClient.fileUpload([imgfile])

      const { data } = await img2img(
        {
          styleId,
          imageUrl: uploadImg.url,
          prompt: '',
          width: 1920, // 4:3写死
          height: 1440, // 4:3写死
          uniqueId: drawId.current
        },
        signal
      )
      if (sendDisplayViewMsg.current && data?.imageUrl) {
        ipcInvoke('custom', {
          method: 'msg',
          toWinName: displayWinId.current,
          data: { loaded: data.imageUrl }
        })
      } else {
        ipcInvoke('custom', {
          method: 'msg',
          toWinName: displayWinId.current,
          data: { loading: false }
        })
      }
      return data.imageUrl
    } catch (error) {
      console.log(error)
      ipcInvoke('custom', {
        method: 'msg',
        toWinName: displayWinId.current,
        data: { loading: false }
      })
    }
  }, [])

  useEffect(() => {
    const canvas = canvasClass.current
    if (canvas) {
      canvas.freeDrawingBrush.width = penSize
      canvas.freeDrawingBrush.color = drawColor
    }
  }, [drawColor, penSize])

  useEffect(() => {
    generateFetchState.current = handlePathCreatedState.loading
    if (!handlePathCreatedState.loading && sendDisplayViewMsg.current && imgToImgQueuing.current) {
      imgToImgQueuing.current = false
      handlePathCreatedFetch()
    }
  }, [handlePathCreatedState])

  useEffect(() => {
    const canvas = canvasClass.current
    if (!canvas) return
    if (tool === 'brush') {
      canvas.freeDrawingBrush = new fabric.PencilBrush(canvas)
      canvas.freeDrawingBrush.color = drawColor
      canvas.freeDrawingBrush.width = penSize
    } else {
      // canvas.freeDrawingBrush = new fabric.EraserBrush(canvas, { inverted: true }, true)
      // canvas.freeDrawingBrush.width = 30
      canvas.freeDrawingBrush = new fabric.PencilBrush(canvas)
      canvas.freeDrawingBrush.color = styleColor
      canvas.freeDrawingBrush.width = 40
    }
  }, [tool])

  return (
    <div id="content" className="w-screen h-screen flex_center select-none overflow-hidden touch-pan-x bg-[#C6D0ED]">
      <div className="relative w-full h-full flex_center">
        {/* home */}
        <div className="home absolute top-[45px] left-[45px] w-[75px] h-[75px]" onClick={() => toHome()}></div>
        {/* 左侧工具栏 */}
        <div className={`relative w-[333px] h-[915px] rounded-[48px] mr-[30px] p-[30px] bg-linear shadow-xl`}>
          {/* 色卡 */}
          <div className="relative w-full h-[272px]">
            <div className="absolute top-[117px] left-[70px] w-[118px] h-[118px] rounded-full " style={{ backgroundColor: color }}></div>
            {/* 毛笔 */}
            <div
              className="absolute top-[43px] left-[70px] ai-pen w-[227px] h-[233px] cursor-pointer "
              onClick={() => {
                setTool('brush')
              }}
            ></div>
          </div>
          <ColorPicker
            show={true}
            value={color}
            onChange={(color) => {
              setColor(color)
              setDrawColor(color)
              setTool('brush')
            }}
          />

          <div className="absolute bottom-0 left-0 w-full p-[30px] flex_center flex-col">
            <div className="flex w-full justify-between cursor-pointer">
              <div className="w-[80px] h-[80px] rounded-[15px] ai-next flex_center bg-white active:bg-[#FFF3E2]" onClick={() => historyState(currentStep - 1)}></div>
              <div className="w-[80px] h-[80px] rounded-[15px] ai-prev flex_center bg-white active:bg-[#FFF3E2]" onClick={() => historyState(currentStep + 1)}></div>
              <div
                className={`${tool === 'eraser' ? 'bg-[#FFE6EF]' : 'bg-white'} w-[80px] h-[80px] rounded-[15px] ai-eraser flex_center active:bg-[#FFE6EF]`}
                onClick={() => setTool(tool === 'brush' ? 'eraser' : 'brush')}
              ></div>
            </div>
            <div className=" w-full border-t-[1px] border-[#ddd] my-[20px]"> </div>
            <div
              className="w-full h-[90px] flex_center bg-[#14B8A6] text-white rounded-[15px] active:bg-[#0d9488] text-[30px] cursor-pointer"
              onClick={() => {
                clearCanvas()
              }}
            >
              清空 <RiDeleteBinLine className="ml-[10px]" />
            </div>
          </div>
        </div>

        {/* 画板 */}
        <div className={`w-[1300px] h-[975px]  rounded-[48px] p-[24px] bg-linear shadow-xl box-border `}>
          <div ref={parentRef} className="w-full h-full bg-white rounded-[30px] overflow-hidden relative ">
            <canvas ref={canvasRef}></canvas>
            <div className={`${placeholder || 'hidden'} absolute left-0 top-0 w-full h-full z-10`}></div>
          </div>
        </div>
      </div>
    </div>
  )
}
