import noLoginImg from '@/assets/images/my/no_login.png'
import retImg from '@/assets/images/board/ret.png'
import addressImg from '@/assets/images/order/address.png'
import { useAtomValue, useSetAtom } from 'jotai'
import { loginModalState, userState } from '@/store/global'
import { useNavigate } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { useAsyncFn, useCopyToClipboard } from 'react-use'
import { userOrderList } from '@/api/order'
import { toast } from 'react-toastify'
import loadingGif from '@/assets/images/global/loading.png'
import { sleep } from '@/utils/tool'
import { AnimatePresence, motion } from 'framer-motion'

interface OrderItem {
  orderId?: string
  productName?: string
  logisticsNo?: string
  logisticsCompany?: string
  logisticsStatus?: 0 | 1
}

export default () => {
  const navigate = useNavigate()
  const [_, copyToClipboard] = useCopyToClipboard()
  const [orderList, setOrderList] = useState<OrderItem[]>([{}, {}, {}, {}])

  const [orderListData, orderListFetch] = useAsyncFn<() => Promise<OrderItem[]>>(async () => {
    await sleep(500)
    const res = await userOrderList()
    if (res.code !== 200) {
      toast(res.msg)
      return ''
    }
    console.log(res)
    setOrderList(res.data)
    return res.data
  }, [])

  useEffect(() => {
    orderListFetch()
  }, [])

  // 复制
  const copy = (item: OrderItem) => {
    if (item.logisticsStatus === 0) {
      toast('待发货,暂无物流信息')
      return
    }
    if (item.logisticsNo) {
      toast.success(`复制成功，物流公司：${item.logisticsCompany}，物流单号：${item.logisticsNo}`)
      copyToClipboard(item.logisticsNo)
    } else {
      toast('暂无物流信息')
    }
  }

  return (
    <div className=" w-screen h-screen darw_bg pt-[17px] px-[16px]">
      <div className="w-[1024px] h-[68px] px-[17px] m-auto">
        <div className="h-[44px] flex items-center font-normal">
          <img onClick={() => navigate(-1)} className="w-[44p] h-[44px] anim_btn" src={retImg} alt="" />
          <div className="text-[#6855A9] text-[18px] ml-[24px]">我的订单</div>
        </div>
        <div className="mt-[28px]">
          <AnimatePresence>
            {orderList.map((item, i) => (
              <motion.div
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.1, delay: i * 0.02 }}
                key={i}
                className={`h-[68px] rounded-[10px] bg-white text-[#6855A9] text-[16px] flex items-center mb-[15px] pl-[23px] pr-[16px] ${item.orderId ? '' : 'skeleton'}`}
              >
                {item.orderId ? (
                  <>
                    <div className="flex items-center h-full flex-1">
                      订单编号：<div className="text-black mr-[20px]">{item.orderId}</div> 商品信息：<div className="text-black">{item.productName}</div>
                    </div>
                    <img onClick={() => copy(item)} className="w-[156px] h-[49px] anim_btn" src={addressImg} alt="" />
                  </>
                ) : null}
              </motion.div>
            ))}
          </AnimatePresence>

          {!orderListData.value?.length && !orderListData.loading ? (
            <div className="w-full flex_center flex-col mt-[112px]">
              <img className="w-[129px] h-[90px]" src={noLoginImg} alt="" />
              <div className="text-[#6855A9] text-[13px] mt-[13px]">暂无订单~</div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  )
}
