import { useNavigate, useSearchParams, useParams } from 'react-router-dom'
import { useState, useEffect } from 'react'
export default function Index() {
  const [searchParams] = useSearchParams()
  const url = searchParams.get('url')

  useEffect(() => {
    console.log('🚀 ~ useEffect ~ url:', url)
  }, [])

  return (
    <div id="content" className="relative w-full h-screen flex_center  play-bg pt-[16px]  select-none  overflow-hidden touch-pan-x">
      <img src={url as string} alt="" />
    </div>
  )
}
