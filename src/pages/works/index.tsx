import { useNavigate, useSearchParams } from 'react-router-dom'
import retImg from '@/assets/images/board/ret.png'
import defHeadImg from '@/assets/images/my/def_head.png'
import { useEffect, useRef } from 'react'
import { sleep } from '@/utils/tool'
import { useAsyncFn } from 'react-use'
import { competitionJoin, competitionTime, worksDetail } from '@/api/board'
import { toast } from 'react-toastify'
import MaskLoading from '@/components/MaskLoading'
import { useAtomValue } from 'jotai'
import { userState } from '@/store/global'

interface Prompt {
  md5: string
  chineseContent: string
  englishContent: string
}

export interface WorksDetail {
  id: number
  title: string
  userImage: string
  generateImage: string
  prompt: Prompt[]
  styleId: number
  styleName: string
  recommendFlag: number
  myselfWorks: boolean
  competitionFlag: number
  styleImage: string
}

interface Competition {
  startTime: number
  endTime: number
  startCompetition: boolean
  target: string
  tips: string
}

export default function Index() {
  const navigate = useNavigate()
  const parentRef = useRef<HTMLDivElement>(null)
  const [searchParams] = useSearchParams()
  const userInfo = useAtomValue(userState)

  useEffect(() => {
    generateFetch()
    console.log(searchParams.get('id'))
    if (!searchParams.get('id')) {
      toast('缺少参数')
    } else {
      worksDetailFetch()
    }
  }, [])

  // 作品详情
  const [worksDetailState, worksDetailFetch] = useAsyncFn<() => Promise<WorksDetail>>(async () => {
    await sleep(500)
    const res = await worksDetail(searchParams.get('id'))
    if (res.code !== 200) {
      toast(res.msg)
      return
    }
    return { ...res.data, prompt: JSON.parse(res.data.prompt) }
  }, [])

  // 提交作品
  const [generateState, generateFetch] = useAsyncFn<() => Promise<Competition>>(async () => {
    const res = await competitionTime()
    if (res.code !== 200) {
      return
    }
    return res.data
  }, [])

  // 参赛
  const [competitionJoinState, competitionJoinFetch] = useAsyncFn(async () => {
    const res = await competitionJoin({ worksId: searchParams.get('id') })
    if (res.code !== 200) {
      toast.error(res.msg)
      return
    }
    toast.success('提交成功')
    worksDetailFetch()
    return res.data
  }, [])

  return (
    <div className="fixed left-0 top-0 w-full  min-h-screen flex flex-col darw_bg py-[35px]">
      <div className="bg-white flex items-center h-[68px] rounded-[10px] mb-[35px]  w-[1023px] mx-auto">
        <div className="flex ml-[16px] justify-start items-center h-[68px] rounded-[10px]">
          <img onClick={() => navigate(-1)} className="w-[45px] h-[45px] anim_btn" src={retImg} alt="" />
        </div>
        <div className="w-[962px] flex items-center justify-between">
          <img className="w-[45px] h-[45  px] ml-[24px]" src={userInfo ? userInfo.avatar : defHeadImg} alt="" />
          <div className="ml-[18px] text-[17px] text-[#595959] whitespace-nowrap">{userInfo ? userInfo.nickname : ''}</div>
          <div className="ml-[50px] flex-1 text-right truncate text-[#595959] text-[13px]">
            描述词：
            {worksDetailState.value?.prompt.map((item) => (
              <>{item.chineseContent}&emsp;</>
            ))}
          </div>
          <div
            className="flex mr-[10px] justify-center items-center  text-[#ffffff] w-[53px] rounded-[10px] h-[52px] text-[13px] ml-[23px]"
            style={{
              backgroundImage: `url(${worksDetailState.value?.styleImage})`,
              backgroundSize: 'cover'
            }}
          >
            <div className="flex w-[53px] h-[53px] border text-[9px] text-white justify-center items-center whitespace-nowrap rounded-[10px] border-solid border-[#979797] bg-[#00000035]">
              {worksDetailState.value?.styleName}
            </div>
          </div>
        </div>
      </div>
      <div className="w-full flex w-[992px] justify-center">
        <div className="w-[504px] ml-[16px]">
          <div ref={parentRef} className="bg-white w-full border-[12px] border-[#fff] h-[504px] mx-auto mb-[18px] rounded-[32px] overflow-hidden">
            {worksDetailState.value?.generateImage ? <img className="w-full h-full rounded-[10px]" src={worksDetailState.value?.generateImage} alt="" /> : null}
          </div>
        </div>
        <div className="ml-[16px] w-[504px] h-[504px] border-[12px] rounded-[32px] border-[#fff]">
          <div className="w-full h-full">
            <div className="bg-white w-full h-full overflow-hidden">{worksDetailState.value?.userImage ? <img className="rounded-[32px] w-full h-full" src={worksDetailState.value?.userImage} alt="" /> : null}</div>
          </div>
        </div>
      </div>

      <MaskLoading state={worksDetailState.loading || generateState.loading || competitionJoinState.loading} time={2000} />
    </div>
  )
}
