import React, { ReactNode } from 'react'
import {} from 'react-router-dom'

// Layout import
import Layout from '../components/Layout'

export interface routerType {
  path: string
  component: ReactNode
  children?: routerType[]
}

export const ignorePath = ['/login']
export const ignoreAuthPath = ['/login', '/', '/course', '/my', '/works', '/klecks', '/apply', '/display']

const router: routerType[] = [
  {
    path: '/',
    component: <Layout />,
    children: [
      {
        path: '/',
        // component: '../pages/home/<USER>'
        component: '../pages/settings/index.tsx'
      },
      // 课件
      {
        path: '/course',
        component: '../pages/course/index.tsx'
      },
      // 画板
      {
        path: '/board',
        component: '../pages/board/index.tsx'
      },
      {
        path: '/newBoard',
        component: '../pages/newBoard/index.tsx'
      },
      // 播放画板
      {
        path: '/display/:id',
        component: '../pages/display/index.tsx'
      },
      // 画板封面
      {
        path: '/shareCover',
        component: '../pages/shareCover/index.tsx'
      },
      {
        path: '/settings',
        component: '../pages/settings/index.tsx'
      },
      // 我的
      {
        path: '/my',
        component: '../pages/my/index.tsx'
      },
      // 作品详情
      {
        path: '/works',
        component: '../pages/works/index.tsx'
      },
      // 订单
      // {
      //   path: '/order',
      //   component: '../pages/order/index.tsx'
      // },
      {
        path: '/klecks',
        component: '../pages/klecks/index.tsx'
      },
      {
        path: '/apply',
        component: '../pages/apply/index.tsx'
      },
      {
        path: '*',
        component: '../pages/error/auth/index.tsx'
      }
    ]
  },
  {
    path: '/login',
    component: '../pages/login/index.tsx'
  }
]

export default router
