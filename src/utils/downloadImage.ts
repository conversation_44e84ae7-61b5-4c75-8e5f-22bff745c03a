export const downloadImage = (url: string, filename: string) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const img = new Image();
  img.crossOrigin = 'Anonymous';
  
  img.onload = () => {
    canvas.width = img.width;
    canvas.height = img.height;
    ctx?.drawImage(img, 0, 0);
    
    // iOS兼容处理
    if (navigator.userAgent.match(/iPhone|iPad/i)) {
      const reader = new FileReader();
      canvas.toBlob((blob) => {
        if (blob) {
          reader.readAsDataURL(blob);
          reader.onloadend = () => {
            const a = document.createElement('a');
            a.href = reader.result as string;
            a.download = `${filename}.png`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          };
        }
      });
    } else {
      const dataURL = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.download = `${filename}.png`;
      link.href = dataURL;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };
  
  img.src = url;
};