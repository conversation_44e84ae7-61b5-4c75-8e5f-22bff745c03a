

// 检查是否在electron环境中
export const isElectron = () => {
  // 检查 window 是否存在且 electronAPI 属性存在
  return window.electronAPI !== undefined
}

// 创建模拟的electron API
const createMockElectronAPI = () => {
  return {
    on: () => { },
    invoke: () => Promise.reject('非Electron环境,无法调用ipcInvoke,请做好兼容'),
    send: () => { },
    removeListener: () => { },
    setLocalJSON: () => { },
    getLocalJSON: () => { },
    windowInfo: {
      winName: '',
      isMac: false,
      platform: ''
    }
  }
}

const ipcRenderer = isElectron() ? window.electronAPI : createMockElectronAPI()

export const ipcOn = (channel: string, listener: (event: any, reslut: BodyType, ...args: any[]) => void) => {
  ipcRenderer.on(channel, listener)
}

export interface BodyType {
  /**
   * 发送者 默认sessionStorage.winName
   */
  sendWinName?: string
  /**
   * 方法
   */
  method: string
  /**
   * 接收者 -1=全部 空=不通知 字符串=指定win
   */
  toWinName?: string | -1
  /**
   * 数据
   */
  data?: any
}

export const ipcInvoke = (
  // app 方法：https://www.electronjs.org/zh/docs/latest/api/app
  // win 方法：https://www.electronjs.org/zh/docs/latest/api/browser-window
  // custom: 自定义方法
  channel: 'app' | 'win' | 'screen' | 'custom' | 'DESKTOP_CAPTURER_GET_SOURCES' | 'GET_LOCAL_JSON_FILE' | 'SET_LOCAL_JSON_FILE',
  body: BodyType,
  ...args: any[]
): Promise<BodyType> => {
  // console.log('ipcInvoke:', !sessionStorage.winName ? '注意: 没有winName,执行可能失败' : sessionStorage.winName, body.method, body, ...args)
  // 处理特殊的desktopCapturer
  if (['DESKTOP_CAPTURER_GET_SOURCES', 'GET_LOCAL_JSON_FILE', 'SET_LOCAL_JSON_FILE'].includes(channel)) {
    return ipcRenderer.invoke(channel, body.data || {}).then((res: any) => {
      return {
        ...body,
        data: res
      }
    })
  }

  return ipcRenderer
    .invoke(
      channel,
      {
        sendWinName: windowInfo.winName,
        data: {},
        toWinName: '',
        ...body
      },
      ...args
    )
    .then((res: any) => {
      if (channel === 'win' && (body.method === 'setBounds' || body.method === 'setSize')) {
        // window.refreshRem()
      }
      return res
    })
}

export const windowInfo = ipcRenderer.windowInfo

export const ipcSend = (channel: string, ...args: any[]) => {
  ipcRenderer.send(channel, ...args)
}

export const ipcRemoveListener = (channel: string, listener: (...args: any[]) => void) => {
  ipcRenderer.removeListener(channel, listener)
}
