import fontCaveat from './Caveat-Regular.woff2'
import fontClimateCrisis from './ClimateCrisis-Regular-VariableFont_YEAR.woff2'
import fontCorrectionBrush from './CorrectionBrush.woff2'
import fontDancingScript from './DancingScript-Regular.woff2'
import fontDiabolik from './Diabolik-Regular-VF.woff2'
import fontFreckleFace from './FreckleFace-Regular.woff2'
import fontGloock from './Gloock-Regular.woff2'
import fontPacha from './Pacha.woff2'
import fontGochiHand from './GochiHand-Regular.woff2'
import fontPassionOne from './PassionOne-Bold.woff2'
import fontPixeloidSans from './PixeloidSans.woff2'
import fontPlaypenSans from './PlaypenSans-Thin.woff2'
import fontQuicksandLight from './Quicksand-Light.woff2'
import fontSilkscreen from './Silkscreen-Regular.woff2'
import fontTehroc from './Tehroc-Regular.woff2'
import fontYunga from './YUNGA-Display.woff2'

export const fonts: {
  name: string
  url: string
}[] = [
  { name: 'Caveat', url: fontCaveat },
  { name: 'ClimateCrisis', url: fontClimateCrisis },
  { name: 'CorrectionBrush', url: fontCorrectionBrush },
  { name: 'DancingScript', url: fontDancingScript },
  { name: 'Diabolik', url: fontDiabolik },
  { name: 'FreckleFace', url: fontFreckleFace },
  { name: 'Gloock', url: fontGloock },
  { name: 'GochiHand', url: fontGochiHand },
  { name: 'Pacha', url: fontPacha },
  { name: 'PassionOne', url: fontPassionOne },
  { name: 'PixeloidSans', url: fontPixeloidSans },
  { name: 'PlaypenSans', url: fontPlaypenSans },
  { name: 'Quicksand', url: fontQuicksandLight },
  { name: 'Silkscreen', url: fontSilkscreen },
  { name: 'Tehroc', url: fontTehroc },
  { name: 'YUNGA', url: fontYunga }
]
