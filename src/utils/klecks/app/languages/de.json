{"switch-ui-left-right": "Werkzeuge links / rechts", "toggle-show-tools": "Werkzeuge Anzeigen/Verbergen", "scroll": "<PERSON><PERSON>", "donate": "<PERSON><PERSON><PERSON>", "home": "Home", "modal-new-tab": "In neuem Tab <PERSON>", "tab-edit": "Edit", "tab-file": "<PERSON><PERSON>", "tool-brush": "<PERSON><PERSON>l", "tool-paint-bucket": "Füllwerkzeug", "tool-gradient": "<PERSON><PERSON><PERSON><PERSON>", "tool-shape": "Form-Werkzeug", "tool-text": "Text-Werkzeug", "tool-hand": "Hand-Werkzeug", "tool-select": "Auswahl-Werkzeug", "tool-zoom": "Zoom-Werkzeug", "undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "redo": "Wiederherstellen", "brush-pen": "Stift", "brush-blend": "<PERSON><PERSON><PERSON><PERSON>", "brush-sketchy": "<PERSON><PERSON><PERSON>", "brush-pixel": "Pixel", "brush-chemy": "<PERSON><PERSON><PERSON>", "brush-smudge": "<PERSON><PERSON><PERSON>", "brush-size": "Größe", "brush-blending": "Mischung", "brush-toggle-pressure": "Drucksensitivität an/aus", "brush-pen-circle": "Kreis", "brush-pen-chalk": "<PERSON><PERSON><PERSON>", "brush-pen-calligraphy": "Kalligraphie", "brush-pen-square": "Quadrat", "brush-sketchy-scale": "<PERSON><PERSON><PERSON>", "brush-pixel-dither": "<PERSON><PERSON>", "brush-chemy-fill": "<PERSON><PERSON><PERSON>", "brush-chemy-stroke": "<PERSON><PERSON>", "brush-chemy-mirror-x": "Horizontale Symmetrie", "brush-chemy-mirror-y": "Vert<PERSON>le Symmetrie", "brush-chemy-gradient": "<PERSON><PERSON><PERSON><PERSON>", "brush-eraser-transparent-bg": "Transparenter Hintergrund", "stabilizer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stabilizer-title": "<PERSON>ien-Stabilisierer", "eyedropper": "Pipette", "secondary-color": "Sekundäre <PERSON>", "manual-color-input": "<PERSON><PERSON>", "mci-hex": "Hex", "mci-copy": "<PERSON><PERSON><PERSON>", "modal-ok": "Ok", "modal-cancel": "Abbrechen", "modal-close": "Schließen", "layers-active-layer": "Aktive Ebene", "layers-layer": "<PERSON><PERSON><PERSON>", "layers-copy": "<PERSON><PERSON>", "layers-blending": "Modus", "layers-new": "Neue Ebene", "layers-remove": "<PERSON><PERSON><PERSON>", "layers-duplicate": "<PERSON><PERSON><PERSON> du<PERSON>", "layers-merge": "Auf untere Ebene reduzieren", "layers-merge-all": "Alle Ebenen vereinen", "layers-rename": "Umbenennen", "layers-active-layer-visible": "Aktive Ebene ist eingeblendet", "layers-active-layer-hidden": "Aktive Ebene ist ausgeblendet", "layers-visibility-toggle": "Ebenensichtbarkeit", "layers-blend-normal": "Normal", "layers-blend-darken": "<PERSON><PERSON><PERSON><PERSON>", "layers-blend-multiply": "Multiplizieren", "layers-blend-color-burn": "Farbig nachbel.", "layers-blend-lighten": "Aufhellen", "layers-blend-screen": "Negativ multipl.", "layers-blend-color-dodge": "Farbig abwedeln", "layers-blend-overlay": "Überlagern", "layers-blend-soft-light": "Weiches Licht", "layers-blend-hard-light": "<PERSON>es Licht", "layers-blend-difference": "<PERSON><PERSON><PERSON><PERSON>", "layers-blend-exclusion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layers-blend-hue": "Farbton", "layers-blend-saturation": "Sättigung", "layers-blend-color": "Farbe", "layers-blend-luminosity": "Luminanz", "layers-rename-title": "<PERSON>bene umben<PERSON>n", "layers-rename-name": "Name", "layers-rename-clear": "<PERSON>n leeren", "layers-rename-sketch": "Skizze", "layers-rename-colors": "<PERSON><PERSON>", "layers-rename-shading": "Beleuchtung", "layers-rename-lines": "<PERSON><PERSON>", "layers-rename-effects": "Effekte", "layers-rename-foreground": "Vordergrund", "layers-merge-modal-title": "<PERSON><PERSON><PERSON>", "layers-merge-description": "Reduziert gewählte Ebene auf die darunter liegende. Wähle den Mischmodus:", "file-no-autosave": "Kein Autosave & Cloud-Speicher", "file-new": "<PERSON>eu", "file-import": "<PERSON><PERSON><PERSON>", "file-save": "Speichern", "file-format": "Dateiformat", "file-copy": "<PERSON><PERSON><PERSON>", "file-copy-title": "Kopieren in Zwischenablage", "file-share": "Teilen", "file-storage": "Browser-<PERSON><PERSON><PERSON><PERSON>", "file-storage-thumb-title": "Wiederhergestellt bei Neuladen", "file-storage-about": "Über Browser-Speicher", "file-storage-cant-access": "<PERSON><PERSON>", "file-storage-empty": "<PERSON><PERSON>", "file-storage-store": "Speichern", "file-storage-clear": "<PERSON><PERSON>", "file-storage-storing": "<PERSON><PERSON><PERSON><PERSON>", "file-storage-overwrite": "Überschreiben", "file-storage-min-ago": "vor {x} Min", "file-storage-hours-ago": "vor {x} h", "file-storage-days-ago": "vor {x} T", "file-storage-month-ago": "vor >1 Monat", "file-storage-restored": "W<PERSON>erhergestellt (Browser-Speicher)", "file-storage-stored": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Browser-Speicher)", "file-storage-failed": "Speichern fehlgeschlagen (Browser-Speicher)", "file-storage-failed-1": "Speichern fehlgeschlagen. Mögliche Gründe:", "file-storage-failed-2": "Ungenügend Speicherplatz vorhanden", "file-storage-failed-3": "Speicher deaktiviert in Inkognito-Tab", "file-storage-failed-4": "<PERSON><PERSON>er unterstützt Speicher nicht", "file-storage-failed-clear": "<PERSON><PERSON> feh<PERSON>.", "file-upload": "Hochladen", "cleared-layer": "<PERSON><PERSON><PERSON> gel<PERSON>", "cleared-selected-area": "Ausgewählte Fläche geleert", "filled": "<PERSON><PERSON><PERSON>", "filled-selected-area": "Auswahl gefüllt", "new-title": "Neues Bild", "new-current": "Aktuell", "new-fit": "Passen", "new-oversize": "<PERSON><PERSON><PERSON><PERSON>", "new-square": "Quadrat", "new-landscape": "Querformat", "new-portrait": "Hochformat", "new-screen": "Bildschirm", "new-video": "Video", "new-din-paper": "DIN Papier", "new-px": "Px", "new-ratio": "Verhältnis", "upload-title": "<PERSON><PERSON><PERSON><PERSON> auf Imgur", "upload-link-notice": "<PERSON><PERSON>, der den Link zu Deinem hochgeladenen Bild hat, kann es sehen.", "upload-name": "Titel", "upload-title-untitled": "<PERSON><PERSON>", "upload-caption": "Beschreibung", "upload-submit": "Hochladen", "upload-uploading": "<PERSON><PERSON><PERSON> hoch...", "upload-success": "Erfolgreich Hochgeladen", "upload-failed": "Hochladen fehlgeschlagen.", "upload-delete": "Um dein Bild zu löschen öffne folgenden Link:", "cropcopy-title-copy": "Kopieren in Zwischenablage", "cropcopy-title-crop": "zuschneiden", "cropcopy-click-hold": "Rechtsklick oder gedrückt halten für kopieren.", "cropcopy-btn-copy": "In Zwischenablage", "cropcopy-copied": "<PERSON><PERSON><PERSON>.", "cropcopy-btn-crop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crop-drag-to-crop": "Ziehen um zuzuschneiden", "filter-crop-extend": "Arbeitsfläche", "filter-flip": "<PERSON><PERSON><PERSON><PERSON>", "filter-perspective": "Perspektive", "filter-resize": "Skalieren", "filter-rotate": "<PERSON><PERSON><PERSON>", "filter-transform": "Transformier.", "filter-bright-contrast": "Hell/Kontrast", "filter-curves": "<PERSON><PERSON><PERSON>", "filter-hue-sat": "Farbe/Sättig", "filter-invert": "Umkehren", "filter-tilt-shift": "Tilt Shift", "filter-to-alpha": "Zu Alpha", "filter-triangle-blur": "Weichzeichnen", "filter-unsharp-mask": "Un<PERSON><PERSON>f mask", "filter-crop-title": "Arbeitsfläche", "filter-crop-description": "Schneidet das Bild zu oder erweitert es.", "filter-crop-left": "Links", "filter-crop-right": "<PERSON><PERSON><PERSON>", "filter-crop-top": "<PERSON><PERSON>", "filter-crop-bottom": "Unten", "filter-crop-rule-thirds": "Drittel-<PERSON><PERSON>", "filter-crop-fill": "Füllung", "filter-flip-title": "<PERSON><PERSON><PERSON><PERSON>", "filter-flip-description": "Spiegelt Ebene oder gesamtes Bild.", "filter-flip-horizontal": "Horizontal", "filter-flip-vertical": "Vertikal", "filter-flip-image": "Bild", "filter-flip-layer": "<PERSON><PERSON><PERSON>", "filter-perspective-title": "Perspektive", "filter-perspective-description": "Transformiert die gewählte Ebene.", "filter-resize-title": "Skalieren", "filter-resize-description": "Ändert die Größe des Bildes.", "filter-rotate-title": "<PERSON><PERSON><PERSON>", "filter-rotate-description": "<PERSON><PERSON><PERSON> das Bild.", "filter-transform-empty": "<PERSON>bene ist leer.", "filter-transform-title": "Transformieren", "filter-transform-description": "Transformiert die gewählte Ebene. Halte Shift für erweiterte Funktionalität.", "filter-transform-rotation": "Rotation", "filter-transform-flip": "<PERSON><PERSON><PERSON>", "filter-transform-center": "Zentrieren", "filter-transform-constrain": "Fest", "filter-transform-snap": "<PERSON><PERSON><PERSON>", "filter-transform-snap-title": "Rotation und Position einrasten", "filter-bright-contrast-title": "Helligkeit / Kontrast", "filter-bright-contrast-description": "Ändert Helligkeit und Kontrast der gewählten Ebene.", "filter-bright-contrast-brightness": "Helligkeit", "filter-bright-contrast-contrast": "<PERSON><PERSON><PERSON><PERSON>", "filter-curves-title": "<PERSON><PERSON><PERSON>", "filter-curves-description": "<PERSON><PERSON> Kurven auf gewählte Ebene an.", "filter-curves-all": "Alle", "filter-hue-sat-title": "Farbton / Sättigung", "filter-hue-sat-description": "Ändert Farbton und Sättigung der gewählten Ebene.", "filter-hue-sat-hue": "Farbton", "filter-hue-sat-saturation": "Sättigung", "filter-applied": "an<PERSON><PERSON><PERSON>", "filter-tilt-shift-title": "Tilt Shift", "filter-tilt-shift-description": "Wendet Tilt Shift auf gewählte Ebene an.", "filter-tilt-shift-blur": "Unschärferadius", "filter-tilt-shift-gradient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter-to-alpha-title": "Zu Alpha", "filter-to-alpha-description": "Erzeugt Alphakanal für gewählte Ebene aus:", "filter-to-alpha-inverted-lum": "Umgekehrte Luminanz", "filter-to-alpha-lum": "Luminanz", "filter-to-alpha-replace": "Ersetze RGB", "filter-triangle-blur-title": "Dreiecksunschärfe", "filter-triangle-blur-description": "<PERSON><PERSON> Dreiecksunschärfe auf gewählte Ebene an.", "filter-unsharp-mask-title": "<PERSON><PERSON><PERSON><PERSON> mask<PERSON>en", "filter-unsharp-mask-description": "Schärft die ausgewählte Ebene durch Skalierung der Pixel weg vom Durchschnitt ihrer Nachbarn.", "filter-unsharp-mask-strength": "Stärke", "filter-grid": "Gitter", "filter-grid-description": "<PERSON><PERSON><PERSON><PERSON> Gitter auf gewählte Ebene.", "filter-noise": "<PERSON><PERSON><PERSON>", "filter-noise-description": "<PERSON><PERSON><PERSON><PERSON> Rauschen auf gewählte Ebene.", "filter-noise-scale": "Größe", "filter-noise-alpha": "Alpha", "filter-pattern": "Muster", "filter-pattern-description": "<PERSON><PERSON><PERSON><PERSON> auf aktiver Ebene. Ziehe in der Vorschau um weitere Parameter zu steuern.", "filter-distort": "Verzerren", "filter-distort-description": "Verzerrt aktive Ebene.", "filter-distort-phase": "Phase", "filter-distort-stepsize": "Schrittgröße", "filter-distort-sync-xy": "XY koppeln", "filter-vanish-point": "Fluchtpunkt", "filter-vanish-point-title": "Fluchtpunkt", "filter-vanish-point-description": "Zeichnet Fluchtpunkt auf aktive Ebene. Ziehe Vorschau zum Verschieben.", "filter-vanish-point-lines": "<PERSON><PERSON>", "import-opening": "<PERSON><PERSON><PERSON>...", "import-title": "<PERSON><PERSON>", "import-too-large": "<PERSON><PERSON>d zu groß, es wird verkleinert.", "import-btn-as-layer": "Als Ebene", "import-btn-as-image": "Als Bild", "import-as-layer-title": "Öffne Bild als neue Ebene", "import-as-layer-description": "Passe die Position des importierten Bildes an.", "import-as-layer-limit-reached": "Maximale Ebenen erreicht, es wird auf vorhandene Ebene platz<PERSON>t.", "import-as-layer-fit": "Passen", "import-flatten": "Reduzieren", "import-unsupported-file": "Nicht unterstützter Dateityp. Siehe Hilfe für unterstützte Typen.", "import-broken-file": "Bild konnte nicht geladen werden. Datei könnte beschädigt sein.", "import-psd-unsupported": "Nicht unterstützte Funktionen. PSD musste auf eine Ebene reduziert werden.", "import-psd-limited-support": "Begrenzte Unterstützung für PSD. Reduziertes Bild sieht vermutlich korrekter aus.", "import-psd-too-large": "Bild überschreitet Maximum von {x} x {x} <PERSON><PERSON><PERSON>, kann nicht geöffnet werden.", "import-psd-size": "Bildgröße", "hand-reset": "Z<PERSON>ücksetzen", "hand-fit": "Passen", "hand-inertia-scrolling": "Trägheits-Scrollen", "bucket-tolerance": "To<PERSON>anz", "bucket-sample": "<PERSON><PERSON>", "bucket-sample-title": "Von welchen Ebenen wird Farbe gelesen", "bucket-sample-all": "Alle", "bucket-sample-active": "Aktiv", "bucket-sample-above": "<PERSON><PERSON>", "bucket-grow": "<PERSON><PERSON><PERSON>", "bucket-grow-title": "Erweitere gefüllten Bereich (in Pixel)", "bucket-contiguous": "Benachbart", "bucket-contiguous-title": "<PERSON><PERSON><PERSON> nur miteinander verbundene Bereiche", "gradient-linear": "Linear", "gradient-linear-mirror": "Linear Gespiegelt", "gradient-radial": "Radial", "shape-stroke": "<PERSON><PERSON>", "shape-fill": "<PERSON><PERSON><PERSON>", "shape-rect": "<PERSON><PERSON><PERSON>", "shape-ellipse": "Ellipse", "shape-line": "<PERSON><PERSON>", "shape-line-width": "Linienbreite", "shape-outwards": "<PERSON><PERSON>", "shape-fixed": "Fest 1:1", "text-instruction": "Klicken um Text zu platzieren", "text-title": "Text Hinzufügen", "text-text": "Text", "text-font": "Sc<PERSON><PERSON>", "text-placeholder": "<PERSON><PERSON> Text", "text-color": "Farbe", "text-size": "Größe", "text-line-height": "Zeilenhöhe", "text-letter-spacing": "Zeichenabstand", "text-left": "Linksbündig", "text-center": "<PERSON><PERSON><PERSON>", "text-right": "Rechtsbündig", "text-italic": "<PERSON><PERSON><PERSON>", "text-bold": "<PERSON><PERSON>", "select-select": "Auswahl", "select-transform": "Transformier.", "select-lasso": "<PERSON><PERSON>", "select-polygon": "Polygon", "select-boolean-replace": "<PERSON><PERSON><PERSON><PERSON>", "select-boolean-add": "Hinzufügen", "select-boolean-subtract": "<PERSON><PERSON><PERSON><PERSON>", "select-all": "Alles", "select-invert": "Umkehren", "select-reset": "Z<PERSON>ücksetzen", "select-fill": "<PERSON><PERSON><PERSON>", "select-erase": "<PERSON><PERSON>", "select-transform-clone": "Klonen", "select-transform-clone-applied": "Geklont", "select-transform-move-to-layer": "Auf Ebene verschieben:", "select-transform-applied": "Transformation angewendet", "select-transform-empty": "Die ausgewählte Fläche der Ebene ist leer.", "save-reminder-title": "Ungespeicherter Fortschritt", "save-reminder-text": "Dein Bild wurde seit {a} Minuten{b} nicht gespeichert. Speichere jetzt um Datenverlust zu vermeiden.", "save-reminder-save-psd": "Speichere als PSD", "save-reminder-psd-layers": "PSD merkt sich alle Ebenen.", "backup-drawing": "Du kannst dein Bild lokal sichern.", "submit": "Senden", "submit-title": "Bild senden", "submit-prompt": "Bild senden?", "submit-submitting": "<PERSON><PERSON><PERSON> hoch", "embed-init-loading": "Lade App", "embed-init-waiting": "<PERSON><PERSON> auf Bild", "unsaved": "Ungespeichert", "help": "<PERSON><PERSON><PERSON>", "tab-settings": "Einstellungen", "settings-language": "<PERSON><PERSON><PERSON>", "settings-language-reload": "Aktualisiert mit Neuladen.", "settings-theme": "Farbe", "settings-save-reminder-label": "Speicher-Erinnerung", "settings-save-reminder-disabled": "<PERSON>ak<PERSON><PERSON><PERSON>", "settings-save-reminder-confirm-title": "Speicher-Erinnerung deaktivieren?", "settings-save-reminder-confirm-a": "Es gibt kein Autosave und Browser Tabs existieren nicht für ewig. Wenn du nicht regelmäßig speicherst, verlierst du wahrscheinlich deinen Fortschritt.", "settings-save-reminder-confirm-b": "Auf eigene Gefahr ausschalten?", "settings-save-reminder-confirm-disable": "Deaktivieren", "theme-dark": "<PERSON><PERSON><PERSON>", "theme-light": "Hell", "terms-of-service": "Nutzungsbedingungen", "licenses": "<PERSON><PERSON><PERSON>", "source-code": "Quellcode", "auto": "Automatisch", "zoom-in": "Vergrößern", "zoom-out": "Verkleinern", "radius": "<PERSON><PERSON>", "constrain-proportions": "Festes Seitenverhältnis", "width": "Breite", "height": "<PERSON><PERSON><PERSON>", "opacity": "Deckkraft", "red": "Rot", "green": "<PERSON><PERSON><PERSON><PERSON>", "blue": "Blau", "eraser": "<PERSON><PERSON><PERSON>", "center": "Zentrieren", "layers": "<PERSON><PERSON><PERSON>", "background": "Hi<PERSON>grund", "scaling-algorithm": "Skalierungsalgorithmus", "algorithm-smooth": "<PERSON><PERSON>", "algorithm-pixelated": "Pixelig", "preview": "Vorschau", "angle-snap": "<PERSON><PERSON><PERSON>", "angle-snap-title": "45° einrasten", "lock-alpha": "Fixieren", "lock-alpha-title": "<PERSON><PERSON><PERSON>-Kanal der Ebene", "reverse": "Umkehren", "compare-before": "<PERSON><PERSON><PERSON>", "compare-after": "<PERSON><PERSON>", "loading": "<PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON>", "x-minutes": "{x} Min", "wip": "In Arbeit", "browser-zoom-help": "Doppeltippen oder Finger auseinanderziehen, um den Browser-Zoom zurückzusetzen.", "dismiss": "Schließen"}