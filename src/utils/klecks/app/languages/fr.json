{"switch-ui-left-right": "Basculer gauche / droite", "toggle-show-tools": "Afficher / Masquer les Outils", "scroll": "<PERSON><PERSON><PERSON><PERSON>", "donate": "Don", "home": "Accueil", "modal-new-tab": "<PERSON><PERSON><PERSON><PERSON>r dans un nouvel onglet", "tab-edit": "Modifier", "tab-file": "<PERSON><PERSON><PERSON>", "tool-brush": "<PERSON><PERSON><PERSON>", "tool-paint-bucket": "Pot de peinture", "tool-gradient": "<PERSON><PERSON><PERSON><PERSON>", "tool-shape": "Forme", "tool-text": "Texte", "tool-hand": "Outil <PERSON>", "tool-zoom": "Zoom", "undo": "Annuler", "redo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brush-pen": "<PERSON><PERSON><PERSON>", "brush-blend": "Fusion", "brush-sketchy": "C<PERSON><PERSON>", "brush-pixel": "Pixel", "brush-chemy": "<PERSON><PERSON>", "brush-smudge": "Outil doigt", "brush-size": "<PERSON><PERSON>", "brush-blending": "<PERSON><PERSON><PERSON><PERSON>", "brush-toggle-pressure": "Basculer la sensibilité de la pression", "brush-pen-circle": "Cercle", "brush-pen-chalk": "<PERSON><PERSON><PERSON>", "brush-pen-calligraphy": "Calligraphie", "brush-pen-square": "Carré", "brush-sketchy-scale": "<PERSON><PERSON><PERSON>", "brush-pixel-dither": "Trame", "brush-chemy-fill": "<PERSON><PERSON><PERSON><PERSON>", "brush-chemy-mirror-x": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "brush-chemy-mirror-y": "<PERSON><PERSON><PERSON><PERSON><PERSON> Verticale", "brush-chemy-gradient": "<PERSON><PERSON><PERSON><PERSON>", "brush-eraser-transparent-bg": "Fond Transparent", "stabilizer": "Stabilisateur", "stabilizer-title": "Stabilisateur de Ligne", "eyedropper": "Pipette", "secondary-color": "Couleur Secondaire", "manual-color-input": "<PERSON><PERSON> des Couleurs", "mci-copy": "<PERSON><PERSON><PERSON>", "modal-ok": "Ok", "modal-cancel": "Annuler", "modal-close": "<PERSON><PERSON><PERSON>", "layers-active-layer": "Calque Active", "layers-layer": "Calque", "layers-copy": "<PERSON><PERSON><PERSON>", "layers-blending": "<PERSON><PERSON><PERSON><PERSON>", "layers-new": "Nouveau Calque", "layers-remove": "Supprimé un calque", "layers-duplicate": "<PERSON><PERSON><PERSON><PERSON> le Calque", "layers-merge": "Fusionner avec le calque ci-dessous", "layers-rename": "<PERSON>mmer", "layers-active-layer-visible": "Calque active visible", "layers-active-layer-hidden": "Calque active masqué", "layers-visibility-toggle": "Visibilité des calques", "layers-blend-normal": "normal", "layers-blend-darken": "obscurcir", "layers-blend-multiply": "produit", "layers-blend-color-burn": "densité couleur +", "layers-blend-lighten": "<PERSON><PERSON>lair<PERSON><PERSON>", "layers-blend-screen": "superposition", "layers-blend-color-dodge": "densité couleur -", "layers-blend-overlay": "incrustation", "layers-blend-soft-light": "lumière tamisée", "layers-blend-hard-light": "lumière crue", "layers-blend-difference": "différence", "layers-blend-exclusion": "exclusion", "layers-blend-hue": "teinte", "layers-blend-saturation": "saturation", "layers-blend-color": "couleur", "layers-blend-luminosity": "luminosité", "layers-rename-title": "Renommer le Calque", "layers-rename-name": "Nom", "layers-rename-clear": "Nom <PERSON>", "layers-rename-sketch": "C<PERSON><PERSON>", "layers-rename-colors": "Couleurs", "layers-rename-shading": "Ombrage", "layers-rename-lines": "Ligne", "layers-rename-effects": "<PERSON><PERSON><PERSON>", "layers-rename-foreground": "Premier Plan", "layers-merge-modal-title": "Fusionner / Mélanger des Calques", "layers-merge-description": "Fusionne le calque sélectionné avec celui du dessous. Sélectionnez le mode de mixage:", "file-no-autosave": "Pas de sauvegarde automatique, pas de cloud", "file-new": "Nouveau", "file-import": "Importer", "file-save": "Enregistrer", "file-format": "Format du Fichier", "file-copy": "<PERSON><PERSON><PERSON>", "file-copy-title": "<PERSON><PERSON><PERSON> Le Presse-Papiers", "file-share": "Partager", "file-storage": "Stockage du navigateur", "file-storage-thumb-title": "Récupération lors du rechargement de la page.", "file-storage-about": "A propos du stockage du navigateur", "file-storage-cant-access": "Ne peut pas avoir accès", "file-storage-empty": "Vide", "file-storage-store": "Conserver", "file-storage-clear": "<PERSON><PERSON><PERSON><PERSON>", "file-storage-storing": "Stockage", "file-storage-overwrite": "<PERSON><PERSON><PERSON><PERSON>", "file-storage-min-ago": "il y a {x}min", "file-storage-hours-ago": "il y a {x}h", "file-storage-days-ago": "il y a {x} jours", "file-storage-month-ago": "> il y a un mois", "file-storage-restored": "Restauré à partir du stockage du Navigateur", "file-storage-stored": "Stocké dans le Stockage du Navigateur", "file-storage-failed": "Échec de l'enregistrement dans le stockage du navigateur", "file-storage-failed-1": "Impossible de stocker. Causes possibles:", "file-storage-failed-2": "Manque d'espace disque", "file-storage-failed-3": "Stockage désactivé dans l'onglet Navigation privée", "file-storage-failed-4": "Navigateur ne supporte pas le stockage", "file-storage-failed-clear": "Échec de l'effacement.", "file-upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cleared-layer": "<PERSON>que effacée", "filled": "<PERSON><PERSON><PERSON><PERSON>", "new-title": "Nouvelle Image", "new-current": "Actuel", "new-fit": "<PERSON><PERSON><PERSON>", "new-oversize": "Surdimensionnée", "new-square": "Carré", "new-landscape": "Paysage", "new-portrait": "Portrait", "new-screen": "É<PERSON>ran", "new-video": "Vidéo", "new-din-paper": "Papier DIN", "new-px": "px", "new-ratio": "Format", "upload-title": "Publier sur Imgur", "upload-link-notice": "Toute personne disposant du lien vers votre image téléchargée pourra la visualiser.", "upload-name": "Titre", "upload-title-untitled": "Sans Titre", "upload-caption": "Légende", "upload-submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "upload-uploading": "Téléversement...", "upload-success": "Téléversement Réussi", "upload-failed": "Le téléversement a échoué.", "upload-delete": "Pour supprimer votre image d'<PERSON><PERSON><PERSON><PERSON>, visitez:", "cropcopy-title-copy": "<PERSON><PERSON><PERSON> Le Presse-Papiers", "cropcopy-title-crop": "<PERSON><PERSON><PERSON>", "cropcopy-click-hold": "Clique-droit ou rester appuyé pour copier.", "cropcopy-btn-copy": "<PERSON><PERSON><PERSON>", "cropcopy-copied": "<PERSON><PERSON><PERSON>.", "cropcopy-btn-crop": "<PERSON><PERSON><PERSON><PERSON>", "crop-drag-to-crop": "Glisser pour recadrer", "filter-crop-extend": "<PERSON><PERSON><PERSON><PERSON>", "filter-flip": "<PERSON><PERSON><PERSON>", "filter-perspective": "Perspective", "filter-resize": "Redimensionner", "filter-rotate": "Pivoter", "filter-transform": "Transformer", "filter-bright-contrast": "Lum / Contraste", "filter-curves": "<PERSON><PERSON><PERSON>", "filter-hue-sat": "Teinte/Satur.", "filter-invert": "Inverser", "filter-tilt-shift": "Tilt Shift", "filter-to-alpha": "Vers Alpha", "filter-triangle-blur": "Flou <PERSON>", "filter-unsharp-mask": "<PERSON><PERSON>", "filter-crop-title": "Recadrer / Étendre", "filter-crop-description": "Reca<PERSON>z ou étendez l'image.", "filter-crop-left": "G<PERSON><PERSON>", "filter-crop-right": "<PERSON><PERSON><PERSON>", "filter-crop-top": "<PERSON><PERSON>", "filter-crop-bottom": "Bas", "filter-crop-rule-thirds": "<PERSON><PERSON><PERSON> des Tiers", "filter-crop-fill": "<PERSON><PERSON><PERSON><PERSON>", "filter-flip-title": "<PERSON><PERSON><PERSON>", "filter-flip-description": "<PERSON><PERSON><PERSON> le calque ou l'image entière.", "filter-flip-horizontal": "Horizontalement", "filter-flip-vertical": "Verticalement", "filter-flip-image": "Image", "filter-flip-layer": "Calque", "filter-perspective-description": "Transforme le calque sélectionné.", "filter-resize-title": "Redimensionner", "filter-resize-description": "Redimensionne l'image.", "filter-rotate-title": "<PERSON><PERSON>", "filter-rotate-description": "Faire pivoter l'image.", "filter-transform-empty": "Le calque est vide.", "filter-transform-title": "Transformer", "filter-transform-description": "Transforme le calque sélectionné. Maintenez la touche Shift enfoncée pour un comportement supplémentaire.", "filter-transform-rotation": "Rotation", "filter-transform-flip": "<PERSON><PERSON><PERSON>", "filter-transform-center": "Centre", "filter-transform-constrain": "Contraindre", "filter-transform-snap-title": "Rotation Et Position Du Bouton-Pression", "filter-bright-contrast-title": "Luminosité / Contraste", "filter-bright-contrast-description": "Modifiez la luminosité et le contraste du calque sélectionné.", "filter-bright-contrast-brightness": "Luminosité", "filter-bright-contrast-contrast": "Contraste", "filter-curves-title": "<PERSON><PERSON><PERSON>", "filter-curves-description": "Appliquez des courbes sur le calque sélectionné.", "filter-curves-all": "<PERSON>ut", "filter-hue-sat-title": "Teinte / Saturation", "filter-hue-sat-description": "Modifiez la teinte et la saturation du calque sélectionné.", "filter-hue-sat-hue": "Teinte", "filter-hue-sat-saturation": "Saturation", "filter-applied": "appliquer", "filter-tilt-shift-title": "Décalage d'Inclinaison", "filter-tilt-shift-description": "Applique le décalage d'inclinaison sur le calque sélectionné.", "filter-tilt-shift-blur": "Rayon de Flou", "filter-tilt-shift-gradient": "Rayon de Gradient", "filter-to-alpha-title": "Vers Alpha", "filter-to-alpha-description": "Généré un canal alpha pour le calque sélectionné:", "filter-to-alpha-inverted-lum": "Luminance Inversée", "filter-to-alpha-lum": "Luminance", "filter-to-alpha-replace": "Remplacer RGB", "filter-triangle-blur-title": "Flou Triangle", "filter-triangle-blur-description": "Applique un flou triangulaire sur le calque sélectionné.", "filter-unsharp-mask-title": "<PERSON><PERSON>", "filter-unsharp-mask-description": "Accentue le calque sélectionné en décalant les pixels de la moyenne de leurs voisins.", "filter-unsharp-mask-strength": "Force", "filter-grid": "Grille", "filter-grid-description": "Trace une grille sur le calque choisi.", "filter-noise": "Bruit", "filter-noise-description": "Ajouter du bruit au calque sélectionné.", "filter-noise-scale": "<PERSON><PERSON><PERSON>", "filter-noise-alpha": "Alpha", "filter-pattern": "<PERSON><PERSON><PERSON>", "filter-pattern-description": "Crée un motif sur le calque choisi. Faites glisser l'aperçu pour plus de contrôle.", "filter-distort": "Déformer", "filter-distort-description": "Déformer le calque sélectionné.", "filter-distort-phase": "Phase", "filter-distort-stepsize": "Taille de l'Étape", "filter-distort-sync-xy": "Synchroniser XY", "filter-vanish-point": "Point de Fuite", "filter-vanish-point-title": "Point de Fuite", "filter-vanish-point-description": "Ajoute un point de fuite au calque sélectionné. Faites glisser l'aperçu pour vous déplacer.", "filter-vanish-point-lines": "<PERSON><PERSON><PERSON>", "import-opening": "Ouverture du fichier...", "import-title": "Importer une Image", "import-too-large": "Image trop grande, devrait être réduite.", "import-btn-as-layer": "En calques", "import-btn-as-image": "En image", "import-as-layer-title": "Importer l'Image en tant que nouveau Calque", "import-as-layer-description": "Ajustez la position de l'image importée.", "import-as-layer-limit-reached": "Limite de couche atteinte. L'image sera placée sur le calque existant.", "import-as-layer-fit": "Adapter", "import-flatten": "Aplatir l'image", "import-unsupported-file": "Type de fichier non pris en charge. Voir l'aide pour les types pris en charge.", "import-broken-file": "Impossible de charger l'image. Le fichier peut être corrompu.", "import-psd-unsupported": "Fonctionnalités non prises en charge. PSD a dû être aplati.", "import-psd-limited-support": "Le support PSD est limité. Aplati aura plus probablement l'air correct.", "import-psd-too-large": "L'image dépasse les dimensions maximales de {x} x {x} pixels. Impossible d'importer.", "import-psd-size": "<PERSON>lle de l'image", "hand-reset": "Réinitialiser", "hand-fit": "Adapter", "bucket-tolerance": "Tolérance", "bucket-sample": "Échantillon", "bucket-sample-title": "A quelles couches échantillonner la couleur", "bucket-sample-all": "Tous", "bucket-sample-above": "<PERSON><PERSON><PERSON><PERSON>", "bucket-grow": "<PERSON><PERSON><PERSON>", "bucket-grow-title": "Agrandir la zone remplie (en pixels)", "bucket-contiguous": "Contiguë", "bucket-contiguous-title": "Ne remplissez que les zones connectées", "gradient-linear": "Linéaire", "gradient-linear-mirror": "Linéaire-Miroir", "gradient-radial": "Radial", "shape-stroke": "<PERSON>tour", "shape-fill": "<PERSON><PERSON><PERSON><PERSON>", "shape-rect": "Rectangle", "shape-ellipse": "Ellipse", "shape-line": "Ligne", "shape-line-width": "<PERSON><PERSON>", "shape-outwards": "Extérieur", "shape-fixed": "Fixé 1:1", "text-instruction": "Cliquez sur le canevas pour placer le texte", "text-title": "<PERSON><PERSON><PERSON> du Texte", "text-placeholder": "Votre texte", "text-color": "<PERSON><PERSON><PERSON>", "text-size": "<PERSON><PERSON>", "text-left": "G<PERSON><PERSON>", "text-center": "Centre", "text-right": "<PERSON><PERSON><PERSON>", "text-italic": "Italique", "text-bold": "Gras", "save-reminder-title": "Travail non sauvé", "save-reminder-text": "L'image n'a pas été sauvegardée en {a} minutes{b}. Enregistrez-la maintenant pour éviter toute perte éventuelle.", "save-reminder-save-psd": "Enregistrer en tant que PSD", "save-reminder-psd-layers": "PSD se souviendra de toutes les couches.", "backup-drawing": "V<PERSON> pouvez sauvegarder votre dessin.", "submit": "Envoyer", "submit-title": "<PERSON><PERSON><PERSON><PERSON>", "submit-prompt": "Soumettre Un Dessin?", "submit-submitting": "Soumission", "embed-init-loading": "Chargement de l'application", "embed-init-waiting": "En Attente de L'image", "unsaved": "<PERSON><PERSON>", "help": "Aide", "tab-settings": "Paramètres", "settings-language": "<PERSON><PERSON>", "settings-language-reload": "Mettra à jour après le rechargement.", "terms-of-service": "Conditions d'utilisation", "licenses": "Licence", "source-code": "Code Source", "auto": "auto", "zoom-in": "<PERSON>m <PERSON>nt", "zoom-out": "Zoom <PERSON>", "radius": "Rayon", "constrain-proportions": "Proportions Contraintes", "width": "<PERSON><PERSON>", "height": "<PERSON><PERSON>", "opacity": "Opacité", "red": "Rouge", "green": "<PERSON>ert", "blue": "Bleu", "eraser": "<PERSON><PERSON>", "center": "Centre", "layers": "Calques", "background": "Arrière Plan", "scaling-algorithm": "Algorithme de Mise à l'Échelle", "algorithm-smooth": "Lisse", "algorithm-pixelated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "angle-snap": "<PERSON><PERSON>", "angle-snap-title": "Encliquetage à angle de 45°", "lock-alpha": "Verrou Alpha", "lock-alpha-title": "Verrou Alpha du Calques", "reverse": "Inverser", "compare-before": "Avant", "compare-after": "<PERSON><PERSON>", "x-minutes": "{x}min"}