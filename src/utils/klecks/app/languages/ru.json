{"switch-ui-left-right": "Меню влево/вправо", "toggle-show-tools": "Показать/скрыть инструменты", "scroll": "Прокрутка", "donate": "Пожертвовать", "home": "Главная страница", "modal-new-tab": "Открыть в новой вкладке", "tab-edit": "Изменить", "tab-file": "<PERSON>а<PERSON><PERSON>", "tool-brush": "Кисть", "tool-paint-bucket": "Заливка", "tool-gradient": "Град<PERSON><PERSON><PERSON><PERSON>", "tool-shape": "Форма", "tool-text": "Текст", "tool-hand": "Инструмент «Рука»", "tool-zoom": "Приблизить/отдалить", "undo": "Отмена", "redo": "Повторить действие", "brush-pen": "Ручка", "brush-blend": "Переход", "brush-sketchy": "Эскиз", "brush-pixel": "Пиксели", "brush-chemy": "Графика особой формы", "brush-smudge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "brush-size": "Размер", "brush-blending": "Наложение", "brush-toggle-pressure": "Переключение чувствительности давления", "brush-pen-circle": "Круг", "brush-pen-chalk": "<PERSON><PERSON><PERSON>", "brush-pen-calligraphy": "Каллиграфия", "brush-pen-square": "Ква<PERSON><PERSON><PERSON><PERSON>", "brush-sketchy-scale": "Масштабирование", "brush-pixel-dither": "Диз<PERSON><PERSON><PERSON><PERSON><PERSON>", "brush-chemy-fill": "Заливка", "brush-chemy-stroke": "Обводка", "brush-chemy-mirror-x": "Горизонтальная симметрия", "brush-chemy-mirror-y": "Вертикальная симметрия", "brush-chemy-gradient": "Град<PERSON><PERSON><PERSON><PERSON>", "brush-eraser-transparent-bg": "Прозрачный фон", "stabilizer": "Стабилизатор", "stabilizer-title": "Стабилизатор обводки", "eyedropper": "Пипетка", "secondary-color": "Вторичный цвет", "manual-color-input": "Ввод цвета вручную", "mci-hex": "Шестнадцатеричный", "mci-copy": "Скопировать", "modal-ok": "OK", "modal-cancel": "Отмена", "modal-close": "Закрыть", "layers-active-layer": "Активный слой", "layers-layer": "Слой", "layers-copy": "копия", "layers-blending": "Наложение", "layers-new": "Новый слой", "layers-remove": "Удалить слой", "layers-duplicate": "Копировать слой", "layers-merge": "Объединить со слоём ниже", "layers-merge-all": "Объединить все", "layers-rename": "Переименовать", "layers-active-layer-visible": "Активный слой видимый", "layers-active-layer-hidden": "Активный слой скрыт", "layers-visibility-toggle": "Видимость слоя", "layers-blend-normal": "обычный", "layers-blend-darken": "затемнение", "layers-blend-multiply": "умножение", "layers-blend-color-burn": "затемнение основы", "layers-blend-lighten": "осветление", "layers-blend-screen": "экранирование", "layers-blend-color-dodge": "осветление основы", "layers-blend-overlay": "перекрытие", "layers-blend-soft-light": "мягкий свет", "layers-blend-hard-light": "жёсткий свет", "layers-blend-difference": "разница", "layers-blend-exclusion": "исключение", "layers-blend-hue": "цветовой тон", "layers-blend-saturation": "насыщенность", "layers-blend-color": "цветность", "layers-blend-luminosity": "светимость", "layers-rename-title": "Переименовать слой", "layers-rename-name": "Имя", "layers-rename-clear": "Очистить имя", "layers-rename-sketch": "Эскиз", "layers-rename-colors": "Цвета", "layers-rename-shading": "Затенение", "layers-rename-lines": "Линии", "layers-rename-effects": "Эффекты", "layers-rename-foreground": "Передний план", "layers-merge-modal-title": "Объединение/смешивание слоёв", "layers-merge-description": "Объединяет выбранный слой с тем, который находится под ним. Выберите режим смешивания:", "file-no-autosave": "Нет автосохранения, нет облачного хранилища", "file-new": "Новый", "file-import": "Импорт", "file-save": "Сохранить", "file-format": "Формат файла", "file-copy": "Скопировать", "file-copy-title": "Скопировать в буфер обмена", "file-share": "Поделиться", "file-storage": "Хранилище браузера", "file-storage-thumb-title": "Восстанавливается при повторном открытии страницы", "file-storage-about": "О хранилище браузера", "file-storage-cant-access": "Не удаётся получить доступ", "file-storage-empty": "Пусто", "file-storage-store": "Сохранить", "file-storage-clear": "Очистить", "file-storage-storing": "Сохранение", "file-storage-overwrite": "Перезаписать", "file-storage-min-ago": "{x} мин. назад", "file-storage-hours-ago": "{x} ч. назад", "file-storage-days-ago": "{x} дн. назад", "file-storage-month-ago": "> 1 месяц назад", "file-storage-restored": "Восстановлено из хранилища браузера", "file-storage-stored": "Сохранено в хранилище браузера", "file-storage-failed": "Не удалось сохранить в хранилище браузера", "file-storage-failed-1": "Не удалось сохранить. Возможные причины:", "file-storage-failed-2": "Не хватает дискового пространства", "file-storage-failed-3": "Хранилище отключено во вкладке инкогнито", "file-storage-failed-4": "Браузер не поддерживает хранилище", "file-storage-failed-clear": "Не удалось очистить.", "file-upload": "Загрузить", "cleared-layer": "Очищенный слой", "filled": "Заполненный", "new-title": "Новое изображение", "new-current": "Текущее", "new-fit": "Подгонка", "new-oversize": "Огромная", "new-square": "Ква<PERSON><PERSON><PERSON><PERSON>", "new-landscape": "Альбомная", "new-portrait": "Книжная", "new-screen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "new-video": "Видео", "new-din-paper": "Формат бумаги", "new-px": "пикс.", "new-ratio": "Соотношение", "upload-title": "Загрузить на Imgur", "upload-link-notice": "<PERSON><PERSON><PERSON><PERSON><PERSON>, у кого есть ссылка на загруженное вами изображение, сможет его просмотреть.", "upload-name": "Название", "upload-title-untitled": "Безымянный", "upload-caption": "Подпись", "upload-submit": "Загрузить", "upload-uploading": "Загрузка...", "upload-success": "Загрузка удалась", "upload-failed": "Загрузка не удалась.", "upload-delete": "Чтобы удалить своё изображение с Imgur, посетите сайт:", "cropcopy-title-copy": "Скопировать в буфер обмена", "cropcopy-title-crop": "Рамка", "cropcopy-click-hold": "Нажмите правую кнопку мыши или нажмите и удерживайте, чтобы скопировать.", "cropcopy-btn-copy": "В буфер обмена", "cropcopy-copied": "Скопировано.", "cropcopy-btn-crop": "Применение обрезки", "crop-drag-to-crop": "Перетащите для кадрирования", "filter-crop-extend": "Кадрир./растягив.", "filter-flip": "Перевернуть", "filter-perspective": "Перспектива", "filter-resize": "Изм. размера", "filter-rotate": "Поворот", "filter-transform": "Трансформ.", "filter-bright-contrast": "Ярк./Контраст.", "filter-curves": "Кривые", "filter-hue-sat": "Цв. тон/Насыщ.", "filter-invert": "Инверсия", "filter-tilt-shift": "Глуб. резкости", "filter-to-alpha": "Прозрачность", "filter-triangle-blur": "Треуг. размытие", "filter-unsharp-mask": "Нерезкая маска", "filter-crop-title": "Кадрирование / растягивание", "filter-crop-description": "Обрезка или растягивание изображения.", "filter-crop-left": "Влево", "filter-crop-right": "Вправо", "filter-crop-top": "Ввер<PERSON>", "filter-crop-bottom": "<PERSON><PERSON><PERSON><PERSON>", "filter-crop-rule-thirds": "Правило третей", "filter-crop-fill": "Заполнение", "filter-flip-title": "Перевернуть", "filter-flip-description": "Переворачивание слоя или всего изображения.", "filter-flip-horizontal": "По горизонтали", "filter-flip-vertical": "По вертикали", "filter-flip-image": "Перевернуть изображение", "filter-flip-layer": "Перевернуть слой", "filter-perspective-title": "Перспектива", "filter-perspective-description": "Трансформирование выбранного слоя.", "filter-resize-title": "Изменение размера", "filter-resize-description": "Изменение размера изображения.", "filter-rotate-title": "Поворот", "filter-rotate-description": "Поворачивание изображения.", "filter-transform-empty": "Слой пустой.", "filter-transform-title": "Трансформация", "filter-transform-description": "Трансформация выбранного слоя. Удерживайте Shift для дополнительного поведения.", "filter-transform-rotation": "Поворот", "filter-transform-flip": "Перевернуть", "filter-transform-center": "Центрировать", "filter-transform-constrain": "Сохранить", "filter-transform-snap": "Привязать", "filter-transform-snap-title": "Привязать вращение и положение", "filter-bright-contrast-title": "Яркость / Контрастность", "filter-bright-contrast-description": "Изменение яркости и контрастности для выбранного слоя.", "filter-bright-contrast-brightness": "Яркость", "filter-bright-contrast-contrast": "Контраст", "filter-curves-title": "Кривые", "filter-curves-description": "Применение кривые к выбранному слою.", "filter-curves-all": "Все", "filter-hue-sat-title": "Цветовой тон / Насыщенность", "filter-hue-sat-description": "Изменение оттенка и насыщенность для выбранного слоя.", "filter-hue-sat-hue": "Цветовой тон", "filter-hue-sat-saturation": "Насыщенность", "filter-applied": "- применено", "filter-tilt-shift-title": "Глубина резкости", "filter-tilt-shift-description": "Применение глубины резкости к выбранному слою.", "filter-tilt-shift-blur": "Радиус размытия", "filter-tilt-shift-gradient": "Радиус градиента", "filter-to-alpha-title": "Прозрачность", "filter-to-alpha-description": "Создание альфа-канала для выбранного слоя:", "filter-to-alpha-inverted-lum": "Инвертированная яркость", "filter-to-alpha-lum": "Яркость", "filter-to-alpha-replace": "Замена RGB", "filter-triangle-blur-title": "Треугольное размытие", "filter-triangle-blur-description": "Применение треугольного размытия к выбранному слою.", "filter-unsharp-mask-title": "Нерезкая маска", "filter-unsharp-mask-description": "Повышение резкости выбранного слоя, масштабируя пиксели от среднего значения их соседей.", "filter-unsharp-mask-strength": "Интенсивность", "filter-grid": "Сетка", "filter-grid-description": "Отображение сетки на выбранном слое.", "filter-noise": "<PERSON>ум", "filter-noise-description": "Добавление шума к выбранному слою.", "filter-noise-scale": "Масш<PERSON><PERSON><PERSON>", "filter-noise-alpha": "Прозрачность", "filter-pattern": "Шабл<PERSON>н", "filter-pattern-description": "Создание шаблона на выбранном слое. Перетащите предпросмотр для дополнительных элементов управления.", "filter-distort": "Искажение", "filter-distort-description": "Искажение выбранного слоя.", "filter-distort-phase": "Фаза", "filter-distort-stepsize": "Размер шага", "filter-distort-sync-xy": "Синхронизация XY", "filter-vanish-point": "Испр. перспективы", "filter-vanish-point-title": "Исправление перспективы", "filter-vanish-point-description": "Добавление исправления перспективы к выбранному слою. Перетащите предпросмотр, чтобы переместить.", "filter-vanish-point-lines": "Линии", "import-opening": "Открытие файла...", "import-title": "Импорт изображения", "import-too-large": "Изображение слишком большое, оно будет уменьшено.", "import-btn-as-layer": "Как слой", "import-btn-as-image": "Как изображение", "import-as-layer-title": "Импорт изображения как нового слоя", "import-as-layer-description": "Регулировка положения импортированного изображения.", "import-as-layer-limit-reached": "Достигнуто ограничение слоёв. Изображение будет помещено на существующий слой.", "import-as-layer-fit": "Подгонка", "import-flatten": "Выполнить сведение", "import-unsupported-file": "Неподдерживаемый тип файла. Поддерживаемые типы см. во вкладке «Помощь».", "import-broken-file": "Не удалось загрузить изображение. Возможно, файл повреждён.", "import-psd-unsupported": "Неподдерживаемые функции. PSD необходимо было сведить.", "import-psd-limited-support": "Поддержка PSD ограничена. Сведённый вариант, скорее всего, будет выглядеть правильно.", "import-psd-too-large": "Изображение превышает максимальные размеры {x} x {x} пикселей. Невозможно импортировать.", "import-psd-size": "Размер изображения", "hand-reset": "Сброс", "hand-fit": "Подгонка", "bucket-tolerance": "Допуск", "bucket-sample": "Обр<PERSON><PERSON><PERSON><PERSON>", "bucket-sample-title": "Из каких слоёв брать образцы цвета", "bucket-sample-all": "Все<PERSON>", "bucket-sample-active": "Активный", "bucket-sample-above": "Выше", "bucket-grow": "Расш. заполн.", "bucket-grow-title": "Расширить заполненную область (в пикселях)", "bucket-contiguous": "Смеж. пикс", "bucket-contiguous-title": "Заполнить только соединённые области", "gradient-linear": "Линейный", "gradient-linear-mirror": "Линейно-зеркальный", "gradient-radial": "Лучевой", "shape-stroke": "Обводка", "shape-fill": "Заполнение", "shape-rect": "Прямоугольник", "shape-ellipse": "Элли<PERSON><PERSON>", "shape-line": "Линия", "shape-line-width": "<PERSON><PERSON><PERSON><PERSON>на линии", "shape-outwards": "Внешне", "shape-fixed": "Фиксированная 1:1", "text-instruction": "Нажмите на холст, чтобы разместить текст", "text-title": "Добавить текст", "text-text": "Текст", "text-font": "<PERSON>ри<PERSON><PERSON>", "text-placeholder": "Ваш текст", "text-color": "Цвет", "text-size": "Размер", "text-line-height": "Высота линии", "text-letter-spacing": "Расстояние между буквами", "text-left": "Слева", "text-center": "По центру", "text-right": "Справа", "text-italic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text-bold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "save-reminder-title": "Несохранённая работа", "save-reminder-text": "Изображение не было сохранено в течение {a} мин{b}. Сохраните сейчас, чтобы предотвратить возможную потерю.", "save-reminder-save-psd": "Сохранить как PSD", "save-reminder-psd-layers": "PSD запомнит все слои.", "backup-drawing": "Вы можете создать резервную копию своего рисунка.", "submit": "Отправить", "submit-title": "Отправка рисунка", "submit-prompt": "Отправить рисунок?", "submit-submitting": "Отправка", "embed-init-loading": "Загрузка приложения", "embed-init-waiting": "Ожидание изображения", "unsaved": "Не сохранено", "help": "Помощь", "tab-settings": "Настройки", "settings-language": "Язык", "settings-language-reload": "Обновится после перезагрузки.", "settings-theme": "Тема", "settings-save-reminder-label": "Напоминание сохр.", "settings-save-reminder-disabled": "отключено", "settings-save-reminder-confirm-title": "Отключить напоминание о сохранении?", "settings-save-reminder-confirm-a": "Здесь нет автосохранения, а вкладки браузера не хранятся вечно. Если вы не будете периодически сохранять, то, скорее всего, потеряете рисунок.", "settings-save-reminder-confirm-b": "Отключить на свой страх и риск?", "settings-save-reminder-confirm-disable": "Отключить", "theme-dark": "Тёмная", "theme-light": "Светлая", "terms-of-service": "Условия обслуживания", "licenses": "Лицензии", "source-code": "Исходный код", "auto": "авто", "zoom-in": "Приближение", "zoom-out": "Отдаление", "radius": "Радиус", "constrain-proportions": "Сохранение пропорций", "width": "Ши<PERSON><PERSON><PERSON>", "height": "Высота", "opacity": "Прозрач.", "red": "Красный", "green": "Зелёный", "blue": "Синий", "eraser": "Л<PERSON>с<PERSON><PERSON><PERSON>", "center": "По центру", "layers": "Слои", "background": "Фон", "scaling-algorithm": "Алгоритм масштабирования", "algorithm-smooth": "Глад<PERSON><PERSON>", "algorithm-pixelated": "Пиксельный", "preview": "Предпросмотр", "angle-snap": "Привязка", "angle-snap-title": "Угловая привязка 45°", "lock-alpha": "Альфа-блокировка", "lock-alpha-title": "Блокирует альфа-канал слоя", "reverse": "Реверсивный", "compare-before": "До", "compare-after": "После", "loading": "Загрузка", "more": "<PERSON><PERSON><PERSON>", "x-minutes": "{x} мин."}