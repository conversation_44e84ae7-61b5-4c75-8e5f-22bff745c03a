import { brushes } from '../brushes/brushes'
import { eventResMs } from './brushes-consts'
import { KlSlider } from '../ui/components/kl-slider'
import { Checkbox } from '../ui/components/checkbox'
import { LANG, languageStrings } from '../../language/language'
import { IBrushUi } from '../kl-types'
import { CalligraphyBrushStandalone } from '../brushes/calligraphy-brush-standalone'
import { BB } from '../../bb/bb'
import brushIconImg from '@/utils/klecks/app/img/ui/brush-pen.svg'

export const calligraphyBrushUi = (function () {
  const brushInterface = {
    image: brushIconImg,
    tooltip: '书法笔锋',
    sizeSlider: {
      min: 0.5,
      max: 100,
      curve: BB.quadraticSplineInput(0.5, 100, 0.1)
    },
    opacitySlider: {
      min: 1 / 100,
      max: 1,
      curve: [
        [0, 1 / 100],
        [0.5, 30 / 100],
        [1, 1]
      ]
    }
  } as IBrushUi<CalligraphyBrushStandalone>

  languageStrings.subscribe(() => {
    brushInterface.tooltip = '书法笔锋'
  })

  brushInterface.Ui = function (p) {
    const div = document.createElement('div') // the gui
    const brush = new brushes.CalligraphyBrushStandalone()
    brush.setHistory(p.history)
    p.onSizeChange(brush.getSize())

    let sizeSlider: KlSlider
    let opacitySlider: KlSlider
    let velocityInfluenceSlider: KlSlider
    let pressureInfluenceSlider: KlSlider
    let velocitySmoothingSlider: KlSlider

    const lockAlphaToggle = new Checkbox({
      init: brush.getLockAlpha(),
      label: LANG('lock-alpha'),
      callback: function (b) {
        brush.setLockAlpha(b)
      },
      doHighlight: true,
      title: LANG('lock-alpha-title')
    })

    const sizePressureToggle = new Checkbox({
      init: brush.getSizePressure(),
      label: LANG('brush-size-pressure'),
      callback: function (b) {
        brush.setSizePressure(b)
      },
      doHighlight: true,
      title: LANG('brush-size-pressure-title')
    })

    const opacityPressureToggle = new Checkbox({
      init: brush.getOpacityPressure(),
      label: LANG('brush-opacity-pressure'),
      callback: function (b) {
        brush.setOpacityPressure(b)
      },
      doHighlight: true,
      title: LANG('brush-opacity-pressure-title')
    })

    function setSize(size: number) {
      brush.setSize(size)
    }

    function init() {
      console.log('[CalligraphyBrushUI] Initializing with values:', {
        size: brush.getSize(),
        opacity: brush.getOpacity(),
        velocityInfluence: brush.getVelocityInfluence(),
        pressureInfluence: brush.getPressureInfluence(),
        velocitySmoothing: brush.getVelocitySmoothing()
      })

      sizeSlider = new KlSlider({
        label: LANG('brush-size'),
        width: 225,
        height: 30,
        min: brushInterface.sizeSlider.min,
        max: brushInterface.sizeSlider.max,
        value: brush.getSize(),
        curve: brushInterface.sizeSlider.curve,
        eventResMs: eventResMs,
        toDisplayValue: (val) => val * 2,
        toValue: (displayValue) => displayValue / 2,
        onChange: (val) => {
          setSize(val)
          p.onSizeChange(val)
        },
        formatFunc: (displayValue) => {
          if (displayValue < 10) {
            return BB.round(displayValue, 1)
          } else {
            return Math.round(displayValue)
          }
        },
        manualInputRoundDigits: 1
      })

      opacitySlider = new KlSlider({
        label: LANG('opacity'),
        width: 225,
        height: 30,
        min: brushInterface.opacitySlider.min,
        max: brushInterface.opacitySlider.max,
        value: brush.getOpacity(),
        curve: brushInterface.opacitySlider.curve,
        eventResMs: eventResMs,
        onChange: (val) => {
          brush.setOpacity(val)
          p.onOpacityChange(val)
        }
      })

      // 书法参数滑块
      velocityInfluenceSlider = new KlSlider({
        label: '速度影响',
        width: 225,
        height: 30,
        min: 0,
        max: 1,
        value: brush.getVelocityInfluence() || 0.5,
        eventResMs: eventResMs,
        onChange: (val) => {
          brush.setVelocityInfluence(val)
        },
        formatFunc: (val) => Math.round((val || 0) * 100) + '%'
      })

      pressureInfluenceSlider = new KlSlider({
        label: '压力影响',
        width: 225,
        height: 30,
        min: 0,
        max: 1,
        value: brush.getPressureInfluence() || 0.7,
        eventResMs: eventResMs,
        onChange: (val) => {
          brush.setPressureInfluence(val)
        },
        formatFunc: (val) => Math.round((val || 0) * 100) + '%'
      })

      velocitySmoothingSlider = new KlSlider({
        label: '速度平滑',
        width: 225,
        height: 30,
        min: 0,
        max: 1,
        value: brush.getVelocitySmoothing() || 0.3,
        eventResMs: eventResMs,
        onChange: (val) => {
          brush.setVelocitySmoothing(val)
        },
        formatFunc: (val) => Math.round((val || 0) * 100) + '%'
      })

      // 组装UI
      const toggleRow1 = document.createElement('div')
      toggleRow1.style.display = 'flex'
      toggleRow1.style.flexWrap = 'wrap'
      toggleRow1.style.alignItems = 'center'
      toggleRow1.style.marginBottom = '10px'
      toggleRow1.append(lockAlphaToggle.getElement())

      const toggleRow2 = document.createElement('div')
      toggleRow2.style.display = 'flex'
      toggleRow2.style.flexWrap = 'wrap'
      toggleRow2.style.alignItems = 'center'
      toggleRow2.style.marginBottom = '10px'
      toggleRow2.append(sizePressureToggle.getElement(), opacityPressureToggle.getElement())

      const calligraphyTitle = document.createElement('div')
      calligraphyTitle.textContent = '笔锋效果参数'
      calligraphyTitle.style.fontWeight = 'bold'
      calligraphyTitle.style.marginTop = '15px'
      calligraphyTitle.style.marginBottom = '10px'
      calligraphyTitle.style.color = '#666'

      div.append(
        sizeSlider.getElement(),
        opacitySlider.getElement(),
        toggleRow1,
        toggleRow2,
        calligraphyTitle,
        velocityInfluenceSlider.getElement(),
        pressureInfluenceSlider.getElement(),
        velocitySmoothingSlider.getElement()
      )
    }

    init()

    this.increaseSize = function (f) {
      if (!brush.getIsDrawing()) {
        sizeSlider.changeSliderValue(f)
      }
    }
    this.decreaseSize = function (f) {
      if (!brush.getIsDrawing()) {
        sizeSlider.changeSliderValue(-f)
      }
    }

    this.getSize = function () {
      return brush.getSize()
    }
    this.setSize = function (size) {
      setSize(size)
      sizeSlider.setValue(size)
    }
    this.getOpacity = function () {
      return brush.getOpacity()
    }
    this.setOpacity = function (opacity) {
      brush.setOpacity(opacity)
      opacitySlider.setValue(opacity)
    }
    this.setColor = function (c) {
      brush.setColor(c)
    }
    this.setContext = function (c) {
      brush.setContext(c)
    }
    this.startLine = function (x, y, p) {
      brush.startLine(x, y, p)
    }
    this.goLine = function (x, y, p) {
      brush.goLine(x, y, p)
    }
    this.endLine = function (x, y) {
      brush.endLine(x, y)
    }
    this.getBrush = function () {
      return brush
    }
    this.isDrawing = function () {
      return brush.getIsDrawing()
    }
    this.getElement = function () {
      return div
    }
  } as IBrushUi<CalligraphyBrushStandalone>['Ui']
  return brushInterface
})()
