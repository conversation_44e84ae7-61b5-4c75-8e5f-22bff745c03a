import { BB } from '../../bb/bb'
import { brushes } from '../brushes/brushes'
import { eventResMs } from './brushes-consts'
import { KlSlider } from '../ui/components/kl-slider'
import { createPenPressureToggle } from '../ui/components/create-pen-pressure-toggle'
import { Checkbox } from '../ui/components/checkbox'
import brushIconImg from '@/utils/klecks/app/img/ui/brush-eraser.svg'
import { IBrushUi } from '../kl-types'
import { LANG, languageStrings } from '../../language/language'
import { EraserBrush } from '../brushes/eraser-brush'

export const eraserBrushUi = (function () {
  const brushInterface = {
    image: brushIconImg,
    tooltip: LANG('eraser') + ' [E]',
    sizeSlider: {
      min: 0.5,
      max: 200,
      curve: BB.quadraticSplineInput(0.5, 200, 0.1)
    },
    opacitySlider: {
      min: 1 / 100,
      max: 1
    }
  } as IBrushUi<EraserBrush>

  languageStrings.subscribe(() => {
    brushInterface.tooltip = LANG('eraser') + ' [E]'
  })

  brushInterface.Ui = function (p) {
    const div = document.createElement('div') // the gui
    const brush = new brushes.EraserBrush()
    brush.setHistory(p.history)
    p.onSizeChange(brush.getSize())

    let sizeSlider: KlSlider
    let opacitySlider: KlSlider
    let isTransparentBg = false

    function setSize(size: number) {
      brush.setSize(size)
    }

    function init() {
      sizeSlider = new KlSlider({
        label: LANG('brush-size'),
        width: 225,
        height: 30,
        min: brushInterface.sizeSlider.min,
        max: brushInterface.sizeSlider.max,
        value: 30,
        curve: brushInterface.sizeSlider.curve,
        eventResMs: eventResMs,
        toDisplayValue: (val) => val * 2,
        toValue: (displayValue) => displayValue / 2,
        onChange: (val) => {
          setSize(val)
          p.onSizeChange(val)
        },
        formatFunc: (displayValue) => {
          if (displayValue < 10) {
            return BB.round(displayValue, 1)
          } else {
            return Math.round(displayValue)
          }
        },
        manualInputRoundDigits: 1
      })
      opacitySlider = new KlSlider({
        label: LANG('opacity'),
        width: 225,
        height: 30,
        min: brushInterface.opacitySlider.min,
        max: brushInterface.opacitySlider.max,
        value: brushInterface.opacitySlider.max,
        eventResMs: eventResMs,
        toDisplayValue: (val) => val * 100,
        toValue: (displayValue) => displayValue / 100,
        onChange: (val) => {
          brush.setOpacity(val)
          p.onOpacityChange(val)
        }
      })

      const pressureSizeToggle = createPenPressureToggle(true, function (b) {
        brush.sizePressure(b)
      })
      const pressureOpacityToggle = createPenPressureToggle(false, function (b) {
        brush.opacityPressure(b)
      })

      div.append(
        BB.el({
          content: [sizeSlider.getElement(), pressureSizeToggle],
          css: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '10px'
          }
        }),
        BB.el({
          content: [opacitySlider.getElement(), pressureOpacityToggle],
          css: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }
        })
      )

      const transparencyToggle = new Checkbox({
        init: false,
        label: LANG('brush-eraser-transparent-bg'),
        callback: function (b) {
          isTransparentBg = b
          brush.setTransparentBG(b)
        },
        css: {
          marginTop: '10px'
        }
      })
      div.append(transparencyToggle.getElement())
    }

    init()

    this.increaseSize = function (f) {
      if (!brush.isDrawing()) {
        sizeSlider.changeSliderValue(f)
      }
    }
    this.decreaseSize = function (f) {
      if (!brush.isDrawing()) {
        sizeSlider.changeSliderValue(-f)
      }
    }
    this.getSize = function () {
      return brush.getSize()
    }
    this.setSize = function (size) {
      setSize(size)
      sizeSlider.setValue(size)
    }
    this.getOpacity = function () {
      return brush.getOpacity()
    }
    this.setOpacity = function (opacity) {
      brush.setOpacity(opacity)
      opacitySlider.setValue(opacity)
    }
    this.setColor = function () {}
    this.setContext = function (c) {
      brush.setContext(c as Parameters<EraserBrush['setContext']>[0])
    }
    this.startLine = function (x, y, p) {
      brush.startLine(x, y, p)
    }
    this.goLine = function (x, y, p) {
      brush.goLine(x, y, p)
    }
    this.endLine = function () {
      brush.endLine()
    }
    this.getBrush = function () {
      return brush
    }
    this.getIsTransparentBg = function () {
      return isTransparentBg
    }
    this.isDrawing = function () {
      return brush.isDrawing()
    }
    this.getElement = function () {
      return div
    }
  } as IBrushUi<EraserBrush>['Ui']

  return brushInterface
})()
