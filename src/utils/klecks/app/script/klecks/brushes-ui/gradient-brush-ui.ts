import { brushes } from '../brushes/brushes'
import { eventResMs } from './brushes-consts'
import { KlSlider } from '../ui/components/kl-slider'
import { Checkbox } from '../ui/components/checkbox'
import { createPenPressureToggle } from '../ui/components/create-pen-pressure-toggle'
import { LANG } from '../../language/language'
import { languageStrings } from '../../language/language-manager'
import { IBrushUi } from '../kl-types'
import { GradientBrush } from '../brushes/gradient-brush'
import { BB } from '../../bb/bb'
import brushIconImg from '@/utils/klecks/app/img/ui/brush-blend.svg'

export const gradientBrushUi = (function () {
  const brushInterface = {
    image: brushIconImg,
    tooltip: '渐变画笔',
    sizeSlider: {
      min: 0.5,
      max: 100,
      curve: BB.quadraticSplineInput(0.5, 100, 0.1)
    },
    opacitySlider: {
      min: 1 / 100,
      max: 1,
      curve: [
        [0, 1 / 100],
        [0.5, 30 / 100],
        [1, 1]
      ]
    }
  } as IBrushUi<GradientBrush>

  languageStrings.subscribe(() => {
    brushInterface.tooltip = '渐变画笔'
  })

  brushInterface.Ui = function (p) {
    const div = document.createElement('div') // the gui
    const brush = new brushes.GradientBrush()
    brush.setHistory(p.history)
    p.onSizeChange(brush.getSize())

    let sizeSlider: KlSlider
    let opacitySlider: KlSlider
    let colorChangeSpeedSlider: KlSlider
    let colorRandomnessSlider: KlSlider
    let gradientSmoothingSlider: KlSlider
    let gradientModeSelect: HTMLSelectElement

    const lockAlphaToggle = new Checkbox({
      init: brush.getLockAlpha(),
      label: LANG('lock-alpha'),
      callback: function (b) {
        brush.setLockAlpha(b)
      },
      doHighlight: true,
      title: LANG('lock-alpha-title')
    })

    const sizePressureToggle = createPenPressureToggle(brush.getSizePressure(), function (b) {
      brush.setSizePressure(b)
    })

    const opacityPressureToggle = createPenPressureToggle(brush.getOpacityPressure(), function (b) {
      brush.setOpacityPressure(b)
    })

    function setSize(size: number) {
      brush.setSize(size)
    }

    function init() {
      console.log('[GradientBrushUI] Initializing with values:', {
        size: brush.getSize(),
        opacity: brush.getOpacity(),
        gradientMode: brush.getGradientMode(),
        colorChangeSpeed: brush.getColorChangeSpeed(),
        colorRandomness: brush.getColorRandomness(),
        gradientSmoothing: brush.getGradientSmoothing()
      })
      
      sizeSlider = new KlSlider({
        label: LANG('brush-size'),
        width: 225,
        height: 30,
        min: brushInterface.sizeSlider.min,
        max: brushInterface.sizeSlider.max,
        value: brush.getSize(),
        curve: brushInterface.sizeSlider.curve,
        eventResMs: eventResMs,
        toDisplayValue: (val) => val * 2,
        toValue: (displayValue) => displayValue / 2,
        onChange: (val) => {
          setSize(val)
          p.onSizeChange(val)
        },
        formatFunc: (displayValue) => {
          if (displayValue < 10) {
            return BB.round(displayValue, 1)
          } else {
            return Math.round(displayValue)
          }
        },
        manualInputRoundDigits: 1
      })

      opacitySlider = new KlSlider({
        label: LANG('opacity'),
        width: 225,
        height: 30,
        min: brushInterface.opacitySlider.min,
        max: brushInterface.opacitySlider.max,
        value: brush.getOpacity(),
        curve: brushInterface.opacitySlider.curve,
        eventResMs: eventResMs,
        onChange: (val) => {
          brush.setOpacity(val)
          p.onOpacityChange(val)
        }
      })

      // 渐变模式选择
      const gradientModeContainer = document.createElement('div')
      gradientModeContainer.style.marginBottom = '10px'
      
      const gradientModeLabel = document.createElement('label')
      gradientModeLabel.textContent = '渐变模式:'
      gradientModeLabel.style.display = 'block'
      gradientModeLabel.style.marginBottom = '5px'
      gradientModeLabel.style.fontWeight = 'bold'
      gradientModeLabel.style.fontSize = '12px'
      
      gradientModeSelect = document.createElement('select')
      gradientModeSelect.style.width = '100%'
      gradientModeSelect.style.padding = '5px'
      gradientModeSelect.style.borderRadius = '3px'
      gradientModeSelect.style.border = '1px solid #ccc'
      
      const modes = [
        { value: 'rainbow', text: '彩虹渐变' },
        { value: 'warm', text: '暖色调' },
        { value: 'cool', text: '冷色调' },
        { value: 'custom', text: '自定义' }
      ]
      
      modes.forEach(mode => {
        const option = document.createElement('option')
        option.value = mode.value
        option.textContent = mode.text
        if (mode.value === brush.getGradientMode()) {
          option.selected = true
        }
        gradientModeSelect.appendChild(option)
      })
      
      gradientModeSelect.addEventListener('change', () => {
        brush.setGradientMode(gradientModeSelect.value as any)
      })
      
      gradientModeContainer.append(gradientModeLabel, gradientModeSelect)

      // 渐变参数滑块
      colorChangeSpeedSlider = new KlSlider({
        label: '颜色变化速度',
        width: 225,
        height: 30,
        min: 0,
        max: 1,
        value: brush.getColorChangeSpeed() || 0.5,
        eventResMs: eventResMs,
        onChange: (val) => {
          brush.setColorChangeSpeed(val)
        },
        formatFunc: (val) => Math.round((val || 0) * 100) + '%'
      })

      colorRandomnessSlider = new KlSlider({
        label: '颜色随机性',
        width: 225,
        height: 30,
        min: 0,
        max: 1,
        value: brush.getColorRandomness() || 0.3,
        eventResMs: eventResMs,
        onChange: (val) => {
          brush.setColorRandomness(val)
        },
        formatFunc: (val) => Math.round((val || 0) * 100) + '%'
      })

      gradientSmoothingSlider = new KlSlider({
        label: '渐变平滑度',
        width: 225,
        height: 30,
        min: 0,
        max: 1,
        value: brush.getGradientSmoothing() || 0.7,
        eventResMs: eventResMs,
        onChange: (val) => {
          brush.setGradientSmoothing(val)
        },
        formatFunc: (val) => Math.round((val || 0) * 100) + '%'
      })

      // 组装UI
      const toggleRow1 = document.createElement('div')
      toggleRow1.style.display = 'flex'
      toggleRow1.style.flexWrap = 'wrap'
      toggleRow1.style.alignItems = 'center'
      toggleRow1.style.marginBottom = '10px'
      toggleRow1.append(lockAlphaToggle.getElement())

      const pressureRow = document.createElement('div')
      pressureRow.style.display = 'flex'
      pressureRow.style.justifyContent = 'space-between'
      pressureRow.style.alignItems = 'center'
      pressureRow.style.marginBottom = '10px'
      
      const sizeRow = document.createElement('div')
      sizeRow.style.display = 'flex'
      sizeRow.style.justifyContent = 'space-between'
      sizeRow.style.alignItems = 'center'
      sizeRow.style.marginBottom = '10px'
      sizeRow.append(sizeSlider.getElement(), sizePressureToggle)
      
      const opacityRow = document.createElement('div')
      opacityRow.style.display = 'flex'
      opacityRow.style.justifyContent = 'space-between'
      opacityRow.style.alignItems = 'center'
      opacityRow.style.marginBottom = '10px'
      opacityRow.append(opacitySlider.getElement(), opacityPressureToggle)

      const gradientTitle = document.createElement('div')
      gradientTitle.textContent = '渐变效果参数'
      gradientTitle.style.fontWeight = 'bold'
      gradientTitle.style.marginTop = '15px'
      gradientTitle.style.marginBottom = '10px'
      gradientTitle.style.color = '#666'

      div.append(
        sizeRow,
        opacityRow,
        toggleRow1,
        gradientTitle,
        gradientModeContainer,
        colorChangeSpeedSlider.getElement(),
        colorRandomnessSlider.getElement(),
        gradientSmoothingSlider.getElement()
      )
    }

    init()

    this.increaseSize = function (f) {
      if (!brush.getIsDrawing()) {
        sizeSlider.changeSliderValue(f)
      }
    }
    this.decreaseSize = function (f) {
      if (!brush.getIsDrawing()) {
        sizeSlider.changeSliderValue(-f)
      }
    }

    this.getSize = function () {
      return brush.getSize()
    }
    this.setSize = function (size) {
      setSize(size)
      sizeSlider.setValue(size)
    }
    this.getOpacity = function () {
      return brush.getOpacity()
    }
    this.setOpacity = function (opacity) {
      brush.setOpacity(opacity)
      opacitySlider.setValue(opacity)
    }
    this.setColor = function (c) {
      brush.setColor(c)
    }
    this.setContext = function (c) {
      brush.setContext(c)
    }
    this.startLine = function (x, y, p) {
      brush.startLine(x, y, p)
    }
    this.goLine = function (x, y, p) {
      brush.goLine(x, y, p)
    }
    this.endLine = function (x, y) {
      brush.endLine(x, y)
    }
    this.getBrush = function () {
      return brush
    }
    this.isDrawing = function () {
      return brush.getIsDrawing()
    }
    this.getElement = function () {
      return div
    }
  } as IBrushUi<GradientBrush>['Ui']
  return brushInterface
})()
