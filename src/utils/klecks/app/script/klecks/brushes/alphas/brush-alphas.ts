import { BB } from '../../../bb/bb'
import { noise } from '../../../bb/math/perlin'
import { IVector2D } from '../../../bb/bb-types'

// chalk
export function genBrushAlpha01(w: number): HTMLCanvasElement {
  const scaleFac = w / 500
  const h = w
  const canvas = BB.canvas(w, h)
  const ctx = BB.ctx(canvas)
  const imData = ctx.createImageData(w, h)
  for (let x = 0; x < w; x++) {
    for (let y = 0; y < h; y++) {
      const i = (y * w + x) * 4

      // base noise
      const sFac2 = scaleFac + noise.simplex2(x / 50 / scaleFac, y / 50 / scaleFac) * 0.04
      let noisePattern = 100 + ((noise.simplex2(x / 50 / sFac2, y / 50 / sFac2) + 1) / 2) * 100
      noisePattern -= ((noise.simplex2(x / 10 / scaleFac, y / 10 / scaleFac) + 1) / 2) * 100

      // fade out in circular shape
      const centerDist = BB.dist(w / 2, h / 2, x, y)
      const falloff = BB.clamp(1 - ((centerDist - w / 2.5) / (w / 14) + noise.simplex2(x / 22 / sFac2, y / 22 / sFac2)), 0, 1)
      noisePattern = noisePattern * falloff

      // make the middle darker
      const falloff2 = BB.clamp(1 - centerDist / w, 0, 1) * 2
      noisePattern = noisePattern * falloff2

      imData.data[i] = 0
      imData.data[i + 1] = 0
      imData.data[i + 2] = 0
      imData.data[i + 3] = BB.clamp(noisePattern, 0, 255)
    }
  }

  ctx.putImageData(imData, 0, 0)
  return canvas
}

// https://www.shadertoy.com/view/3tdSDj
function udSegment(p: IVector2D, a: IVector2D, b: IVector2D): number {
  const ba = BB.Vec2.sub(b, a)
  const pa = BB.Vec2.sub(p, a)
  const h = BB.clamp(BB.Vec2.dot(pa, ba) / BB.Vec2.dot(ba, ba), 0.0, 1.0)
  return BB.Vec2.len(BB.Vec2.sub(pa, BB.Vec2.mul(ba, h)))
}

// calligraphy
export function genBrushAlpha02(w: number): HTMLCanvasElement {
  const pDist = 1 / 4
  let centerSize = 2 / 3
  let transitionSize = 1 / 3
  centerSize *= pDist
  transitionSize *= pDist
  const p1 = { x: pDist * w, y: w - w * pDist }
  const p2 = { x: w - w * pDist, y: pDist * w }

  const h = w
  const canvas = BB.canvas(w, h)
  const ctx = BB.ctx(canvas)
  const imData = ctx.createImageData(w, h)
  for (let x = 0; x < w; x++) {
    for (let y = 0; y < h; y++) {
      const i = (y * w + x) * 4

      let col = udSegment({ x: x, y: y }, p1, p2)

      col = BB.clamp(255 - ((col - w * centerSize) / (w * transitionSize)) * 255, 0, 255)

      imData.data[i] = 0
      imData.data[i + 1] = 0
      imData.data[i + 2] = 0
      imData.data[i + 3] = col
    }
  }

  ctx.putImageData(imData, 0, 0)
  return canvas
}

// calligraphy brush effect - creates a simple circular brush for calligraphy strokes
export function genBrushAlpha03(w: number): HTMLCanvasElement {
  console.log(`Generating calligraphy brush alpha with size ${w}x${w}`)

  const h = w
  const canvas = BB.canvas(w, h)
  const ctx = BB.ctx(canvas)

  // Create a simple circular brush for calligraphy
  // The actual calligraphy effect will be handled by the stroke rendering logic
  ctx.fillStyle = 'black'
  ctx.beginPath()
  ctx.arc(w / 2, h / 2, w / 2 - 1, 0, 2 * Math.PI)
  ctx.closePath()
  ctx.fill()

  console.log(`Calligraphy brush alpha generated successfully`)
  return canvas
}

// pen tip effect - creates a sharp pointed brush tip for calligraphy-like strokes
export function genBrushAlpha04(w: number): HTMLCanvasElement {
  console.log(`Generating sharp pen tip alpha with size ${w}x${w}`)

  const h = w
  const canvas = BB.canvas(w, h)
  const ctx = BB.ctx(canvas)
  const imData = ctx.createImageData(w, h)

  const centerX = w / 2
  const centerY = h / 2
  const maxRadius = w / 2 - 1

  for (let x = 0; x < w; x++) {
    for (let y = 0; y < h; y++) {
      const i = (y * w + x) * 4

      // Calculate distance from center
      const dx = x - centerX
      const dy = y - centerY
      const distance = Math.sqrt(dx * dx + dy * dy)

      let alpha = 0

      if (distance <= maxRadius) {
        const normalizedDistance = distance / maxRadius

        // Create a very sharp pointed tip - like a calligraphy brush
        // Use higher power for sharper falloff
        alpha = Math.pow(1 - normalizedDistance, 5.0) * 255

        // Create strong directional bias for sharp tip effect
        const angle = Math.atan2(dy, dx)
        const verticalComponent = Math.abs(Math.sin(angle))
        const horizontalComponent = Math.abs(Math.cos(angle))

        // Make it much more pointed vertically (like a brush tip)
        const sharpnessFactor = 1 - (verticalComponent * 0.6) + (horizontalComponent * 0.2)
        alpha = alpha * sharpnessFactor

        // Add additional sharpening for the tip
        if (distance < maxRadius * 0.3) {
          // Core area - keep full intensity
          alpha = alpha * 1.2
        } else if (distance < maxRadius * 0.7) {
          // Middle area - gradual falloff
          const falloff = (maxRadius * 0.7 - distance) / (maxRadius * 0.4)
          alpha = alpha * (0.8 + falloff * 0.4)
        } else {
          // Outer area - sharp falloff for pointed edge
          const falloff = (maxRadius - distance) / (maxRadius * 0.3)
          alpha = alpha * Math.pow(falloff, 2.5)
        }

        // Add very subtle texture to avoid completely smooth appearance
        const textureNoise = Math.sin(x * 0.5) * Math.cos(y * 0.5) * 0.05
        alpha = alpha * (1 + textureNoise)

        alpha = BB.clamp(alpha, 0, 255)
      }

      imData.data[i] = 0
      imData.data[i + 1] = 0
      imData.data[i + 2] = 0
      imData.data[i + 3] = alpha
    }
  }

  ctx.putImageData(imData, 0, 0)
  console.log(`Sharp pen tip alpha generated successfully`)
  return canvas
}
