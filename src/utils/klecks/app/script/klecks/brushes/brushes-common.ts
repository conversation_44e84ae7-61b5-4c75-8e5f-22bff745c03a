import { genBrushAlpha01, genBrushAlpha02, genBrushAlpha03, genBrushAlpha04 } from './alphas/brush-alphas'

export const alphaImArr: HTMLCanvasElement[] = [] //used by default brush
alphaImArr[1] = genBrushAlpha01(128)
alphaImArr[2] = genBrushAlpha02(128)
alphaImArr[4] = genBrushAlpha03(128) // calligraphy brush effect
alphaImArr[5] = genBrushAlpha04(128) // pen tip effect

console.log('Alpha images initialized:', {
  '1': !!alphaImArr[1],
  '2': !!alphaImArr[2],
  '4': !!alphaImArr[4],
  '5': !!alphaImArr[5]
})
