import { PenBrush } from './pen-brush'
import { BlendBrush } from './blend-brush'
import { SketchyBrush } from './sketchy-brush'
import { PixelBrush } from './pixel-brush'
import { EraserBrush } from './eraser-brush'
import { SmudgeBrush } from './smudge-brush'
import { ChemyBrush } from './chemy-brush'
import { CalligraphyBrushStandalone } from './calligraphy-brush-standalone'

export type TBrush = PenBrush | BlendBrush | SketchyBrush | PixelBrush | ChemyBrush | SmudgeBrush | EraserBrush | CalligraphyBrushStandalone

export const brushes = {
  PenBrush,
  BlendBrush,
  SketchyBrush,
  PixelBrush,
  ChemyBrush,
  SmudgeBrush,
  EraserBrush,
  CalligraphyBrushStandalone
}
