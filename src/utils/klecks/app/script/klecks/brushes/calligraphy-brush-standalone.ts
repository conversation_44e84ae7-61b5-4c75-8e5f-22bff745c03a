import { BB } from '../../bb/bb'
import { IRGB, TPressureInput } from '../kl-types'
import { IHistoryEntry, KlHistory, THistoryInnerActions } from '../history/kl-history'
import { BezierLine } from '../../bb/math/line'

export interface ICalligraphyBrushStandaloneHistoryEntry extends IHistoryEntry {
  tool: ['brush', 'CalligraphyBrushStandalone']
  actions: THistoryInnerActions<CalligraphyBrushStandalone>[]
}

interface IStrokePoint {
  x: number
  y: number
  pressure: number
  timestamp: number
  velocity: number
  width: number
}

/**
 * 独立的书法笔刷 - 实现真正的笔锋效果
 * 根据书写速度和压力动态调整线条粗细，模拟毛笔书法效果
 */
export class CalligraphyBrushStandalone {
  private context: CanvasRenderingContext2D = {} as CanvasRenderingContext2D
  private history: KlHistory | undefined
  private historyEntry: ICalligraphyBrushStandaloneHistoryEntry | undefined

  // 基本设置
  private settingSize: number = 20
  private settingOpacity: number = 1
  private settingColor: IRGB = { r: 0, g: 0, b: 0 }
  private settingColorStr: string = 'rgb(0,0,0)'
  private settingLockLayerAlpha: boolean = false
  private settingHasSizePressure: boolean = true
  private settingHasOpacityPressure: boolean = false

  // 笔锋效果参数
  private minWidth: number = 1 // 最小线条宽度
  private maxWidth: number = 20 // 最大线条宽度
  private velocitySmoothing: number = 0.3 // 速度平滑系数
  private pressureInfluence: number = 0.7 // 压力影响系数
  private velocityInfluence: number = 0.5 // 速度影响系数

  // 绘制状态
  private strokePoints: IStrokePoint[] = []
  private lastPoint: IStrokePoint | null = null
  private isDrawing: boolean = false
  private lastTimestamp: number = 0
  private lastInput: TPressureInput = { x: 0, y: 0, pressure: 0 }
  private lastInput2: TPressureInput = { x: 0, y: 0, pressure: 0 }
  private inputArr: TPressureInput[] = []
  private inputIsDrawing: boolean = false
  private bezierLine: BezierLine | null = null

  constructor() {
    console.log(`[CalligraphyBrushStandalone] Constructor called, initial params:`, {
      velocityInfluence: this.velocityInfluence,
      pressureInfluence: this.pressureInfluence,
      velocitySmoothing: this.velocitySmoothing
    })
  }

  /**
   * 重置绘制状态（用于撤销重放）
   */
  resetState(): void {
    this.strokePoints = []
    this.lastPoint = null
    this.isDrawing = false
    this.lastTimestamp = 0
  }

  /**
   * 计算两点间的距离
   */
  private getDistance(p1: IStrokePoint, p2: IStrokePoint): number {
    const dx = p2.x - p1.x
    const dy = p2.y - p1.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 计算书写速度
   */
  private calculateVelocity(currentPoint: IStrokePoint, lastPoint: IStrokePoint): number {
    const distance = this.getDistance(currentPoint, lastPoint)
    const timeDelta = Math.max(1, currentPoint.timestamp - lastPoint.timestamp)
    return distance / timeDelta
  }

  /**
   * 根据速度和压力计算线条宽度
   */
  private calculateWidth(velocity: number, pressure: number): number {
    // 速度越快，线条越细
    const velocityFactor = Math.max(0.1, 1 - velocity * this.velocityInfluence)
    
    // 压力越大，线条越粗
    const pressureFactor = 0.3 + pressure * this.pressureInfluence
    
    // 结合基础大小
    const width = this.settingSize * velocityFactor * pressureFactor
    
    return BB.clamp(width, this.minWidth, this.maxWidth)
  }

  /**
   * 平滑线条宽度变化
   */
  private smoothWidth(newWidth: number, lastWidth: number): number {
    return lastWidth + (newWidth - lastWidth) * this.velocitySmoothing
  }

  /**
   * 使用贝塞尔曲线绘制笔锋线条
   */
  private drawCalligraphyStroke(points: IStrokePoint[]): void {
    if (points.length < 2) return

    this.context.save()
    this.context.globalAlpha = this.settingOpacity
    this.context.fillStyle = this.settingColorStr
    this.context.strokeStyle = this.settingColorStr

    if (this.settingLockLayerAlpha) {
      this.context.globalCompositeOperation = 'source-atop'
    }

    // 绘制笔锋路径
    for (let i = 0; i < points.length - 1; i++) {
      const p1 = points[i]
      const p2 = points[i + 1]
      
      this.drawStrokeSegment(p1, p2)
    }

    this.context.restore()
  }

  /**
   * 绘制单个笔画段
   */
  private drawStrokeSegment(p1: IStrokePoint, p2: IStrokePoint): void {
    const distance = this.getDistance(p1, p2)
    if (distance < 0.1) return

    const w1 = p1.width / 2
    const w2 = p2.width / 2

    // 计算垂直向量
    const dx = p2.x - p1.x
    const dy = p2.y - p1.y
    const length = Math.sqrt(dx * dx + dy * dy)
    
    if (length === 0) return

    const nx = -dy / length
    const ny = dx / length

    // 绘制四边形路径
    this.context.beginPath()
    this.context.moveTo(p1.x + nx * w1, p1.y + ny * w1)
    this.context.lineTo(p1.x - nx * w1, p1.y - ny * w1)
    this.context.lineTo(p2.x - nx * w2, p2.y - ny * w2)
    this.context.lineTo(p2.x + nx * w2, p2.y + ny * w2)
    this.context.closePath()
    this.context.fill()

    // 绘制圆形端点以平滑连接
    this.context.beginPath()
    this.context.arc(p1.x, p1.y, w1, 0, Math.PI * 2)
    this.context.fill()

    if (distance > 0) {
      this.context.beginPath()
      this.context.arc(p2.x, p2.y, w2, 0, Math.PI * 2)
      this.context.fill()
    }
  }

  /**
   * 添加起笔效果
   */
  private addStartStroke(point: IStrokePoint): void {
    // 起笔时从细到粗的过渡
    const startPoints: IStrokePoint[] = []
    const steps = 5
    
    for (let i = 0; i <= steps; i++) {
      const t = i / steps
      const width = this.minWidth + (point.width - this.minWidth) * t * t
      
      startPoints.push({
        ...point,
        width: width
      })
    }
    
    this.drawCalligraphyStroke(startPoints)
  }

  /**
   * 添加收笔效果
   */
  private addEndStroke(point: IStrokePoint): void {
    // 收笔时从粗到细的过渡
    const endPoints: IStrokePoint[] = []
    const steps = 8
    
    for (let i = 0; i <= steps; i++) {
      const t = i / steps
      const width = point.width * (1 - t * t * 0.8) // 逐渐变细
      
      // 添加轻微的拖尾效果
      const offsetX = (Math.random() - 0.5) * t * 2
      const offsetY = (Math.random() - 0.5) * t * 2
      
      endPoints.push({
        ...point,
        x: point.x + offsetX,
        y: point.y + offsetY,
        width: Math.max(1, width)
      })
    }
    
    this.drawCalligraphyStroke(endPoints)
  }

  // ----------------------------------- public -----------------------------------

  startLine(x: number, y: number, pressure: number): void {
    const timestamp = Date.now()

    this.historyEntry = {
      tool: ['brush', 'CalligraphyBrushStandalone'],
      actions: [
        { action: 'setSize', params: [this.settingSize] },
        { action: 'setOpacity', params: [this.settingOpacity] },
        { action: 'setColor', params: [this.settingColor] },
        { action: 'setLockAlpha', params: [this.settingLockLayerAlpha] },
        { action: 'setSizePressure', params: [this.settingHasSizePressure] },
        { action: 'setOpacityPressure', params: [this.settingHasOpacityPressure] },
        { action: 'setVelocityInfluence', params: [this.velocityInfluence] },
        { action: 'setPressureInfluence', params: [this.pressureInfluence] },
        { action: 'setVelocitySmoothing', params: [this.velocitySmoothing] },
        { action: 'startLine', params: [x, y, pressure] }
      ]
    }

    const initialWidth = this.calculateWidth(0, pressure)
    
    const point: IStrokePoint = {
      x,
      y,
      pressure: BB.clamp(pressure, 0, 1),
      timestamp,
      velocity: 0,
      width: initialWidth
    }

    this.strokePoints = [point]
    this.lastPoint = point
    this.isDrawing = true
    this.lastTimestamp = timestamp

    // 添加起笔效果
    this.addStartStroke(point)

    this.lastInput = { x, y, pressure }
    this.lastInput2 = { x, y, pressure }
    this.inputArr = [{ x, y, pressure }]
    this.inputIsDrawing = true
  }

  goLine(x: number, y: number, pressure: number): void {
    if (!this.isDrawing || !this.lastPoint) return

    const timestamp = Date.now()
    const clampedPressure = BB.clamp(pressure, 0, 1)

    // 计算速度
    const tempPoint: IStrokePoint = {
      x, y, pressure: clampedPressure, timestamp, velocity: 0, width: 0
    }
    
    const velocity = this.calculateVelocity(tempPoint, this.lastPoint)
    
    // 计算线条宽度
    const targetWidth = this.calculateWidth(velocity, clampedPressure)
    const smoothedWidth = this.smoothWidth(targetWidth, this.lastPoint.width)

    const currentPoint: IStrokePoint = {
      x, y,
      pressure: clampedPressure,
      timestamp,
      velocity,
      width: smoothedWidth
    }

    this.strokePoints.push(currentPoint)
    
    // 绘制当前段
    this.drawStrokeSegment(this.lastPoint, currentPoint)
    
    this.lastPoint = currentPoint
    this.lastTimestamp = timestamp

    this.historyEntry!.actions!.push({
      action: 'goLine',
      params: [x, y, pressure]
    })

    this.lastInput2 = this.lastInput
    this.lastInput = { x, y, pressure }
    this.inputArr.push({ x, y, pressure })
  }

  endLine(x: number, y: number): void {
    if (!this.isDrawing || !this.lastPoint) return

    // 添加收笔效果
    this.addEndStroke(this.lastPoint)

    this.isDrawing = false
    this.strokePoints = []
    this.lastPoint = null

    this.historyEntry!.actions!.push({
      action: 'endLine',
      params: [x, y]
    })

    if (this.historyEntry) {
      this.history?.push(this.historyEntry)
      this.historyEntry = undefined
    }

    this.inputIsDrawing = false
  }

  // ---- interface ----

  isDrawing(): boolean {
    return this.inputIsDrawing
  }

  getIsDrawing(): boolean {
    return this.inputIsDrawing
  }

  setContext(c: CanvasRenderingContext2D): void {
    this.context = c
  }

  setHistory(h: KlHistory): void {
    this.history = h
  }

  setSize(size: number): void {
    this.settingSize = size
    this.maxWidth = size * 1.5
    this.minWidth = Math.max(1, size * 0.1)
  }

  setOpacity(opacity: number): void {
    this.settingOpacity = opacity
  }

  setColor(color: IRGB): void {
    this.settingColor = { ...color }
    this.settingColorStr = `rgb(${color.r},${color.g},${color.b})`
  }

  setLockAlpha(lock: boolean): void {
    this.settingLockLayerAlpha = lock
  }

  setSizePressure(b: boolean): void {
    this.settingHasSizePressure = b
  }

  setOpacityPressure(b: boolean): void {
    this.settingHasOpacityPressure = b
  }

  // 书法参数设置方法
  setVelocityInfluence(value: number): void {
    this.velocityInfluence = BB.clamp(value, 0, 1)
    console.log(`[CalligraphyBrushStandalone] setVelocityInfluence: ${this.velocityInfluence}`)
  }

  setPressureInfluence(value: number): void {
    this.pressureInfluence = BB.clamp(value, 0, 1)
    console.log(`[CalligraphyBrushStandalone] setPressureInfluence: ${this.pressureInfluence}`)
  }

  setVelocitySmoothing(value: number): void {
    this.velocitySmoothing = BB.clamp(value, 0, 1)
    console.log(`[CalligraphyBrushStandalone] setVelocitySmoothing: ${this.velocitySmoothing}`)
  }

  // Getters
  getSize(): number {
    return this.settingSize
  }

  getOpacity(): number {
    return this.settingOpacity
  }

  getLockAlpha(): boolean {
    return this.settingLockLayerAlpha
  }

  getSizePressure(): boolean {
    return this.settingHasSizePressure
  }

  getOpacityPressure(): boolean {
    return this.settingHasOpacityPressure
  }

  getVelocityInfluence(): number {
    return this.velocityInfluence
  }

  getPressureInfluence(): number {
    return this.pressureInfluence
  }

  getVelocitySmoothing(): number {
    return this.velocitySmoothing
  }

  // 获取书法参数（用于UI同步）
  getCalligraphyParams(): {
    velocityInfluence: number
    pressureInfluence: number
    velocitySmoothing: number
  } {
    return {
      velocityInfluence: this.velocityInfluence,
      pressureInfluence: this.pressureInfluence,
      velocitySmoothing: this.velocitySmoothing
    }
  }
}
