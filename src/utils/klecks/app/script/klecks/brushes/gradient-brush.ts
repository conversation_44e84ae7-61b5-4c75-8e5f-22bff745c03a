import { BB } from '../../bb/bb'
import { IRGB, TPressureInput } from '../kl-types'
import { IHistoryEntry, KlHistory, THistoryInnerActions } from '../history/kl-history'
import { BezierLine } from '../../bb/math/line'

export interface IGradientBrushHistoryEntry extends IHistoryEntry {
  tool: ['brush', 'GradientBrush']
  actions: THistoryInnerActions<GradientBrush>[]
}

interface IGradientPoint {
  x: number
  y: number
  pressure: number
  color: IRGB
  size: number
  opacity: number
}

/**
 * 渐变色画笔 - 实现边画边颜色随机变化的渐变效果
 * 支持多种渐变模式和颜色配置
 */
export class GradientBrush {
  private context: CanvasRenderingContext2D = {} as CanvasRenderingContext2D
  private history: KlHistory | undefined
  private historyEntry: IGradientBrushHistoryEntry | undefined

  // 基本设置
  private settingSize: number = 20
  private settingOpacity: number = 1
  private settingBaseColor: IRGB = { r: 255, g: 0, b: 0 } // 基础颜色
  private settingLockLayerAlpha: boolean = false
  private settingHasSizePressure: boolean = true
  private settingHasOpacityPressure: boolean = false

  // 渐变效果参数
  private gradientMode: 'rainbow' | 'warm' | 'cool' | 'custom' = 'rainbow'
  private colorChangeSpeed: number = 0.5 // 颜色变化速度 (0-1)
  private colorRandomness: number = 0.3 // 颜色随机性 (0-1)
  private gradientSmoothing: number = 0.7 // 渐变平滑度 (0-1)
  
  // 自定义渐变颜色
  private customColors: IRGB[] = [
    { r: 255, g: 0, b: 0 },   // 红
    { r: 255, g: 165, b: 0 }, // 橙
    { r: 255, g: 255, b: 0 }, // 黄
    { r: 0, g: 255, b: 0 },   // 绿
    { r: 0, g: 0, b: 255 },   // 蓝
    { r: 128, g: 0, b: 128 }  // 紫
  ]

  // 绘制状态
  private strokePoints: IGradientPoint[] = []
  private currentColorIndex: number = 0
  private colorProgress: number = 0
  private lastColorProgress: number = 0 // 用于平滑颜色过渡
  private isDrawing: boolean = false
  private lastInput: TPressureInput = { x: 0, y: 0, pressure: 0 }
  private lastInput2: TPressureInput = { x: 0, y: 0, pressure: 0 }
  private inputArr: TPressureInput[] = []
  private inputIsDrawing: boolean = false
  private bezierLine: BezierLine | null = null
  private strokeDistance: number = 0 // 累计绘制距离，用于更自然的颜色变化

  constructor() {
    console.log(`[GradientBrush] Constructor called`)
  }

  /**
   * 重置绘制状态
   */
  resetState(): void {
    this.strokePoints = []
    this.currentColorIndex = 0
    this.colorProgress = 0
    this.lastColorProgress = 0
    this.strokeDistance = 0
    this.isDrawing = false
  }

  /**
   * 根据模式获取颜色调色板（优化过渡更自然）
   */
  private getColorPalette(): IRGB[] {
    switch (this.gradientMode) {
      case 'rainbow':
        return [
          { r: 255, g: 0, b: 0 },     // 红
          { r: 255, g: 127, b: 0 },   // 橙红
          { r: 255, g: 200, b: 0 },   // 橙黄
          { r: 200, g: 255, b: 0 },   // 黄绿
          { r: 0, g: 255, b: 100 },   // 绿
          { r: 0, g: 200, b: 255 },   // 青绿
          { r: 0, g: 100, b: 255 },   // 蓝
          { r: 100, g: 0, b: 255 },   // 蓝紫
          { r: 200, g: 0, b: 200 },   // 紫
          { r: 255, g: 0, b: 100 }    // 紫红（回到红色）
        ]
      case 'warm':
        return [
          { r: 255, g: 60, b: 60 },   // 暖红
          { r: 255, g: 120, b: 40 },  // 橙红
          { r: 255, g: 180, b: 20 },  // 橙
          { r: 255, g: 220, b: 60 },  // 金橙
          { r: 255, g: 240, b: 120 }, // 暖黄
          { r: 255, g: 200, b: 80 },  // 金黄
          { r: 255, g: 140, b: 60 }   // 回到橙色
        ]
      case 'cool':
        return [
          { r: 100, g: 255, b: 255 }, // 浅青
          { r: 60, g: 220, b: 255 },  // 天蓝
          { r: 80, g: 160, b: 255 },  // 蓝
          { r: 120, g: 100, b: 255 }, // 蓝紫
          { r: 160, g: 80, b: 255 },  // 紫
          { r: 200, g: 120, b: 255 }, // 淡紫
          { r: 140, g: 180, b: 255 }  // 回到蓝色
        ]
      case 'custom':
        return this.customColors
      default:
        return this.customColors
    }
  }

  /**
   * 计算当前应该使用的颜色
   */
  private calculateCurrentColor(progress: number): IRGB {
    const palette = this.getColorPalette()
    const paletteLength = palette.length

    // 使用平滑的随机性，避免突兀的颜色跳跃
    const smoothRandom = Math.sin(Date.now() * 0.001) * this.colorRandomness * 0.1
    const adjustedProgress = Math.max(0, Math.min(1, progress + smoothRandom))

    // 使用更平滑的曲线进行颜色过渡
    const smoothProgress = this.smoothStep(adjustedProgress)

    // 计算在调色板中的位置
    const position = smoothProgress * (paletteLength - 1)
    const index1 = Math.floor(position)
    const index2 = Math.min(index1 + 1, paletteLength - 1)
    const t = position - index1

    const color1 = palette[index1]
    const color2 = palette[index2]

    // 使用更平滑的插值算法（Hermite插值）
    const smoothT = this.smoothStep(t)

    return {
      r: Math.round(color1.r + (color2.r - color1.r) * smoothT),
      g: Math.round(color1.g + (color2.g - color1.g) * smoothT),
      b: Math.round(color1.b + (color2.b - color1.b) * smoothT)
    }
  }

  /**
   * 平滑步进函数，产生更自然的过渡
   */
  private smoothStep(t: number): number {
    // Hermite插值：3t² - 2t³
    return t * t * (3 - 2 * t)
  }

  /**
   * 更新颜色进度（基于绘制距离，更自然）
   */
  private updateColorProgress(distance: number): void {
    // 基于绘制距离而不是时间来更新颜色，使颜色变化更自然
    const progressIncrement = (distance / 100) * this.colorChangeSpeed

    // 使用平滑插值来避免突兀的颜色跳跃
    const targetProgress = this.colorProgress + progressIncrement
    this.lastColorProgress = this.colorProgress
    this.colorProgress = this.lastColorProgress + (targetProgress - this.lastColorProgress) * this.gradientSmoothing

    // 循环颜色进度
    if (this.colorProgress > 1) {
      this.colorProgress = this.colorProgress - 1
    }

    // 累计绘制距离
    this.strokeDistance += distance
  }

  /**
   * 绘制渐变线段（使用多段渐变实现更平滑的过渡）
   */
  private drawGradientSegment(p1: IGradientPoint, p2: IGradientPoint): void {
    const distance = Math.sqrt((p2.x - p1.x) ** 2 + (p2.y - p1.y) ** 2)
    if (distance < 0.1) return

    this.context.save()

    if (this.settingLockLayerAlpha) {
      this.context.globalCompositeOperation = 'source-atop'
    }

    // 将线段分成多个小段，每段都有细微的颜色变化
    const segments = Math.max(2, Math.floor(distance / 5)) // 根据距离决定分段数

    for (let i = 0; i < segments; i++) {
      const t1 = i / segments
      const t2 = (i + 1) / segments

      // 计算当前段的起点和终点
      const x1 = p1.x + (p2.x - p1.x) * t1
      const y1 = p1.y + (p2.y - p1.y) * t1
      const x2 = p1.x + (p2.x - p1.x) * t2
      const y2 = p1.y + (p2.y - p1.y) * t2

      // 插值颜色
      const color1 = this.interpolateColor(p1.color, p2.color, t1)
      const color2 = this.interpolateColor(p1.color, p2.color, t2)

      // 插值其他属性
      const opacity1 = p1.opacity + (p2.opacity - p1.opacity) * t1
      const opacity2 = p1.opacity + (p2.opacity - p1.opacity) * t2
      const size1 = p1.size + (p2.size - p1.size) * t1
      const size2 = p1.size + (p2.size - p1.size) * t2

      // 创建当前段的渐变
      const gradient = this.context.createLinearGradient(x1, y1, x2, y2)
      gradient.addColorStop(0, `rgba(${color1.r}, ${color1.g}, ${color1.b}, ${opacity1})`)
      gradient.addColorStop(1, `rgba(${color2.r}, ${color2.g}, ${color2.b}, ${opacity2})`)

      // 绘制当前段
      this.context.strokeStyle = gradient
      this.context.lineWidth = (size1 + size2) / 2
      this.context.lineCap = 'round'
      this.context.lineJoin = 'round'

      this.context.beginPath()
      this.context.moveTo(x1, y1)
      this.context.lineTo(x2, y2)
      this.context.stroke()
    }

    this.context.restore()
  }

  /**
   * 颜色插值（使用平滑插值）
   */
  private interpolateColor(color1: IRGB, color2: IRGB, t: number): IRGB {
    const smoothT = this.smoothStep(t)
    return {
      r: Math.round(color1.r + (color2.r - color1.r) * smoothT),
      g: Math.round(color1.g + (color2.g - color1.g) * smoothT),
      b: Math.round(color1.b + (color2.b - color1.b) * smoothT)
    }
  }

  /**
   * 绘制渐变点
   */
  private drawGradientPoint(point: IGradientPoint): void {
    this.context.save()
    
    if (this.settingLockLayerAlpha) {
      this.context.globalCompositeOperation = 'source-atop'
    }

    this.context.globalAlpha = point.opacity
    this.context.fillStyle = `rgb(${point.color.r}, ${point.color.g}, ${point.color.b})`

    this.context.beginPath()
    this.context.arc(point.x, point.y, point.size / 2, 0, Math.PI * 2)
    this.context.fill()

    this.context.restore()
  }

  // ----------------------------------- public -----------------------------------

  startLine(x: number, y: number, pressure: number): void {
    this.historyEntry = {
      tool: ['brush', 'GradientBrush'],
      actions: [
        { action: 'setSize', params: [this.settingSize] },
        { action: 'setOpacity', params: [this.settingOpacity] },
        { action: 'setBaseColor', params: [this.settingBaseColor] },
        { action: 'setLockAlpha', params: [this.settingLockLayerAlpha] },
        { action: 'setSizePressure', params: [this.settingHasSizePressure] },
        { action: 'setOpacityPressure', params: [this.settingHasOpacityPressure] },
        { action: 'setGradientMode', params: [this.gradientMode] },
        { action: 'setColorChangeSpeed', params: [this.colorChangeSpeed] },
        { action: 'setColorRandomness', params: [this.colorRandomness] },
        { action: 'setGradientSmoothing', params: [this.gradientSmoothing] },
        { action: 'startLine', params: [x, y, pressure] }
      ]
    }

    // 计算初始颜色和大小
    const currentColor = this.calculateCurrentColor(this.colorProgress)
    const size = this.settingHasSizePressure ? 
      this.settingSize * (0.3 + pressure * 0.7) : this.settingSize
    const opacity = this.settingHasOpacityPressure ? 
      this.settingOpacity * pressure : this.settingOpacity

    const point: IGradientPoint = {
      x, y, pressure: BB.clamp(pressure, 0, 1),
      color: currentColor, size, opacity
    }

    this.strokePoints = [point]
    this.isDrawing = true

    // 绘制起始点
    this.drawGradientPoint(point)

    this.lastInput = { x, y, pressure }
    this.lastInput2 = { x, y, pressure }
    this.inputArr = [{ x, y, pressure }]
    this.inputIsDrawing = true
  }

  goLine(x: number, y: number, pressure: number): void {
    if (!this.isDrawing) return

    // 计算与上一个点的距离
    const lastPoint = this.strokePoints[this.strokePoints.length - 1]
    const distance = lastPoint ?
      Math.sqrt((x - lastPoint.x) ** 2 + (y - lastPoint.y) ** 2) : 0

    // 基于距离更新颜色进度
    this.updateColorProgress(distance)

    // 计算当前颜色和大小
    const currentColor = this.calculateCurrentColor(this.colorProgress)
    const size = this.settingHasSizePressure ?
      this.settingSize * (0.3 + pressure * 0.7) : this.settingSize
    const opacity = this.settingHasOpacityPressure ?
      this.settingOpacity * pressure : this.settingOpacity

    const currentPoint: IGradientPoint = {
      x, y, pressure: BB.clamp(pressure, 0, 1),
      color: currentColor, size, opacity
    }

    this.strokePoints.push(currentPoint)

    // 绘制渐变线段
    if (this.strokePoints.length >= 2) {
      const previousPoint = this.strokePoints[this.strokePoints.length - 2]
      this.drawGradientSegment(previousPoint, currentPoint)
    }

    this.historyEntry!.actions!.push({
      action: 'goLine',
      params: [x, y, pressure]
    })

    this.lastInput2 = this.lastInput
    this.lastInput = { x, y, pressure }
    this.inputArr.push({ x, y, pressure })
  }

  endLine(x: number, y: number): void {
    if (!this.isDrawing) return

    this.isDrawing = false
    this.strokePoints = []

    this.historyEntry!.actions!.push({
      action: 'endLine',
      params: [x, y]
    })

    if (this.historyEntry) {
      this.history?.push(this.historyEntry)
      this.historyEntry = undefined
    }

    this.inputIsDrawing = false
  }

  // ---- interface ----

  isDrawing(): boolean {
    return this.inputIsDrawing
  }

  getIsDrawing(): boolean {
    return this.inputIsDrawing
  }

  setContext(c: CanvasRenderingContext2D): void {
    this.context = c
  }

  setHistory(h: KlHistory): void {
    this.history = h
  }

  setSize(size: number): void {
    this.settingSize = size
  }

  setOpacity(opacity: number): void {
    this.settingOpacity = opacity
  }

  setBaseColor(color: IRGB): void {
    this.settingBaseColor = { ...color }
  }

  // 为了兼容性，保留 setColor 方法
  setColor(color: IRGB): void {
    this.setBaseColor(color)
  }

  setLockAlpha(lock: boolean): void {
    this.settingLockLayerAlpha = lock
  }

  setSizePressure(b: boolean): void {
    this.settingHasSizePressure = b
  }

  setOpacityPressure(b: boolean): void {
    this.settingHasOpacityPressure = b
  }

  // 渐变参数设置方法
  setGradientMode(mode: 'rainbow' | 'warm' | 'cool' | 'custom'): void {
    this.gradientMode = mode
    console.log(`[GradientBrush] setGradientMode: ${this.gradientMode}`)
  }

  setColorChangeSpeed(value: number): void {
    this.colorChangeSpeed = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setColorChangeSpeed: ${this.colorChangeSpeed}`)
  }

  setColorRandomness(value: number): void {
    this.colorRandomness = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setColorRandomness: ${this.colorRandomness}`)
  }

  setGradientSmoothing(value: number): void {
    this.gradientSmoothing = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setGradientSmoothing: ${this.gradientSmoothing}`)
  }

  setCustomColors(colors: IRGB[]): void {
    if (colors.length > 0) {
      this.customColors = colors.map(c => ({ ...c }))
      console.log(`[GradientBrush] setCustomColors: ${colors.length} colors`)
    }
  }

  // Getters
  getSize(): number {
    return this.settingSize
  }

  getOpacity(): number {
    return this.settingOpacity
  }

  getBaseColor(): IRGB {
    return { ...this.settingBaseColor }
  }

  getLockAlpha(): boolean {
    return this.settingLockLayerAlpha
  }

  getSizePressure(): boolean {
    return this.settingHasSizePressure
  }

  getOpacityPressure(): boolean {
    return this.settingHasOpacityPressure
  }

  getGradientMode(): string {
    return this.gradientMode
  }

  getColorChangeSpeed(): number {
    return this.colorChangeSpeed
  }

  getColorRandomness(): number {
    return this.colorRandomness
  }

  getGradientSmoothing(): number {
    return this.gradientSmoothing
  }

  getCustomColors(): IRGB[] {
    return this.customColors.map(c => ({ ...c }))
  }

  // 获取渐变参数（用于UI同步）
  getGradientParams(): {
    mode: string
    colorChangeSpeed: number
    colorRandomness: number
    gradientSmoothing: number
    customColors: IRGB[]
  } {
    return {
      mode: this.gradientMode,
      colorChangeSpeed: this.colorChangeSpeed,
      colorRandomness: this.colorRandomness,
      gradientSmoothing: this.gradientSmoothing,
      customColors: this.getCustomColors()
    }
  }
}
