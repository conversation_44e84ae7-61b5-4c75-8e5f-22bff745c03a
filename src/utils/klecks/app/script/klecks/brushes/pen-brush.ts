import { BB } from '../../bb/bb'
import { alphaImArr } from './brushes-common'
import { IHistoryEntry, KlHistory, THistoryInnerActions } from '../history/kl-history'
import { IRGB, TPressureInput } from '../kl-types'
import { BezierLine } from '../../bb/math/line'
import { CalligraphyBrush } from './calligraphy-brush'

export interface IPenBrushHistoryEntry extends IHistoryEntry {
  tool: ['brush', 'PenBrush']
  actions: THistoryInnerActions<PenBrush>[]
}

const ALPHA_CIRCLE = 0
const ALPHA_CHALK = 1
const ALPHA_CAL = 2 // calligraphy
const ALPHA_SQUARE = 3
const ALPHA_BRUSH_TIP = 4 // brush tip effect
const ALPHA_PEN_TIP = 5 // pen tip effect

const TWO_PI = 2 * Math.PI

export class PenBrush {
  private context: CanvasRenderingContext2D = {} as CanvasRenderingContext2D
  private history: KlHistory | undefined
  private historyEntry: IPenBrushHistoryEntry | undefined

  private settingHasOpacityPressure: boolean = false
  private settingHasSizePressure: boolean = true
  private settingSize: number = 2
  private settingSpacing: number = 0.8489
  private settingOpacity: number = 1
  private settingColor: IRGB = {} as IRGB
  private settingColorStr: string = ''
  private settingAlphaId: number = ALPHA_CIRCLE
  private settingLockLayerAlpha: boolean = false

  private hasDrawnDot: boolean = false
  private lineToolLastDot: number = 0
  private lastInput: TPressureInput = { x: 0, y: 0, pressure: 0 }
  private lastInput2: TPressureInput = { x: 0, y: 0, pressure: 0 }
  private inputArr: TPressureInput[] = []
  private inputIsDrawing: boolean = false
  private bezierLine: BezierLine | null = null

  // 书法笔刷实例 (用于 Alpha ID 4)
  private calligraphyBrush: CalligraphyBrush | null = null

  // 书法笔刷参数缓存（用于撤销重放时的参数恢复）
  private calligraphyParams: {
    velocityInfluence: number
    pressureInfluence: number
    velocitySmoothing: number
  } = {
    velocityInfluence: 0.5,
    pressureInfluence: 0.7,
    velocitySmoothing: 0.3
  }

  // mipmapping
  private readonly alphaCanvas128: HTMLCanvasElement = BB.canvas(128, 128)
  private readonly alphaCanvas64: HTMLCanvasElement = BB.canvas(64, 64)
  private readonly alphaCanvas32: HTMLCanvasElement = BB.canvas(32, 32)
  private readonly alphaOpacityArr: number[] = [1, 0.9, 1, 1, 1, 1] // added opacity for pen tip effect

  private updateAlphaCanvas() {
    if (this.settingAlphaId === ALPHA_CIRCLE || this.settingAlphaId === ALPHA_SQUARE) {
      return
    }

    const instructionArr: [HTMLCanvasElement, number][] = [
      [this.alphaCanvas128, 128],
      [this.alphaCanvas64, 64],
      [this.alphaCanvas32, 32]
    ]

    let ctx

    for (let i = 0; i < instructionArr.length; i++) {
      ctx = BB.ctx(instructionArr[i][0] as any)

      ctx.save()
      ctx.clearRect(0, 0, instructionArr[i][1], instructionArr[i][1])

      ctx.fillStyle = 'rgba(' + this.settingColor.r + ', ' + this.settingColor.g + ', ' + this.settingColor.b + ', ' + this.alphaOpacityArr[this.settingAlphaId] + ')'
      ctx.fillRect(0, 0, instructionArr[i][1], instructionArr[i][1])

      ctx.globalCompositeOperation = 'destination-in'
      ctx.imageSmoothingQuality = 'high'

      // Add error checking for alpha image
      const alphaImage = alphaImArr[this.settingAlphaId]
      if (alphaImage) {
        ctx.drawImage(alphaImage, 0, 0, instructionArr[i][1], instructionArr[i][1])
      } else {
        console.warn(`Alpha image not found for ID ${this.settingAlphaId}`)
      }

      ctx.restore()
    }
  }

  private calcOpacity(pressure: number): number {
    return this.settingOpacity * (this.settingHasOpacityPressure ? pressure * pressure : 1)
  }

  /**
   * 初始化书法笔刷
   */
  private initCalligraphyBrush(): void {
    if (!this.calligraphyBrush) {
      this.calligraphyBrush = new CalligraphyBrush()
      this.calligraphyBrush.setContext(this.context)
      this.calligraphyBrush.setHistory(this.history!)
    }

    // 同步设置
    this.calligraphyBrush.setSize(this.settingSize)
    this.calligraphyBrush.setOpacity(this.settingOpacity)
    this.calligraphyBrush.setColor(this.settingColor)
    this.calligraphyBrush.setLockAlpha(this.settingLockLayerAlpha)

    // 应用缓存的书法参数
    this.calligraphyBrush.setCalligraphyParams(this.calligraphyParams)
  }

  /**
   * 强制重新初始化书法笔刷（用于撤销重放时）
   */
  private forceInitCalligraphyBrush(): void {
    console.log(`[PenBrush] forceInitCalligraphyBrush - cached params:`, this.calligraphyParams)

    this.calligraphyBrush = new CalligraphyBrush()
    console.log(`[PenBrush] CalligraphyBrush created:`, this.calligraphyBrush)

    this.calligraphyBrush.setContext(this.context)
    this.calligraphyBrush.setHistory(this.history!)

    // 重置绘制状态
    this.calligraphyBrush.resetState()

    // 同步当前设置
    this.calligraphyBrush.setSize(this.settingSize)
    this.calligraphyBrush.setOpacity(this.settingOpacity)
    this.calligraphyBrush.setColor(this.settingColor)
    this.calligraphyBrush.setLockAlpha(this.settingLockLayerAlpha)

    console.log(`[PenBrush] Before applying cached params, brush default params:`, this.calligraphyBrush.getCalligraphyParams())

    // 应用缓存的书法参数
    this.calligraphyBrush.setCalligraphyParams(this.calligraphyParams)

    console.log(`[PenBrush] After applying cached params, brush params:`, this.calligraphyBrush.getCalligraphyParams())
  }

  /**
   * @param x
   * @param y
   * @param size
   * @param opacity
   * @param angle
   * @param before - [x, y, size, opacity, angle] the drawDot call before
   */
  private drawDot(x: number, y: number, size: number, opacity: number, angle?: number, before?: [number, number, number, number, number | undefined]): void {
    if (size <= 0) {
      return
    }

    if (this.settingLockLayerAlpha) {
      this.context.globalCompositeOperation = 'source-atop'
    }

    if (!before || before[3] !== opacity) {
      this.context.globalAlpha = opacity
    }

    if (!before && (this.settingAlphaId === ALPHA_CIRCLE || this.settingAlphaId === ALPHA_SQUARE)) {
      this.context.fillStyle = this.settingColorStr
    }

    if (this.settingAlphaId === ALPHA_CIRCLE) {
      this.context.beginPath()
      this.context.arc(x, y, size, 0, TWO_PI)
      this.context.closePath()
      this.context.fill()
      this.hasDrawnDot = true
    } else if (this.settingAlphaId === ALPHA_SQUARE) {
      if (angle !== undefined) {
        this.context.save()
        this.context.translate(x, y)
        this.context.rotate((angle / 180) * Math.PI)
        this.context.fillRect(-size, -size, size * 2, size * 2)
        this.context.restore()
        this.hasDrawnDot = true
      }
    } else {
      // other brush alphas
      this.context.save()
      this.context.translate(x, y)
      let targetMipmap = this.alphaCanvas128
      if (size <= 32 && size > 16) {
        targetMipmap = this.alphaCanvas64
      } else if (size <= 16) {
        targetMipmap = this.alphaCanvas32
      }
      this.context.scale(size, size)
      if (this.settingAlphaId === ALPHA_CHALK) {
        this.context.rotate(((x + y) * 53123) % TWO_PI) // without mod it sometimes looks different
      } else if (this.settingAlphaId === ALPHA_BRUSH_TIP && angle !== undefined) {
        // Rotate brush tip based on drawing direction for natural brush stroke effect
        this.context.rotate((angle / 180) * Math.PI)
      } else if (this.settingAlphaId === ALPHA_PEN_TIP && angle !== undefined) {
        // Rotate pen tip based on drawing direction for pointed pen effect
        this.context.rotate((angle / 180) * Math.PI)
      }
      this.context.drawImage(targetMipmap, -1, -1, 2, 2)

      this.context.restore()
      this.hasDrawnDot = true
    }
  }

  // continueLine
  private continueLine(x: number | null, y: number | null, size: number, pressure: number): void {
    if (this.bezierLine === null) {
      this.bezierLine = new BB.BezierLine()
      this.bezierLine.add(this.lastInput.x, this.lastInput.y, 0, () => {})
    }

    const drawArr: [number, number, number, number, number | undefined][] = [] //draw instructions. will be all drawn at once

    const dotCallback = (val: { x: number; y: number; t: number; angle?: number; dAngle: number }): void => {
      const localPressure = BB.mix(this.lastInput2.pressure, pressure, val.t)
      const localOpacity = this.calcOpacity(localPressure)
      const localSize = Math.max(0.1, this.settingSize * (this.settingHasSizePressure ? localPressure : 1))
      drawArr.push([val.x, val.y, localSize, localOpacity, val.angle])
    }

    const localSpacing = size * this.settingSpacing
    if (x === null || y === null) {
      this.bezierLine.addFinal(localSpacing, dotCallback)
    } else {
      this.bezierLine.add(x, y, localSpacing, dotCallback)
    }

    // execute draw instructions
    this.context.save()
    let before: (typeof drawArr)[number] | undefined = undefined
    for (let i = 0; i < drawArr.length; i++) {
      const item = drawArr[i]
      this.drawDot(item[0], item[1], item[2], item[3], item[4], before)
      before = item
    }
    this.context.restore()
  }

  // ----------------------------------- public -----------------------------------
  constructor() {}

  // ---- interface ----

  startLine(x: number, y: number, p: number): void {
    // 如果是书法笔锋模式 (Alpha ID 4)，使用书法笔刷但保持 PenBrush 的历史记录
    if (this.settingAlphaId === ALPHA_BRUSH_TIP) {
      this.initCalligraphyBrush()

      // 创建 PenBrush 的历史记录条目，包含书法笔刷的参数
      // 注意：setAlpha 必须在书法参数设置之前，以确保书法笔刷先被初始化
      this.historyEntry = {
        tool: ['brush', 'PenBrush'],
        actions: [
          {
            action: 'opacityPressure',
            params: [this.settingHasOpacityPressure]
          },
          {
            action: 'sizePressure',
            params: [this.settingHasSizePressure]
          },
          {
            action: 'setSize',
            params: [this.settingSize]
          },
          {
            action: 'setSpacing',
            params: [this.settingSpacing]
          },
          {
            action: 'setOpacity',
            params: [this.settingOpacity]
          },
          {
            action: 'setColor',
            params: [this.settingColor]
          },
          {
            action: 'setLockAlpha',
            params: [this.settingLockLayerAlpha]
          },
          {
            action: 'setAlpha',
            params: [this.settingAlphaId]
          },
          {
            action: 'setCalligraphyVelocityInfluence',
            params: [this.calligraphyBrush!.getCalligraphyParams().velocityInfluence]
          },
          {
            action: 'setCalligraphyPressureInfluence',
            params: [this.calligraphyBrush!.getCalligraphyParams().pressureInfluence]
          },
          {
            action: 'setCalligraphyVelocitySmoothing',
            params: [this.calligraphyBrush!.getCalligraphyParams().velocitySmoothing]
          },
          {
            action: 'startLine',
            params: [x, y, p]
          }
        ]
      }

      this.calligraphyBrush!.startLine(x, y, p)
      this.inputIsDrawing = true
      return
    }
    this.historyEntry = {
      tool: ['brush', 'PenBrush'],
      actions: [
        {
          action: 'opacityPressure',
          params: [this.settingHasOpacityPressure]
        },
        {
          action: 'sizePressure',
          params: [this.settingHasSizePressure]
        },
        {
          action: 'setSize',
          params: [this.settingSize]
        },
        {
          action: 'setSpacing',
          params: [this.settingSpacing]
        },
        {
          action: 'setOpacity',
          params: [this.settingOpacity]
        },
        {
          action: 'setColor',
          params: [this.settingColor]
        },
        {
          action: 'setAlpha',
          params: [this.settingAlphaId]
        },
        {
          action: 'setLockAlpha',
          params: [this.settingLockLayerAlpha]
        }
      ]
    }

    p = BB.clamp(p, 0, 1)
    const localOpacity = this.calcOpacity(p)
    const localSize = this.settingHasSizePressure ? Math.max(0.1, p * this.settingSize) : Math.max(0.1, this.settingSize)

    this.hasDrawnDot = false

    this.inputIsDrawing = true
    this.context.save()
    this.drawDot(x, y, localSize, localOpacity)
    this.context.restore()

    this.lineToolLastDot = localSize * this.settingSpacing
    this.lastInput.x = x
    this.lastInput.y = y
    this.lastInput.pressure = p
    this.lastInput2.pressure = p

    this.inputArr = [
      {
        x,
        y,
        pressure: p
      }
    ]

    this.historyEntry.actions!.push({
      action: 'startLine',
      params: [x, y, p]
    })
  }

  goLine(x: number, y: number, p: number): void {
    if (!this.inputIsDrawing) {
      return
    }

    // 如果是书法笔锋模式，使用书法笔刷但记录到 PenBrush 历史
    if (this.settingAlphaId === ALPHA_BRUSH_TIP && this.calligraphyBrush) {
      this.historyEntry!.actions!.push({
        action: 'goLine',
        params: [x, y, p]
      })
      this.calligraphyBrush.goLine(x, y, p)
      return
    }
    this.historyEntry!.actions!.push({
      action: 'goLine',
      params: [x, y, p]
    })

    const pressure = BB.clamp(p, 0, 1)
    const localSize = this.settingHasSizePressure ? Math.max(0.1, this.lastInput.pressure * this.settingSize) : Math.max(0.1, this.settingSize)

    this.context.save()
    this.continueLine(x, y, localSize, this.lastInput.pressure)

    /*context.fillStyle = 'red';
        context.fillRect(Math.floor(x), Math.floor(y - 10), 1, 20);
        context.fillRect(Math.floor(x - 10), Math.floor(y), 20, 1);*/

    this.context.restore()

    this.lastInput.x = x
    this.lastInput.y = y
    this.lastInput2.pressure = this.lastInput.pressure
    this.lastInput.pressure = pressure

    this.inputArr.push({
      x,
      y,
      pressure: p
    })
  }

  endLine(x: number, y: number): void {
    // 如果是书法笔锋模式，使用书法笔刷但记录到 PenBrush 历史
    if (this.settingAlphaId === ALPHA_BRUSH_TIP && this.calligraphyBrush) {
      this.historyEntry!.actions!.push({
        action: 'endLine',
        params: [x, y]
      })
      this.calligraphyBrush.endLine(x, y)
      this.inputIsDrawing = false

      // 推送 PenBrush 的历史记录条目
      if (this.historyEntry) {
        this.history?.push(this.historyEntry)
        this.historyEntry = undefined
      }
      return
    }
    const localSize = this.settingHasSizePressure ? Math.max(0.1, this.lastInput.pressure * this.settingSize) : Math.max(0.1, this.settingSize)
    this.context.save()
    this.continueLine(null, null, localSize, this.lastInput.pressure)
    this.context.restore()

    this.inputIsDrawing = false

    if ((this.settingAlphaId === ALPHA_SQUARE || this.settingAlphaId === ALPHA_BRUSH_TIP) && !this.hasDrawnDot) {
      // find max pressure input, use that one
      let maxInput = this.inputArr[0]
      this.inputArr.forEach((item) => {
        if (item.pressure > maxInput.pressure) {
          maxInput = item
        }
      })

      this.context.save()
      const p = BB.clamp(maxInput.pressure, 0, 1)
      const localOpacity = this.calcOpacity(p)
      this.drawDot(maxInput.x, maxInput.y, localSize, localOpacity, 0)
      this.context.restore()
    }

    this.bezierLine = null

    if (this.historyEntry) {
      this.historyEntry.actions!.push({
        action: 'endLine',
        params: [x, y]
      })
      this.history?.push(this.historyEntry)
      this.historyEntry = undefined
    }

    this.hasDrawnDot = false
    this.inputArr = []
  }

  drawLineSegment(x1: number, y1: number, x2: number, y2: number): void {
    this.lastInput.x = x2
    this.lastInput.y = y2
    this.lastInput.pressure = 1

    if (this.inputIsDrawing || x1 === undefined) {
      return
    }

    const angle = BB.pointsToAngleDeg({ x: x1, y: y1 }, { x: x2, y: y2 })
    const mouseDist = Math.sqrt(Math.pow(x2 - x1, 2.0) + Math.pow(y2 - y1, 2.0))
    const eX = (x2 - x1) / mouseDist
    const eY = (y2 - y1) / mouseDist
    let loopDist
    const bdist = this.settingSize * this.settingSpacing
    this.lineToolLastDot = this.settingSize * this.settingSpacing
    this.context.save()
    for (loopDist = this.lineToolLastDot; loopDist <= mouseDist; loopDist += bdist) {
      this.drawDot(x1 + eX * loopDist, y1 + eY * loopDist, this.settingSize, this.settingOpacity, angle)
    }
    this.context.restore()

    const historyEntry: IPenBrushHistoryEntry = {
      tool: ['brush', 'PenBrush'],
      actions: [
        {
          action: 'opacityPressure',
          params: [this.settingHasOpacityPressure]
        },
        {
          action: 'sizePressure',
          params: [this.settingHasSizePressure]
        },
        {
          action: 'setSize',
          params: [this.settingSize]
        },
        {
          action: 'setSpacing',
          params: [this.settingSpacing]
        },
        {
          action: 'setOpacity',
          params: [this.settingOpacity]
        },
        {
          action: 'setColor',
          params: [this.settingColor]
        },
        {
          action: 'setAlpha',
          params: [this.settingAlphaId]
        },
        {
          action: 'setLockAlpha',
          params: [this.settingLockLayerAlpha]
        },
        {
          action: 'drawLineSegment',
          params: [x1, y1, x2, y2]
        }
      ]
    }
    this.history?.push(historyEntry)
  }

  //IS
  isDrawing(): boolean {
    if (this.settingAlphaId === ALPHA_BRUSH_TIP && this.calligraphyBrush) {
      return this.calligraphyBrush.isDrawingStroke()
    }
    return this.inputIsDrawing
  }

  //SET
  setAlpha(a: number): void {
    if (this.settingAlphaId === a) {
      return
    }
    console.log(`[PenBrush] Setting alpha to ${a} (previous: ${this.settingAlphaId})`)
    console.log(`[PenBrush] Current cached params:`, this.calligraphyParams)
    this.settingAlphaId = a
    this.updateAlphaCanvas()

    // 如果设置为书法笔锋模式，强制重新初始化书法笔刷
    if (a === ALPHA_BRUSH_TIP) {
      this.forceInitCalligraphyBrush()
      console.log(`[PenBrush] After forceInit, brush params:`, this.calligraphyBrush?.getCalligraphyParams())
    }
  }

  setColor(c: IRGB): void {
    if (this.settingColor === c) {
      return
    }
    this.settingColor = { r: c.r, g: c.g, b: c.b }
    this.settingColorStr = 'rgb(' + this.settingColor.r + ',' + this.settingColor.g + ',' + this.settingColor.b + ')'
    this.updateAlphaCanvas()

    // 如果是书法笔锋模式，同步更新书法笔刷的颜色
    if (this.settingAlphaId === ALPHA_BRUSH_TIP && this.calligraphyBrush) {
      this.calligraphyBrush.setColor(this.settingColor)
    }
  }

  setContext(c: CanvasRenderingContext2D): void {
    this.context = c
    if (this.calligraphyBrush) {
      this.calligraphyBrush.setContext(c)
    }
  }

  setHistory(l: KlHistory): void {
    this.history = l
  }

  setSize(s: number): void {
    this.settingSize = s
    // 如果是书法笔锋模式，同步更新书法笔刷的大小
    if (this.settingAlphaId === ALPHA_BRUSH_TIP && this.calligraphyBrush) {
      this.calligraphyBrush.setSize(s)
    }
  }

  setOpacity(o: number): void {
    this.settingOpacity = o
    // 如果是书法笔锋模式，同步更新书法笔刷的透明度
    if (this.settingAlphaId === ALPHA_BRUSH_TIP && this.calligraphyBrush) {
      this.calligraphyBrush.setOpacity(o)
    }
  }

  setSpacing(s: number): void {
    this.settingSpacing = s
  }

  sizePressure(b: boolean): void {
    this.settingHasSizePressure = b
  }

  opacityPressure(b: boolean): void {
    this.settingHasOpacityPressure = b
  }

  setLockAlpha(b: boolean): void {
    this.settingLockLayerAlpha = b
  }

  //GET
  getSpacing(): number {
    return this.settingSpacing
  }

  getSize(): number {
    return this.settingSize
  }

  getOpacity(): number {
    return this.settingOpacity
  }

  getLockAlpha(): boolean {
    return this.settingLockLayerAlpha
  }

  setCalligraphyParams(params: {
    velocityInfluence: number
    pressureInfluence: number
    velocitySmoothing: number
  }): void {
    console.log(`[PenBrush] setCalligraphyParams called:`, params)

    // 总是更新缓存的参数
    this.calligraphyParams = { ...this.calligraphyParams, ...params }

    // 如果书法笔刷已初始化，立即应用
    if (this.settingAlphaId === ALPHA_BRUSH_TIP && this.calligraphyBrush) {
      this.calligraphyBrush.setCalligraphyParams(params)
    }
  }

  // 获取当前缓存的书法参数（用于UI同步）
  getCalligraphyParams(): {
    velocityInfluence: number
    pressureInfluence: number
    velocitySmoothing: number
  } {
    return { ...this.calligraphyParams }
  }

  setCalligraphyVelocityInfluence(value: number): void {
    console.log(`[PenBrush] setCalligraphyVelocityInfluence: ${value}, alphaId: ${this.settingAlphaId}, brushExists: ${!!this.calligraphyBrush}`)

    // 总是更新缓存的参数
    this.calligraphyParams.velocityInfluence = value

    // 如果书法笔刷已初始化，立即应用（不检查 alphaId，因为在撤销重放时顺序可能不同）
    if (this.calligraphyBrush) {
      this.calligraphyBrush.setCalligraphyParams({ velocityInfluence: value })
    }
  }

  setCalligraphyPressureInfluence(value: number): void {
    console.log(`[PenBrush] setCalligraphyPressureInfluence: ${value}, alphaId: ${this.settingAlphaId}, brushExists: ${!!this.calligraphyBrush}`)

    // 总是更新缓存的参数
    this.calligraphyParams.pressureInfluence = value

    // 如果书法笔刷已初始化，立即应用（不检查 alphaId，因为在撤销重放时顺序可能不同）
    if (this.calligraphyBrush) {
      this.calligraphyBrush.setCalligraphyParams({ pressureInfluence: value })
    }
  }

  setCalligraphyVelocitySmoothing(value: number): void {
    console.log(`[PenBrush] setCalligraphyVelocitySmoothing: ${value}, alphaId: ${this.settingAlphaId}, brushExists: ${!!this.calligraphyBrush}`)

    // 总是更新缓存的参数
    this.calligraphyParams.velocitySmoothing = value

    // 如果书法笔刷已初始化，立即应用（不检查 alphaId，因为在撤销重放时顺序可能不同）
    if (this.calligraphyBrush) {
      this.calligraphyBrush.setCalligraphyParams({ velocitySmoothing: value })
    }
  }
}
