/*
Base Language File
- contains hints - hints are not to be translated, they serve as help for the translator
- is the source of truth
- is in English

renaming keys across language files:
- change key name in here
- add property "oldKey" with the old key name
- run sync
-> translations are updated.
*/
{
  'switch-ui-left-right': {
    hint: 'Button that switches layout from left to right, to accommodate left handed & right handed visitors.',
    value: 'Switch left/right UI'
  },
  'toggle-show-tools': {
    hint: 'Button that hides/shows the toolbar.',
    value: 'Show/Hide Tools'
  },
  scroll: {
    hint: 'Verb. As in scrolling a page.',
    value: 'Scroll'
  },
  donate: 'Donate',
  home: {
    hint: 'button with logo, top left of tools',
    value: 'Home'
  },
  'modal-new-tab': {
    hint: 'as in browser tab',
    value: 'Open in new tab'
  },

  // Second row of buttons

  'tab-edit': {
    hint: 'label for a tab that offer filters for making color adjustments, cropping, transforming, etc.',
    value: 'Edit'
  },
  'tab-file': {
    hint: 'label for a tab that lets user save the image, import, upload, etc.',
    value: 'File'
  },

  'tool-brush': {
    hint: 'tool that lets user paint (like in ms paint or photoshop)',
    value: 'Brush'
  },
  'tool-paint-bucket': {
    value: 'Paint Bucket'
  },
  'tool-gradient': 'Gradient',
  'tool-shape': {
    hint: 'Tool for drawing rectangles, circles, lines',
    value: 'Shape'
  },
  'tool-text': {
    hint: 'Tool for placing text on canvas',
    value: 'Text'
  },
  'tool-hand': {
    hint: 'Tool for navigating (or scrolling around) the canvas. Same name in Photoshop.',
    value: 'Hand Tool'
  },
  'tool-select': 'Select Tool',
  'tool-zoom': 'Zoom',

  undo: 'Undo',
  redo: 'Redo',

  // names of brushes. (show when hovering row of icons below color selection, when using Brush tool)

  'brush-pen': {
    hint: 'Generic "brush" that feels a bit like a pen or marker, as opposed to a paint brush.',
    value: 'Pen'
  },
  'brush-blend': {
    hint: 'brush with watercolor feel. Makes colors blend/bleed into each other. Different from smudging though.',
    value: 'Blend'
  },
  'brush-sketchy': {
    hint: 'Made up brush name. Pictures drawn with this brush often look like sketches.',
    value: 'Sketchy'
  },
  'brush-pixel': 'Pixel',
  'brush-chemy': {
    hint: 'Made up brush name, derived from drawing applications "Webchemy" and "Alchemy"',
    value: 'Chemy'
  },
  'brush-smudge': {
    hint: 'Common brush/tool in image editing software. Smudges colors around canvas.',
    value: 'Smudge'
  },

  // brush settings (below that row of icons)

  'brush-size': 'Size',
  'brush-blending': {
    hint: 'Wetness. How much colors will bleed into each other.',
    value: 'Blending'
  },
  'brush-toggle-pressure': {
    hint: 'google "pressure sensitivity". Feature of Wacom tablets, Apple Pencil, XP Pen, etc.',
    value: 'Toggle Pressure Sensitivity'
  },

  'brush-pen-circle': {
    hint: 'as in circle shape',
    value: 'Circle'
  },
  'brush-pen-chalk': {
    hint: 'as in chalk texture',
    value: 'Chalk'
  },
  'brush-pen-calligraphy': {
    hint: 'like a "chisel tip marker"',
    value: 'Calligraphy'
  },
  'brush-pen-square': {
    hint: 'as in square shape',
    value: 'Square'
  },

  'brush-sketchy-scale': {
    hint: 'How far the connecting lines of sketchy brush can reach.',
    value: 'Scale'
  },

  'brush-pixel-dither': {
    hint: 'Dithering/halftone pattern',
    value: 'Dither'
  },

  'brush-chemy-fill': {
    hint: 'Like in MS Paint. A shape can have be outlined, and filled.',
    value: 'Fill'
  },
  'brush-chemy-stroke': 'Stroke',
  'brush-chemy-mirror-x': 'Horizontal Symmetry',
  'brush-chemy-mirror-y': 'Vertical Symmetry',
  'brush-chemy-gradient': {
    hint: 'as in color gradient',
    value: 'Gradient'
  },

  'brush-eraser-transparent-bg': {
    hint: 'as in transparent background/bottom layer',
    value: 'Transparent Background'
  },

  stabilizer: {
    hint: 'Common feature in drawing software to make lines smoother',
    value: 'Stabilizer'
  },
  'stabilizer-title': {
    hint: '-title keys are usually more verbose description. shown as a tooltip.',
    value: 'Stroke Stabilizer'
  },

  eyedropper: 'Eyedropper',
  'secondary-color': {
    hint: 'or sometimes called "background" color. Photoshop, MS Paint, etc. allow user to switch between two colors.',
    value: 'Secondary Color'
  },
  'manual-color-input': {
    hint: 'A dialog where users can manually type the color, each channel a number. "mci" keys all related to this dialog.',
    value: 'Manual Color Input'
  },
  'mci-hex': {
    hint: 'as in hex code. Hex representation of color. „#f00“ → red',
    value: 'Hex'
  },
  'mci-copy': {
    hint: 'verb',
    value: 'Copy'
  },

  'modal-ok': {
    hint: 'main confirmation button in dialogs. Can be to confirm an action, not just acknowledge information.',
    value: 'Ok'
  },
  'modal-cancel': 'Cancel',
  'modal-close': {
    hint: 'as in close a dialog box',
    value: 'Close'
  },

  // Layer tab. When pressing layer icon (below the tools)

  'layers-active-layer': {
    hint: 'currently selected layer that user is drawing on',
    value: 'Active Layer'
  },
  'layers-layer': 'Layer',
  'layers-copy': {
    hint: 'noun',
    value: 'copy'
  },
  'layers-blending': {
    hint: 'Common feature in image editing & drawing software. All layers-blend- keys relate to this. https://en.wikipedia.org/wiki/Blend_modes',
    value: 'Blending'
  },
  'layers-new': {
    hint: 'action',
    value: 'New Layer'
  },
  'layers-remove': {
    hint: 'action',
    value: 'Remove Layer'
  },
  'layers-duplicate': {
    hint: 'action',
    value: 'Duplicate Layer'
  },
  'layers-merge': {
    hint: 'action',
    value: 'Merge with layer below'
  },
  'layers-merge-all': 'Merge all',
  'layers-rename': {
    hint: 'action',
    value: 'Rename'
  },
  'layers-active-layer-visible': {
    value: 'Active layer is visible'
  },
  'layers-active-layer-hidden': {
    value: 'Active layer is hidden'
  },
  'layers-visibility-toggle': {
    value: 'Layer Visibility'
  },

  // Layer blend modes as seen in Photoshop and other image editing software

  'layers-blend-normal': 'normal',
  'layers-blend-darken': 'darken',
  'layers-blend-multiply': 'multiply',
  'layers-blend-color-burn': 'color burn',
  'layers-blend-lighten': 'lighten',
  'layers-blend-screen': 'screen',
  'layers-blend-color-dodge': 'color dodge',
  'layers-blend-overlay': 'overlay',
  'layers-blend-soft-light': 'soft light',
  'layers-blend-hard-light': 'hard light',
  'layers-blend-difference': 'difference',
  'layers-blend-exclusion': 'exclusion',
  'layers-blend-hue': 'hue',
  'layers-blend-saturation': 'saturation',
  'layers-blend-color': 'color',
  'layers-blend-luminosity': 'luminosity',

  'layers-rename-title': {
    hint: '-title in code usually means it shows up in a tooltip, or it’s the header of a dialog box, so it can be a little bit longer.',
    value: 'Rename Layer'
  },
  'layers-rename-name': {
    hint: 'noun',
    value: 'Name'
  },
  'layers-rename-clear': {
    hint: 'aka action of deleting entered name',
    value: 'Clear Name'
  },
  'layers-rename-sketch': {
    hint: 'noun. Name suggestion for a layer',
    value: 'Sketch'
  },
  'layers-rename-colors': {
    hint: 'noun. Name suggestion for a layer',
    value: 'Colors'
  },
  'layers-rename-shading': {
    hint: 'noun. Name suggestion for a layer',
    value: 'Shading'
  },
  'layers-rename-lines': {
    hint: 'noun. Name suggestion for a layer',
    value: 'Lines'
  },
  'layers-rename-effects': {
    hint: 'noun. Name suggestion for a layer',
    value: 'Effects'
  },
  'layers-rename-foreground': {
    hint: 'noun. Name suggestion for a layer',
    value: 'Foreground'
  },

  'layers-merge-modal-title': 'Merge/Mix Layers',
  'layers-merge-description': {
    hint: 'After ":" a list of mix/blend modes is shown.',
    value: 'Merges the selected layer with the one underneath. Select the mix mode:'
  },

  // File tab (left of three dots)

  'file-no-autosave': {
    hint: "let user know they can't autosave, and there's no cloud storage. Keep short so fits on one line",
    value: 'No autosave, no cloud storage'
  },
  'file-new': {
    hint: 'action',
    value: 'New'
  },
  'file-import': {
    hint: 'action',
    value: 'Import'
  },
  'file-save': {
    hint: 'action',
    value: 'Save'
  },
  'file-format': {
    value: 'File Format'
  },
  'file-copy': {
    hint: 'verb',
    value: 'Copy'
  },
  'file-copy-title': 'Copy To Clipboard',
  'file-share': {
    hint: 'Common feature on phones. Pressing share button allows to send data to a different app, e.g. as a text message to someone.',
    value: 'Share'
  },

  // Browser Storage (near bottom of file tab)

  'file-storage': {
    hint: 'Made up term. Allows users to store drawing in the browser. (not synced across devices)',
    value: 'Browser Storage'
  },
  'file-storage-thumb-title': {
    hint: 'when reopening or reloading',
    value: 'Restores when reopening page'
  },
  'file-storage-about': {
    hint: 'link to Browser Storage help/user manual',
    value: 'About Browser Storage'
  },
  'file-storage-cant-access': {
    hint: 'an error. Can’t access the storage.',
    value: "Can't access"
  },
  'file-storage-empty': {
    hint: 'nothing stored',
    value: 'Empty'
  },
  'file-storage-store': {
    hint: 'verb',
    value: 'Store'
  },
  'file-storage-clear': 'Clear',
  'file-storage-storing': {
    hint: 'while it is in the process of storing',
    value: 'Storing'
  },
  'file-storage-overwrite': {
    hint: 'overwrite what is already stored',
    value: 'Overwrite'
  },
  'file-storage-min-ago': {
    hint: "{x} <- needs to stay, it's a placeholder.",
    value: '{x}min ago'
  },
  'file-storage-hours-ago': '{x}h ago',
  'file-storage-days-ago': '{x}d ago',
  'file-storage-month-ago': '> 1month ago',
  'file-storage-restored': 'Restored from Browser Storage',
  'file-storage-stored': 'Stored to Browser Storage',
  'file-storage-failed': 'Failed to store to Browser Storage',
  'file-storage-failed-1': 'Failed to store. Possible causes:',
  'file-storage-failed-2': 'Out of disk space',
  'file-storage-failed-3': 'Storage disabled in incognito tab',
  'file-storage-failed-4': {
    hint: 'as in: unsupported feature',
    value: "Browser doesn't support storage"
  },
  'file-storage-failed-clear': 'Failed to clear.',
  'file-upload': {
    hint: 'verb',
    value: 'Upload'
  },

  'cleared-layer': {
    hint: 'the layer has been cleared. It is now empty',
    value: 'Cleared layer'
  },
  'cleared-selected-area': 'Cleared selected area',
  filled: {
    hint: 'layer got filled with a color',
    value: 'Filled'
  },
  'filled-selected-area': 'Filled selection',

  'new-title': 'New Image',
  'new-current': {
    hint: 'as in "reuse the current image resolution"',
    value: 'Current'
  },

  // image dimension presets

  'new-fit': {
    hint: 'make dimensions of image fit your window size',
    value: 'Fit'
  },
  'new-oversize': {
    hint: 'like oversized clothes.',
    value: 'Oversize'
  },
  'new-square': {
    hint: 'square image format',
    value: 'Square'
  },
  'new-landscape': {
    hint: 'https://en.wikipedia.org/wiki/Page_orientation',
    value: 'Landscape'
  },
  'new-portrait': 'Portrait',
  'new-screen': {
    hint: 'resolution of computer screen',
    value: 'Screen'
  },
  'new-video': {
    hint: 'dimensions of a video (usually 16:9)',
    value: 'Video'
  },
  'new-din-paper': {
    hint: 'https://en.wikipedia.org/wiki/Paper_size',
    value: 'DIN Paper'
  },

  'new-px': {
    hint: 'abbreviation of pixel',
    value: 'px'
  },
  'new-ratio': {
    hint: 'aspect ratio',
    value: 'Ratio'
  },

  'upload-title': {
    hint: '"Imgur" is a website name.',
    value: 'Upload to Imgur'
  },
  'upload-link-notice': {
    hint: 'Explaining to user even though their upload is unlisted, they should be careful who they share the link with. No password protection.',
    value: 'Anyone with the link to your uploaded image will be able to view it.'
  },
  'upload-name': {
    hint: 'noun',
    value: 'Title'
  },
  'upload-title-untitled': 'Untitled',
  'upload-caption': {
    hint: 'as in image caption / description',
    value: 'Caption'
  },
  'upload-submit': {
    hint: 'verb',
    value: 'Upload'
  },
  'upload-uploading': {
    hint: 'in the process of uploading',
    value: 'Uploading...'
  },
  'upload-success': 'Upload Successful',
  'upload-failed': 'Upload failed.',
  'upload-delete': 'To delete your image from Imgur visit:',

  'cropcopy-title-copy': 'Copy To Clipboard',
  'cropcopy-title-crop': {
    hint: 'Cropping is a common feature in image editing.',
    value: 'Crop'
  },
  'cropcopy-click-hold': 'Right-click or press hold to copy.',
  'cropcopy-btn-copy': {
    hint: 'confirmation button. Should be short.',
    value: 'To Clipboard'
  },
  'cropcopy-copied': 'Copied.',
  'cropcopy-btn-crop': {
    hint: 'confirmation button. Should be short.',
    value: 'Apply Crop'
  },

  'crop-drag-to-crop': {
    hint: 'drag the mouse to adjust cropping',
    value: 'Drag to crop'
  },

  // Edit tab
  // Button labels in the edit tab. Should be short so the buttons only use one line. Some are found in Photoshop, etc.

  'filter-crop-extend': 'Crop/Extend',
  'filter-flip': 'Flip',
  'filter-perspective': {
    hint: 'perspective transformation',
    value: 'Perspective'
  },
  'filter-resize': 'Resize',
  'filter-rotate': 'Rotate',
  'filter-transform': {
    hint: 'more generic transformation. Commonly known as "free transform"',
    value: 'Transform'
  },
  'filter-bright-contrast': {
    hint: 'brightness contrast',
    value: 'Bright/Contrast'
  },
  'filter-curves': {
    hint: 'color curves',
    value: 'Curves'
  },
  'filter-hue-sat': 'Hue/Saturation',
  'filter-invert': {
    hint: 'invert the colors',
    value: 'Invert'
  },
  'filter-tilt-shift': {
    hint: 'or just depth-of-field. https://en.wikipedia.org/wiki/Tilt%E2%80%93shift_photography',
    value: 'Tilt Shift'
  },
  'filter-to-alpha': {
    hint: 'feature that can isolate line drawings if it is a black and white image.',
    value: 'To Alpha'
  },
  'filter-triangle-blur': {
    hint: 'or just blur',
    value: 'Triangle Blur'
  },
  'filter-unsharp-mask': {
    hint: 'or just sharpen, https://en.wikipedia.org/wiki/Unsharp_masking',
    value: 'Unsharp Mask'
  },

  // dialogs that open when clicking on the buttons in the edit tab.

  'filter-crop-title': {
    hint: 'same as the button, but can be longer',
    value: 'Crop / Extend'
  },
  'filter-crop-description': {
    hint: 'descriptions explain the "filter" a tiny bit. Most importantly it says if it’s applied to a single layer, or the entire image (all layer)',
    value: 'Crop or extend the image.'
  },
  'filter-crop-left': 'Left',
  'filter-crop-right': 'Right',
  'filter-crop-top': 'Top',
  'filter-crop-bottom': 'Bottom',
  'filter-crop-rule-thirds': {
    hint: 'https://en.wikipedia.org/wiki/Rule_of_thirds',
    value: 'Rule of Thirds'
  },
  'filter-crop-fill': {
    hint: 'If image gets extended, what color to fill that new area with',
    value: 'Fill'
  },

  'filter-flip-title': {
    hint: 'same as the button, but can be longer',
    value: 'Flip'
  },
  'filter-flip-description': 'Flips layer or whole image.',
  'filter-flip-horizontal': 'Horizontal',
  'filter-flip-vertical': 'Vertical',
  'filter-flip-image': 'Flip Image',
  'filter-flip-layer': 'Flip Layer',

  'filter-perspective-title': {
    hint: 'same as the button, but can be longer',
    value: 'Perspective'
  },
  'filter-perspective-description': 'Transforms the selected layer.',

  'filter-resize-title': {
    hint: 'same as the button, but can be longer',
    value: 'Resize'
  },
  'filter-resize-description': 'Resizes the image.',

  'filter-rotate-title': {
    hint: 'same as the button, but can be longer',
    value: 'Rotate'
  },
  'filter-rotate-description': 'Rotates the image.',

  'filter-transform-empty': {
    hint: 'nothing on the layer, just transparency',
    value: 'Layer is empty.'
  },
  'filter-transform-title': {
    hint: 'same as the button, but can be longer',
    value: 'Transform'
  },
  'filter-transform-description': {
    hint: 'as in the Shift key.',
    value: 'Transforms selected layer. Hold Shift for additional behavior.'
  },
  'filter-transform-rotation': {
    hint: 'or Angle',
    value: 'Rotation'
  },
  'filter-transform-flip': 'Flip',
  'filter-transform-center': {
    hint: 'verb or noun',
    value: 'Center'
  },
  'filter-transform-constrain': {
    hint: 'short version of "constrain proportions" – feature that locks the aspect ratio',
    value: 'Constrain'
  },
  'filter-transform-snap': {
    hint: 'or snapping. Common feature in graphics software. Makes stuff snap into place so it’s easier to align elements in an image',
    value: 'Snap'
  },
  'filter-transform-snap-title': {
    hint: 'same as above, but as a tooltip',
    value: 'Snap Rotation And Position'
  },

  'filter-bright-contrast-title': {
    hint: 'same as the button, but can be longer',
    value: 'Brightness / Contrast'
  },
  'filter-bright-contrast-description': 'Change brightness and contrast for the selected layer.',
  'filter-bright-contrast-brightness': 'Brightness',
  'filter-bright-contrast-contrast': 'Contrast',

  'filter-curves-title': {
    hint: 'same as the button, but can be longer',
    value: 'Curves'
  },
  'filter-curves-description': 'Apply curves on the selected layer.',
  'filter-curves-all': 'All',

  'filter-hue-sat-title': {
    hint: 'same as the button, but can be longer',
    value: 'Hue / Saturation'
  },
  'filter-hue-sat-description': 'Change hue and saturation for the selected layer.',
  'filter-hue-sat-hue': 'Hue',
  'filter-hue-sat-saturation': 'Saturation',

  'filter-applied': {
    hint: 'used like this: „Invert“ applied. Informs that a filter was applied',
    value: 'applied'
  },

  'filter-tilt-shift-title': {
    hint: 'same as the button, but can be longer',
    value: 'Tilt Shift'
  },
  'filter-tilt-shift-description': 'Applies tilt shift on the selected layer.',
  'filter-tilt-shift-blur': {
    hint: 'words can be individually translated',
    value: 'Blur Radius'
  },
  'filter-tilt-shift-gradient': {
    hint: 'words can be individually translated',
    value: 'Gradient Radius'
  },

  'filter-to-alpha-title': {
    hint: 'same as the button, but can be longer',
    value: 'To Alpha'
  },
  'filter-to-alpha-description': {
    hint: 'after ":" follow two options',
    value: 'Generates alpha channel for selected layer from:'
  },
  'filter-to-alpha-inverted-lum': {
    hint: 'luminance is same as the layer blend mode "luminosity"',
    value: 'Inverted Luminance'
  },
  'filter-to-alpha-lum': 'Luminance',
  'filter-to-alpha-replace': {
    hint: 'What color to replace the rgb channels with',
    value: 'Replace RGB'
  },

  'filter-triangle-blur-title': {
    hint: 'same as the button, but can be longer',
    value: 'Triangle Blur'
  },
  'filter-triangle-blur-description': 'Applies triangle blur on the selected layer.',

  'filter-unsharp-mask-title': {
    hint: 'same as the button, but can be longer',
    value: 'Unsharp Mask'
  },
  'filter-unsharp-mask-description': 'Sharpens the selected layer by scaling pixels away from the average of their neighbors.',
  'filter-unsharp-mask-strength': 'Strength',

  'filter-grid': 'Grid',
  'filter-grid-description': 'Draws grid on selected layer.',

  'filter-noise': 'Noise',
  'filter-noise-description': 'Adds noise to selected layer.',
  'filter-noise-scale': 'Scale',
  'filter-noise-alpha': 'Alpha',

  'filter-pattern': 'Pattern',
  'filter-pattern-description': 'Generates pattern on selected layer. Drag the preview for further controls.',

  'filter-distort': 'Distort',
  'filter-distort-description': 'Distorts the selected layer.',
  'filter-distort-phase': 'Phase',
  'filter-distort-stepsize': 'Step Size',
  'filter-distort-sync-xy': 'Sync XY',

  'filter-vanish-point': 'Vanish Point',
  'filter-vanish-point-title': 'Vanishing Point',
  'filter-vanish-point-description': 'Adds vanishing point to selected layer. Drag preview to move.',
  'filter-vanish-point-lines': {
    hint: 'As in: the number of lines',
    value: 'Lines'
  },

  'import-opening': {
    hint: 'in the process of opening a large file',
    value: 'Opening file...'
  },
  'import-title': 'Import Image',
  'import-too-large': 'Image too large, will be downscaled.',
  'import-btn-as-layer': {
    hint: 'import as layer. Button label, should be short',
    value: 'As Layer'
  },
  'import-btn-as-image': {
    hint: 'import as image. Button label, should be short',
    value: 'As Image'
  },
  'import-as-layer-title': 'Import Image as New Layer',
  'import-as-layer-description': 'Adjust the position of the imported image.',
  'import-as-layer-limit-reached': {
    hint: 'Kleki allows only a certain number of layers',
    value: 'Layer limit reached. Image will be placed on existing layer.'
  },
  'import-as-layer-fit': {
    hint: 'verb. imported image made to fit inside canvas.',
    value: 'Fit'
  },
  'import-flatten': {
    hint: 'Turn image that has multiple layers into an image that has only one layer.',
    value: 'Flatten image'
  },
  'import-unsupported-file': {
    hint: 'e.g. user tried to open a WAV',
    value: 'Unsupported file type. See Help for supported types.'
  },
  'import-broken-file': {
    hint: 'if an image file is broken',
    value: "Couldn't load image. File might be corrupted."
  },
  'import-psd-unsupported': {
    hint: 'PSD is photoshop file format. flatten as in „flatten image“ in photoshop. Merged all layers',
    value: 'Unsupported features. PSD had to be flattened.'
  },
  'import-psd-limited-support': {
    hint: 'PSD has lots of fancy features that Kleki doesn\'t have, so when importing a PSD it often has to simplify things. So it might look different compared to when opening in Photoshop. We’re informing the user if they "flatten" the image it will be accurate despite losing layers. The user has to weigh, do they care about having layers, or do they care about it looking right.',
    value: 'PSD support is limited. Flattened will more likely look correct.'
  },
  'import-psd-too-large': 'Image exceeds maximum dimensions of {x} x {x} pixels. Unable to import.',
  'import-psd-size': 'Image size',

  // hand tool
  'hand-reset': {
    hint: 'resets zoom (to 100%) and rotation (to 0°)',
    value: 'Reset'
  },
  'hand-fit': {
    hint: 'makes canvas cover whole working area',
    value: 'Fit'
  },
  'hand-inertia-scrolling': {
    hint: 'Canvas will continue to move even after releasing pointer.',
    value: 'Inertia Scrolling'
  },

  // paint bucket tool
  'bucket-tolerance': {
    hint: 'a common feature for paint bucket',
    value: 'Tolerance'
  },
  'bucket-sample': {
    hint: 'like "taking a sample"',
    value: 'Sample'
  },
  'bucket-sample-title': 'Which layers to sample color from',
  'bucket-sample-all': {
    hint: 'all layers',
    value: 'All'
  },
  'bucket-sample-active': {
    hint: 'currently active layer',
    value: 'Active'
  },
  'bucket-sample-above': {
    hint: 'layer above active layer',
    value: 'Above'
  },
  'bucket-grow': {
    hint: 'verb. Or expand.',
    value: 'Grow'
  },
  'bucket-grow-title': 'Grow filled area (in pixels)',
  'bucket-contiguous': {
    hint: 'common setting for paint bucket in image editing software',
    value: 'Contiguous'
  },
  'bucket-contiguous-title': {
    hint: 'tooltip explaining Contiguous',
    value: 'Only fill connected areas'
  },

  // gradient tool
  'gradient-linear': 'Linear',
  'gradient-linear-mirror': 'Linear-Mirror',
  'gradient-radial': 'Radial',

  // shape tool
  'shape-stroke': 'Stroke',
  'shape-fill': 'Fill',
  'shape-rect': {
    hint: 'rectangle shape',
    value: 'Rectangle'
  },
  'shape-ellipse': {
    hint: 'ellipse shape',
    value: 'Ellipse'
  },
  'shape-line': 'Line',
  'shape-line-width': 'Line Width',
  'shape-outwards': {
    hint: 'grows shape from the center outwards',
    value: 'Outwards'
  },
  'shape-fixed': {
    hint: 'fixed ratio of 1:1',
    value: 'Fixed 1:1'
  },

  // text tool
  'text-instruction': 'Click canvas to place text',
  'text-title': 'Add Text',
  'text-text': 'Text',
  'text-font': 'Font',
  'text-placeholder': {
    hint: 'placeholder in text input.',
    value: 'Your text'
  },
  'text-color': 'Color',
  'text-size': 'Size',
  'text-line-height': 'Line Height',
  'text-letter-spacing': 'Letter Spacing',
  'text-left': {
    hint: 'left, center, right text formatting like in Word',
    value: 'Left'
  },
  'text-center': 'Center',
  'text-right': 'Right',
  'text-italic': 'Italic',
  'text-bold': 'Bold',

  // select tool (and transform)
  'select-select': 'Select',
  'select-transform': 'Transform',
  'select-lasso': 'Lasso',
  'select-polygon': 'Polygon',
  'select-boolean-replace': 'Replace',
  'select-boolean-add': 'Add',
  'select-boolean-subtract': 'Subtract',
  'select-all': 'All',
  'select-invert': 'Invert',
  'select-reset': 'Reset',
  'select-fill': 'Fill',
  'select-erase': 'Erase',
  'select-transform-clone': 'Clone',
  'select-transform-clone-applied': 'Cloned',
  'select-transform-move-to-layer': 'Move to layer:',
  'select-transform-applied': 'Transformation applied',
  'select-transform-empty': 'Selected area on active layer is empty.',

  'save-reminder-title': {
    hint: 'Save Reminder is a popup that shows up after 20 minutes of not saving. Allows user to save on the spot.',
    value: 'Unsaved Work'
  },
  'save-reminder-text': {
    hint: 'turns into: "...saved in <strong>12 minutes</strong>. Save..."',
    value: 'Image was not saved in {a} minutes{b}. Save now to prevent eventual loss.'
  },
  'save-reminder-save-psd': 'Save As PSD',
  'save-reminder-psd-layers': 'PSD will remember all layers.',
  'backup-drawing': {
    hint: '(Embed) If the upload failed, the user can backup/download their drawing.',
    value: 'You can backup your drawing.'
  },

  submit: 'Submit',
  'submit-title': {
    hint: 'tooltip when hovering "Submit" button',
    value: 'Submit Drawing'
  },
  'submit-prompt': {
    hint: 'Dialog asking for confirmation',
    value: 'Submit drawing?'
  },
  'submit-submitting': {
    hint: 'in the process of uploading',
    value: 'Submitting'
  },

  'embed-init-loading': {
    hint: 'when opening the app',
    value: 'Loading app'
  },
  'embed-init-waiting': {
    hint: 'The app is loaded, but it’s still waiting for image',
    value: 'Waiting for image'
  },

  unsaved: 'Unsaved',
  help: {
    hint: 'or user manual',
    value: 'Help'
  },

  'tab-settings': 'Settings',
  'settings-language': 'Language',
  'settings-language-reload': {
    hint: 'changes will be visible after reloading the page',
    value: 'Will update after reloading.'
  },
  'settings-theme': 'Theme',
  'settings-save-reminder-label': 'Save Reminder',
  'settings-save-reminder-disabled': 'disabled',
  'settings-save-reminder-confirm-title': 'Turn off Save Reminder?',
  'settings-save-reminder-confirm-a': "There is no autosave and browser tabs don't last forever. If you don't periodically save you will likely lose work.",
  'settings-save-reminder-confirm-b': 'Disable at your own risk?',
  'settings-save-reminder-confirm-disable': 'Disable',
  'theme-dark': 'Dark',
  'theme-light': 'Light',
  'terms-of-service': {
    oldKey: 'upload-tos',
    value: 'Terms of Service'
  },
  licenses: {
    hint: 'as in software licenses',
    value: 'Licenses'
  },
  'source-code': 'Source Code',
  auto: {
    hint: 'automatically decided',
    value: 'auto'
  },

  'zoom-in': 'Zoom In',
  'zoom-out': 'Zoom Out',
  radius: 'Radius',
  'constrain-proportions': 'Constrain Proportions',
  width: 'Width',
  height: 'Height',
  opacity: {
    hint: 'for layer opacity, brush opacity, text opacity, ...',
    value: 'Opacity'
  },
  red: 'Red',
  green: 'Green',
  blue: 'Blue',
  eraser: {
    hint: 'noun. As short as possible',
    value: 'Eraser'
  },
  center: {
    hint: 'verb',
    value: 'Center'
  },
  layers: {
    hint: 'layers in the context of image editing software',
    value: 'Layers'
  },
  background: {
    hint: 'as in background layer',
    value: 'Background'
  },
  'scaling-algorithm': {
    hint: 'Label for dropdown where user chooses which computer graphics algorithm should be used to scale the image',
    value: 'Scaling Algorithm'
  },
  'algorithm-smooth': 'Smooth',
  'algorithm-pixelated': 'Pixelated',
  preview: {
    hint: 'subject. When applying a filter user sees a preview of what the result would look like.',
    value: 'Preview'
  },
  'angle-snap': {
    hint: 'Snapping. Like when transforming an image and it snaps to the side of the frame.',
    value: 'Snap'
  },
  'angle-snap-title': '45° Angle Snapping',
  'lock-alpha': {
    hint: 'Short. Freezes the alpha channel of image. Images consist of 4 channels (red, green, blue, alpha). Alpha lock also found in Procreate and photoshop',
    value: 'Lock Alpha'
  },
  'lock-alpha-title': {
    hint: "same as 'lock-alpha' but longer version. Shows up as a tooltip when hovering",
    value: "Locks layer's alpha channel"
  },
  reverse: {
    hint: 'Reversing the direction of a color gradient',
    value: 'Reverse'
  },
  'compare-before': 'Before',
  'compare-after': 'After',
  loading: 'Loading',
  more: 'More',
  'x-minutes': '{x}min',
  wip: 'Work in progress',
  'browser-zoom-help': 'Double-tap or pinch-out to reset browser zoom.',
  dismiss: 'Dismiss'
}
