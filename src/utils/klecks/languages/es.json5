{
  'switch-ui-left-right': {
    hint: 'Button that switches layout from left to right, to accommodate left handed & right handed visitors.',
    original: 'Switch left/right UI',
    value: 'Cambia UI a izquierda/derecha'
  },
  'toggle-show-tools': {
    hint: 'Button that hides/shows the toolbar.',
    original: 'Show/Hide Tools',
    value: 'Mostrar/Ocultar Herramientas'
  },
  scroll: {
    hint: 'Verb. As in scrolling a page.',
    original: 'Scroll',
    value: 'Desplazar'
  },
  donate: {
    original: 'Donate',
    value: 'Donar'
  },
  home: {
    hint: 'button with logo, top left of tools',
    original: 'Home',
    value: 'Inicio'
  },
  'modal-new-tab': {
    hint: 'as in browser tab',
    original: 'Open in new tab',
    value: 'Abrir en pestaña nueva'
  },
  'tab-edit': {
    hint: 'label for a tab that offer filters for making color adjustments, cropping, transforming, etc.',
    original: 'Edit',
    value: 'Editar'
  },
  'tab-file': {
    hint: 'label for a tab that lets user save the image, import, upload, etc.',
    original: 'File',
    value: 'Archivo'
  },
  'tool-brush': {
    hint: 'tool that lets user paint (like in ms paint or photoshop)',
    original: 'Brush',
    value: 'Pincel'
  },
  'tool-paint-bucket': {
    original: 'Paint Bucket',
    value: 'Bote de pintura'
  },
  'tool-gradient': {
    original: 'Gradient',
    value: 'Gradiente'
  },
  'tool-shape': {
    hint: 'Tool for drawing rectangles, circles, lines',
    original: 'Shape',
    value: 'Forma'
  },
  'tool-text': {
    hint: 'Tool for placing text on canvas',
    original: 'Text',
    value: 'Texto'
  },
  'tool-hand': {
    hint: 'Tool for navigating (or scrolling around) the canvas. Same name in Photoshop.',
    original: 'Hand Tool',
    value: 'Herramienta Mano'
  },
  'tool-select': {
    original: 'Select Tool',
    value: 'Herramienta de Selección'
  },
  'tool-zoom': {
    original: 'Zoom',
    value: 'Zoom'
  },
  undo: {
    original: 'Undo',
    value: 'Deshacer'
  },
  redo: {
    original: 'Redo',
    value: 'Rehacer'
  },
  'brush-pen': {
    hint: 'Generic "brush" that feels a bit like a pen or marker, as opposed to a paint brush.',
    original: 'Pen',
    value: 'Pluma'
  },
  'brush-blend': {
    hint: 'brush with watercolor feel. Makes colors blend/bleed into each other. Different from smudging though.',
    original: 'Blend',
    value: 'Mezcla'
  },
  'brush-sketchy': {
    hint: 'Made up brush name. Pictures drawn with this brush often look like sketches.',
    original: 'Sketchy',
    value: 'Esbozo'
  },
  'brush-pixel': {
    original: 'Pixel',
    value: 'Píxel'
  },
  'brush-chemy': {
    hint: 'Made up brush name, derived from drawing applications "Webchemy" and "Alchemy"',
    original: 'Chemy',
    value: 'Chemy'
  },
  'brush-smudge': {
    hint: 'Common brush/tool in image editing software. Smudges colors around canvas.',
    original: 'Smudge',
    value: 'Difuminado'
  },
  'brush-size': {
    original: 'Size',
    value: 'Tamaño'
  },
  'brush-blending': {
    hint: 'Wetness. How much colors will bleed into each other.',
    original: 'Blending',
    value: 'Mezcla'
  },
  'brush-toggle-pressure': {
    hint: 'google "pressure sensitivity". Feature of Wacom tablets, Apple Pencil, XP Pen, etc.',
    original: 'Toggle Pressure Sensitivity',
    value: 'Alternar sensibilidad a la presión'
  },
  'brush-pen-circle': {
    hint: 'as in circle shape',
    original: 'Circle',
    value: 'Círculo'
  },
  'brush-pen-chalk': {
    hint: 'as in chalk texture',
    original: 'Chalk',
    value: 'Tiza'
  },
  'brush-pen-calligraphy': {
    hint: 'like a "chisel tip marker"',
    original: 'Calligraphy',
    value: 'Caligrafía'
  },
  'brush-pen-square': {
    hint: 'as in square shape',
    original: 'Square',
    value: 'Cuadrado'
  },
  'brush-sketchy-scale': {
    hint: 'How far the connecting lines of sketchy brush can reach.',
    original: 'Scale',
    value: 'Escala'
  },
  'brush-pixel-dither': {
    hint: 'Dithering/halftone pattern',
    original: 'Dither',
    value: 'Tramado'
  },
  'brush-chemy-fill': {
    hint: 'Like in MS Paint. A shape can have be outlined, and filled.',
    original: 'Fill',
    value: 'Llenar'
  },
  'brush-chemy-stroke': {
    original: 'Stroke',
    value: 'Trazo'
  },
  'brush-chemy-mirror-x': {
    original: 'Horizontal Symmetry',
    value: 'Horizontal simetría'
  },
  'brush-chemy-mirror-y': {
    original: 'Vertical Symmetry',
    value: 'Vertical simetría'
  },
  'brush-chemy-gradient': {
    hint: 'as in color gradient',
    original: 'Gradient',
    value: 'Gradiente'
  },
  'brush-eraser-transparent-bg': {
    hint: 'as in transparent background/bottom layer',
    original: 'Transparent Background',
    value: 'Fondo Transparente'
  },
  stabilizer: {
    hint: 'Common feature in drawing software to make lines smoother',
    original: 'Stabilizer',
    value: 'Estabilizador'
  },
  'stabilizer-title': {
    hint: '-title keys are usually more verbose description. shown as a tooltip.',
    original: 'Stroke Stabilizer',
    value: 'Estabilizador de trazo'
  },
  eyedropper: {
    original: 'Eyedropper',
    value: 'Cuentagotas'
  },
  'secondary-color': {
    hint: 'or sometimes called "background" color. Photoshop, MS Paint, etc. allow user to switch between two colors.',
    original: 'Secondary Color',
    value: 'Color Secundario'
  },
  'manual-color-input': {
    hint: 'A dialog where users can manually type the color, each channel a number. "mci" keys all related to this dialog.',
    original: 'Manual Color Input',
    value: 'Entrada manual de color'
  },
  'mci-hex': {
    hint: 'as in hex code. Hex representation of color. „#f00“ → red',
    original: 'Hex',
    value: 'Código hexadecimal'
  },
  'mci-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Copiar'
  },
  'modal-ok': {
    hint: 'main confirmation button in dialogs. Can be to confirm an action, not just acknowledge information.',
    original: 'Ok',
    value: 'Ok'
  },
  'modal-cancel': {
    original: 'Cancel',
    value: 'Cancelar'
  },
  'modal-close': {
    hint: 'as in close a dialog box',
    original: 'Close',
    value: 'Cerrar'
  },
  'layers-active-layer': {
    hint: 'currently selected layer that user is drawing on',
    original: 'Active Layer',
    value: 'Capa activa'
  },
  'layers-layer': {
    original: 'Layer',
    value: 'Capa'
  },
  'layers-copy': {
    hint: 'noun',
    original: 'copy',
    value: 'Copiar'
  },
  'layers-blending': {
    hint: 'Common feature in image editing & drawing software. All layers-blend- keys relate to this. https://en.wikipedia.org/wiki/Blend_modes',
    original: 'Blending',
    value: 'Fusión'
  },
  'layers-new': {
    hint: 'action',
    original: 'New Layer',
    value: 'Nueva capa'
  },
  'layers-remove': {
    hint: 'action',
    original: 'Remove Layer',
    value: 'Eliminar capa'
  },
  'layers-duplicate': {
    hint: 'action',
    original: 'Duplicate Layer',
    value: 'Duplicar capa'
  },
  'layers-merge': {
    hint: 'action',
    original: 'Merge with layer below',
    value: 'Combinar hacia abajo'
  },
  'layers-merge-all': {
    original: 'Merge all',
    value: 'Combinar todas'
  },
  'layers-rename': {
    hint: 'action',
    original: 'Rename',
    value: 'Nuevo Nombre'
  },
  'layers-active-layer-visible': {
    original: 'Active layer is visible',
    value: 'Capa activa es visible'
  },
  'layers-active-layer-hidden': {
    original: 'Active layer is hidden',
    value: 'Capa activa está escondida'
  },
  'layers-visibility-toggle': {
    original: 'Layer Visibility',
    value: 'Visibilidad de capa'
  },
  'layers-blend-normal': {
    original: 'normal',
    value: 'normal'
  },
  'layers-blend-darken': {
    original: 'darken',
    value: 'oscurecer'
  },
  'layers-blend-multiply': {
    original: 'multiply',
    value: 'multiplicar'
  },
  'layers-blend-color-burn': {
    original: 'color burn',
    value: 'subexponer color'
  },
  'layers-blend-lighten': {
    original: 'lighten',
    value: 'aclarar'
  },
  'layers-blend-screen': {
    original: 'screen',
    value: 'trama'
  },
  'layers-blend-color-dodge': {
    original: 'color dodge',
    value: 'sobreexponer color'
  },
  'layers-blend-overlay': {
    original: 'overlay',
    value: 'superponer'
  },
  'layers-blend-soft-light': {
    original: 'soft light',
    value: 'luz suave'
  },
  'layers-blend-hard-light': {
    original: 'hard light',
    value: 'luz fuerte'
  },
  'layers-blend-difference': {
    original: 'difference',
    value: 'diferencia'
  },
  'layers-blend-exclusion': {
    original: 'exclusion',
    value: 'exclusión'
  },
  'layers-blend-hue': {
    original: 'hue',
    value: 'tono'
  },
  'layers-blend-saturation': {
    original: 'saturation',
    value: 'saturación'
  },
  'layers-blend-color': {
    original: 'color',
    value: 'color'
  },
  'layers-blend-luminosity': {
    original: 'luminosity',
    value: 'luminosidad'
  },
  'layers-rename-title': {
    hint: '-title in code usually means it shows up in a tooltip, or it’s the header of a dialog box, so it can be a little bit longer.',
    original: 'Rename Layer',
    value: 'Nuevo nombre de capa'
  },
  'layers-rename-name': {
    hint: 'noun',
    original: 'Name',
    value: 'Nombre de capa'
  },
  'layers-rename-clear': {
    hint: 'aka action of deleting entered name',
    original: 'Clear Name',
    value: 'Borrar nombre'
  },
  'layers-rename-sketch': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Sketch',
    value: 'Bosquejo'
  },
  'layers-rename-colors': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Colors',
    value: 'Colores'
  },
  'layers-rename-shading': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Shading',
    value: 'Sombreado'
  },
  'layers-rename-lines': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Lines',
    value: 'Líneas'
  },
  'layers-rename-effects': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Effects',
    value: 'Efectos'
  },
  'layers-rename-foreground': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Foreground',
    value: 'Primer Plano'
  },
  'layers-merge-modal-title': {
    original: 'Merge/Mix Layers',
    value: 'Unir/Mezclar Capas'
  },
  'layers-merge-description': {
    hint: 'After ":" a list of mix/blend modes is shown.',
    original: 'Merges the selected layer with the one underneath. Select the mix mode:',
    value: 'Fusiona la capa seleccionada con la que está abajo. Seleccione el modo de mezcla:'
  },
  'file-no-autosave': {
    hint: "let user know they can't autosave, and there's no cloud storage. Keep short so fits on one line",
    original: 'No autosave, no cloud storage',
    value: 'No autoguarda, no almacenamiento en la nube'
  },
  'file-new': {
    hint: 'action',
    original: 'New',
    value: 'Nuevo'
  },
  'file-import': {
    hint: 'action',
    original: 'Import',
    value: 'Importar'
  },
  'file-save': {
    hint: 'action',
    original: 'Save',
    value: 'Guardar'
  },
  'file-format': {
    original: 'File Format',
    value: 'Formato de archivo'
  },
  'file-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Copiar'
  },
  'file-copy-title': {
    original: 'Copy To Clipboard',
    value: 'Copiar a portapapeles'
  },
  'file-share': {
    hint: 'Common feature on phones. Pressing share button allows to send data to a different app, e.g. as a text message to someone.',
    original: 'Share',
    value: 'Compartir'
  },
  'file-storage': {
    hint: 'Made up term. Allows users to store drawing in the browser. (not synced across devices)',
    original: 'Browser Storage',
    value: 'Almacenamiento del navegador'
  },
  'file-storage-thumb-title': {
    hint: 'when reopening or reloading',
    original: 'Restores when reopening page',
    value: 'Restaura al volver a abrir la página.'
  },
  'file-storage-about': {
    hint: 'link to Browser Storage help/user manual',
    original: 'About Browser Storage',
    value: 'Acerca del almacenamiento del navegador'
  },
  'file-storage-cant-access': {
    hint: 'an error. Can’t access the storage.',
    original: "Can't access",
    value: 'no puedo acceder'
  },
  'file-storage-empty': {
    hint: 'nothing stored',
    original: 'Empty',
    value: 'Vacío'
  },
  'file-storage-store': {
    hint: 'verb',
    original: 'Store',
    value: 'Almacenar'
  },
  'file-storage-clear': {
    original: 'Clear',
    value: 'Borrar'
  },
  'file-storage-storing': {
    hint: 'while it is in the process of storing',
    original: 'Storing',
    value: 'Guardando'
  },
  'file-storage-overwrite': {
    hint: 'overwrite what is already stored',
    original: 'Overwrite',
    value: 'Sobrescribir'
  },
  'file-storage-min-ago': {
    hint: "{x} <- needs to stay, it's a placeholder.",
    original: '{x}min ago',
    value: 'Hace {x} minutos'
  },
  'file-storage-hours-ago': {
    original: '{x}h ago',
    value: 'Hace {x} horas'
  },
  'file-storage-days-ago': {
    original: '{x}d ago',
    value: 'Hace {x} dias'
  },
  'file-storage-month-ago': {
    original: '> 1month ago',
    value: 'Hace >1 mes'
  },
  'file-storage-restored': {
    original: 'Restored from Browser Storage',
    value: 'Restaurado desde el almacenamiento del navegador'
  },
  'file-storage-stored': {
    original: 'Stored to Browser Storage',
    value: 'Almacenado en el almacenamiento del navegador'
  },
  'file-storage-failed': {
    original: 'Failed to store to Browser Storage',
    value: 'No se pudo almacenar en el almacenamiento del navegador'
  },
  'file-storage-failed-1': {
    original: 'Failed to store. Possible causes:',
    value: 'No se pudo almacenar. Causas Posibles:'
  },
  'file-storage-failed-2': {
    original: 'Out of disk space',
    value: 'No espacio en el disco'
  },
  'file-storage-failed-3': {
    original: 'Storage disabled in incognito tab',
    value: 'Almacenamiento deshabilitado en la pestaña de incógnito'
  },
  'file-storage-failed-4': {
    hint: 'as in: unsupported feature',
    original: "Browser doesn't support storage",
    value: 'El navegador no admite almacenamiento'
  },
  'file-storage-failed-clear': {
    original: 'Failed to clear.',
    value: 'No se pudo borrar'
  },
  'file-upload': {
    hint: 'verb',
    original: 'Upload',
    value: 'Subir'
  },
  'cleared-layer': {
    hint: 'the layer has been cleared. It is now empty',
    original: 'Cleared layer',
    value: 'Capa borrada'
  },
  'cleared-selected-area': {
    original: 'Cleared selected area',
    value: 'área seleccionada borrada'
  },
  filled: {
    hint: 'layer got filled with a color',
    original: 'Filled',
    value: 'Lleno'
  },
  'filled-selected-area': {
    original: 'Filled selection',
    value: 'área seleccionada llena'
  },
  'new-title': {
    original: 'New Image',
    value: 'Nueva Imagen'
  },
  'new-current': {
    hint: 'as in "reuse the current image resolution"',
    original: 'Current',
    value: 'Presente'
  },
  'new-fit': {
    hint: 'make dimensions of image fit your window size',
    original: 'Fit',
    value: 'Adaptar'
  },
  'new-oversize': {
    hint: 'like oversized clothes.',
    original: 'Oversize',
    value: 'Descomunal'
  },
  'new-square': {
    hint: 'square image format',
    original: 'Square',
    value: 'Cuadro'
  },
  'new-landscape': {
    hint: 'https://en.wikipedia.org/wiki/Page_orientation',
    original: 'Landscape',
    value: 'Paisaje'
  },
  'new-portrait': {
    original: 'Portrait',
    value: 'Retrato'
  },
  'new-screen': {
    hint: 'resolution of computer screen',
    original: 'Screen',
    value: 'Pantalla'
  },
  'new-video': {
    hint: 'dimensions of a video (usually 16:9)',
    original: 'Video',
    value: 'Video'
  },
  'new-din-paper': {
    hint: 'https://en.wikipedia.org/wiki/Paper_size',
    original: 'DIN Paper',
    value: 'Papel DIN'
  },
  'new-px': {
    hint: 'abbreviation of pixel',
    original: 'px',
    value: 'px'
  },
  'new-ratio': {
    hint: 'aspect ratio',
    original: 'Ratio',
    value: 'Proporción'
  },
  'upload-title': {
    hint: '"Imgur" is a website name.',
    original: 'Upload to Imgur',
    value: 'Subir a Imgur'
  },
  'upload-link-notice': {
    hint: 'Explaining to user even though their upload is unlisted, they should be careful who they share the link with. No password protection.',
    original: 'Anyone with the link to your uploaded image will be able to view it.',
    value: 'Cualquiera que tenga el enlace a la imagen cargada podrá verla.'
  },
  'upload-name': {
    hint: 'noun',
    original: 'Title',
    value: 'Título'
  },
  'upload-title-untitled': {
    original: 'Untitled',
    value: 'Sin título'
  },
  'upload-caption': {
    hint: 'as in image caption / description',
    original: 'Caption',
    value: 'Descripción'
  },
  'upload-submit': {
    hint: 'verb',
    original: 'Upload',
    value: 'Subir'
  },
  'upload-uploading': {
    hint: 'in the process of uploading',
    original: 'Uploading...',
    value: 'Subiendo...'
  },
  'upload-success': {
    original: 'Upload Successful',
    value: 'Subida exitosa'
  },
  'upload-failed': {
    original: 'Upload failed.',
    value: 'Subida fallida'
  },
  'upload-delete': {
    original: 'To delete your image from Imgur visit:',
    value: 'Para eliminar su imagen de Imgur visite:'
  },
  'cropcopy-title-copy': {
    original: 'Copy To Clipboard',
    value: 'Copiar a portapapeles'
  },
  'cropcopy-title-crop': {
    hint: 'Cropping is a common feature in image editing.',
    original: 'Crop',
    value: 'Cortar'
  },
  'cropcopy-click-hold': {
    original: 'Right-click or press hold to copy.',
    value: 'Clic derecho o mantenga presionado para copiar.'
  },
  'cropcopy-btn-copy': {
    hint: 'confirmation button. Should be short.',
    original: 'To Clipboard',
    value: 'A portapapeles'
  },
  'cropcopy-copied': {
    original: 'Copied.',
    value: 'Copiado.'
  },
  'cropcopy-btn-crop': {
    hint: 'confirmation button. Should be short.',
    original: 'Apply Crop',
    value: 'Aplicar Cortar'
  },
  'crop-drag-to-crop': {
    hint: 'drag the mouse to adjust cropping',
    original: 'Drag to crop',
    value: 'Arrastrar para cortar'
  },
  'filter-crop-extend': {
    original: 'Crop/Extend',
    value: 'Recortar/Extender'
  },
  'filter-flip': {
    original: 'Flip',
    value: 'Voltear'
  },
  'filter-perspective': {
    hint: 'perspective transformation',
    original: 'Perspective',
    value: 'Perspectiva'
  },
  'filter-resize': {
    original: 'Resize',
    value: 'Cambiar tamaño'
  },
  'filter-rotate': {
    original: 'Rotate',
    value: 'Girar'
  },
  'filter-transform': {
    hint: 'more generic transformation. Commonly known as "free transform"',
    original: 'Transform',
    value: 'Transformar'
  },
  'filter-bright-contrast': {
    hint: 'brightness contrast',
    original: 'Bright/Contrast',
    value: 'Brillo/Contraste'
  },
  'filter-curves': {
    hint: 'color curves',
    original: 'Curves',
    value: 'Curvas'
  },
  'filter-hue-sat': {
    original: 'Hue/Saturation',
    value: 'Matiz/Saturación'
  },
  'filter-invert': {
    hint: 'invert the colors',
    original: 'Invert',
    value: 'Invertir'
  },
  'filter-tilt-shift': {
    hint: 'or just depth-of-field. https://en.wikipedia.org/wiki/Tilt%E2%80%93shift_photography',
    original: 'Tilt Shift',
    value: 'Cambio de Inclinación'
  },
  'filter-to-alpha': {
    hint: 'feature that can isolate line drawings if it is a black and white image.',
    original: 'To Alpha',
    value: 'A Alfa'
  },
  'filter-triangle-blur': {
    hint: 'or just blur',
    original: 'Triangle Blur',
    value: 'Desenfoque de Triángulo'
  },
  'filter-unsharp-mask': {
    hint: 'or just sharpen, https://en.wikipedia.org/wiki/Unsharp_masking',
    original: 'Unsharp Mask',
    value: 'Máscara de Enfoque'
  },
  'filter-crop-title': {
    hint: 'same as the button, but can be longer',
    original: 'Crop / Extend',
    value: 'Recortar/Extender'
  },
  'filter-crop-description': {
    hint: 'descriptions explain the "filter" a tiny bit. Most importantly it says if it’s applied to a single layer, or the entire image (all layer)',
    original: 'Crop or extend the image.',
    value: 'Recorta o amplía la imagen.'
  },
  'filter-crop-left': {
    original: 'Left',
    value: 'Izquierda'
  },
  'filter-crop-right': {
    original: 'Right',
    value: 'Derecha'
  },
  'filter-crop-top': {
    original: 'Top',
    value: 'Arriba'
  },
  'filter-crop-bottom': {
    original: 'Bottom',
    value: 'Abajo'
  },
  'filter-crop-rule-thirds': {
    hint: 'https://en.wikipedia.org/wiki/Rule_of_thirds',
    original: 'Rule of Thirds',
    value: 'Regla de los Tercios'
  },
  'filter-crop-fill': {
    hint: 'If image gets extended, what color to fill that new area with',
    original: 'Fill',
    value: 'Llenar'
  },
  'filter-flip-title': {
    hint: 'same as the button, but can be longer',
    original: 'Flip',
    value: 'Voltear'
  },
  'filter-flip-description': {
    original: 'Flips layer or whole image.',
    value: 'Voltea la capa o imagen completa.'
  },
  'filter-flip-horizontal': {
    original: 'Horizontal',
    value: 'Horizontal'
  },
  'filter-flip-vertical': {
    original: 'Vertical',
    value: 'Vertical'
  },
  'filter-flip-image': {
    original: 'Flip Image',
    value: 'Voltear Imagen'
  },
  'filter-flip-layer': {
    original: 'Flip Layer',
    value: 'Voltear Capa'
  },
  'filter-perspective-title': {
    hint: 'same as the button, but can be longer',
    original: 'Perspective',
    value: 'Perspectiva'
  },
  'filter-perspective-description': {
    original: 'Transforms the selected layer.',
    value: 'Transforma la capa seleccionada.'
  },
  'filter-resize-title': {
    hint: 'same as the button, but can be longer',
    original: 'Resize',
    value: 'Cambiar tamaño'
  },
  'filter-resize-description': {
    original: 'Resizes the image.',
    value: 'Cambia el tamaño de la imagen.'
  },
  'filter-rotate-title': {
    hint: 'same as the button, but can be longer',
    original: 'Rotate',
    value: 'Gira'
  },
  'filter-rotate-description': {
    original: 'Rotates the image.',
    value: 'Gira la imagen.'
  },
  'filter-transform-empty': {
    hint: 'nothing on the layer, just transparency',
    original: 'Layer is empty.',
    value: 'La capa está vacía.'
  },
  'filter-transform-title': {
    hint: 'same as the button, but can be longer',
    original: 'Transform',
    value: 'Transformar'
  },
  'filter-transform-description': {
    hint: 'as in the Shift key.',
    original: 'Transforms selected layer. Hold Shift for additional behavior.',
    value: 'Transforma la capa seleccionada. Mantenga presionada tecla Mayús para ver comportamiento adicional.'
  },
  'filter-transform-rotation': {
    hint: 'or Angle',
    original: 'Rotation',
    value: 'Rotación'
  },
  'filter-transform-flip': {
    original: 'Flip',
    value: 'Voltear'
  },
  'filter-transform-center': {
    hint: 'verb or noun',
    original: 'Center',
    value: 'Centro'
  },
  'filter-transform-constrain': {
    hint: 'short version of "constrain proportions" – feature that locks the aspect ratio',
    original: 'Constrain',
    value: 'Constreñir'
  },
  'filter-transform-snap': {
    hint: 'or snapping. Common feature in graphics software. Makes stuff snap into place so it’s easier to align elements in an image',
    original: 'Snap',
    value: 'Ajustar'
  },
  'filter-transform-snap-title': {
    hint: 'same as above, but as a tooltip',
    original: 'Snap Rotation And Position',
    value: 'Chasquido Rotación y Posición'
  },
  'filter-bright-contrast-title': {
    hint: 'same as the button, but can be longer',
    original: 'Brightness / Contrast',
    value: 'Brillo/Contraste'
  },
  'filter-bright-contrast-description': {
    original: 'Change brightness and contrast for the selected layer.',
    value: 'Cambie brillo y contraste de la capa seleccionada.'
  },
  'filter-bright-contrast-brightness': {
    original: 'Brightness',
    value: 'Brillo'
  },
  'filter-bright-contrast-contrast': {
    original: 'Contrast',
    value: 'Contraste'
  },
  'filter-curves-title': {
    hint: 'same as the button, but can be longer',
    original: 'Curves',
    value: 'Curvas'
  },
  'filter-curves-description': {
    original: 'Apply curves on the selected layer.',
    value: 'Aplicar curvas en la capa seleccionada.'
  },
  'filter-curves-all': {
    original: 'All',
    value: 'Todo'
  },
  'filter-hue-sat-title': {
    hint: 'same as the button, but can be longer',
    original: 'Hue / Saturation',
    value: 'Matiz/Saturación'
  },
  'filter-hue-sat-description': {
    original: 'Change hue and saturation for the selected layer.',
    value: 'Cambia el tono y la saturación de la capa seleccionada.'
  },
  'filter-hue-sat-hue': {
    original: 'Hue',
    value: 'Matiz'
  },
  'filter-hue-sat-saturation': {
    original: 'Saturation',
    value: 'Saturación'
  },
  'filter-applied': {
    hint: 'used like this: „Invert“ applied. Informs that a filter was applied',
    original: 'applied',
    value: 'aplicado'
  },
  'filter-tilt-shift-title': {
    hint: 'same as the button, but can be longer',
    original: 'Tilt Shift',
    value: 'Cambio de Inclinación'
  },
  'filter-tilt-shift-description': {
    original: 'Applies tilt shift on the selected layer.',
    value: 'Aplica cambio de inclinación en la capa seleccionada.'
  },
  'filter-tilt-shift-blur': {
    hint: 'words can be individually translated',
    original: 'Blur Radius',
    value: 'Radio de Desenfoque'
  },
  'filter-tilt-shift-gradient': {
    hint: 'words can be individually translated',
    original: 'Gradient Radius',
    value: 'Radio de Gradiente'
  },
  'filter-to-alpha-title': {
    hint: 'same as the button, but can be longer',
    original: 'To Alpha',
    value: 'A Alfa'
  },
  'filter-to-alpha-description': {
    hint: 'after ":" follow two options',
    original: 'Generates alpha channel for selected layer from:',
    value: 'Genera canal alfa para la capa seleccionada desde:'
  },
  'filter-to-alpha-inverted-lum': {
    hint: 'luminance is same as the layer blend mode "luminosity"',
    original: 'Inverted Luminance',
    value: 'Luminancia Invertida'
  },
  'filter-to-alpha-lum': {
    original: 'Luminance',
    value: 'Luminancia'
  },
  'filter-to-alpha-replace': {
    hint: 'What color to replace the rgb channels with',
    original: 'Replace RGB',
    value: 'Reemplazar RGB'
  },
  'filter-triangle-blur-title': {
    hint: 'same as the button, but can be longer',
    original: 'Triangle Blur',
    value: 'Desenfoque de Triángulo'
  },
  'filter-triangle-blur-description': {
    original: 'Applies triangle blur on the selected layer.',
    value: 'Aplica desenfoque triangular en la capa seleccionada.'
  },
  'filter-unsharp-mask-title': {
    hint: 'same as the button, but can be longer',
    original: 'Unsharp Mask',
    value: 'Máscara de Enfoque'
  },
  'filter-unsharp-mask-description': {
    original: 'Sharpens the selected layer by scaling pixels away from the average of their neighbors.',
    value: 'Enfoca la capa seleccionada alejando los píxeles del promedio de sus vecinos.'
  },
  'filter-unsharp-mask-strength': {
    original: 'Strength',
    value: 'Fortaleza'
  },
  'filter-grid': {
    original: 'Grid',
    value: 'Cuadrícula'
  },
  'filter-grid-description': {
    original: 'Draws grid on selected layer.',
    value: 'Dibuja la cuadrícula en la capa seleccionada.'
  },
  'filter-noise': {
    original: 'Noise',
    value: 'Ruido'
  },
  'filter-noise-description': {
    original: 'Adds noise to selected layer.',
    value: 'Agrega ruido a la capa seleccionada.'
  },
  'filter-noise-scale': {
    original: 'Scale',
    value: 'Escala'
  },
  'filter-noise-alpha': {
    original: 'Alpha',
    value: 'Alfa'
  },
  'filter-pattern': {
    original: 'Pattern',
    value: 'Patrón'
  },
  'filter-pattern-description': {
    original: 'Generates pattern on selected layer. Drag the preview for further controls.',
    value: 'Genera patrón en la capa seleccionada. Arrastre la vista previa para obtener más controles.'
  },
  'filter-distort': {
    original: 'Distort',
    value: 'Distorsionar'
  },
  'filter-distort-description': {
    original: 'Distorts the selected layer.',
    value: 'Distorsiona la capa seleccionada.'
  },
  'filter-distort-phase': {
    original: 'Phase',
    value: 'Fase'
  },
  'filter-distort-stepsize': {
    original: 'Step Size',
    value: 'Tamaño de Paso'
  },
  'filter-distort-sync-xy': {
    original: 'Sync XY',
    value: 'Sincronizar XY'
  },
  'filter-vanish-point': {
    original: 'Vanish Point',
    value: 'Punto de Fuga'
  },
  'filter-vanish-point-title': {
    original: 'Vanishing Point',
    value: 'Punto de Fuga'
  },
  'filter-vanish-point-description': {
    original: 'Adds vanishing point to selected layer. Drag preview to move.',
    value: 'Agrega un punto de fuga a la capa seleccionada. Arrastra la vista previa para moverla.'
  },
  'filter-vanish-point-lines': {
    hint: 'As in: the number of lines',
    original: 'Lines',
    value: 'Líneas'
  },
  'import-opening': {
    hint: 'in the process of opening a large file',
    original: 'Opening file...',
    value: 'Abriendo archivo...'
  },
  'import-title': {
    original: 'Import Image',
    value: 'Importar Imagen'
  },
  'import-too-large': {
    original: 'Image too large, will be downscaled.',
    value: 'Imagen demasiado grande, será reducida.'
  },
  'import-btn-as-layer': {
    hint: 'import as layer. Button label, should be short',
    original: 'As Layer',
    value: 'Como Capa'
  },
  'import-btn-as-image': {
    hint: 'import as image. Button label, should be short',
    original: 'As Image',
    value: 'Como Imagen'
  },
  'import-as-layer-title': {
    original: 'Import Image as New Layer',
    value: 'Importar imagen como nueva capa'
  },
  'import-as-layer-description': {
    original: 'Adjust the position of the imported image.',
    value: 'Ajuste la posición de la imagen importada.'
  },
  'import-as-layer-limit-reached': {
    hint: 'Kleki allows only a certain number of layers',
    original: 'Layer limit reached. Image will be placed on existing layer.',
    value: 'Se alcanzó el límite de capas. La imagen se colocará en la capa existente.'
  },
  'import-as-layer-fit': {
    hint: 'verb. imported image made to fit inside canvas.',
    original: 'Fit',
    value: 'Adaptar'
  },
  'import-flatten': {
    hint: 'Turn image that has multiple layers into an image that has only one layer.',
    original: 'Flatten image',
    value: 'Aplanar Imagen'
  },
  'import-unsupported-file': {
    hint: 'e.g. user tried to open a WAV',
    original: 'Unsupported file type. See Help for supported types.',
    value: 'Tipo de archivo no soportado. Consulte Ayuda para conocer los tipos admitidos.'
  },
  'import-broken-file': {
    hint: 'if an image file is broken',
    original: "Couldn't load image. File might be corrupted.",
    value: 'No se pudo cargar la imagen. Es posible que el archivo esté dañado.'
  },
  'import-psd-unsupported': {
    hint: 'PSD is photoshop file format. flatten as in „flatten image“ in photoshop. Merged all layers',
    original: 'Unsupported features. PSD had to be flattened.',
    value: 'Funciones no compatibles. El PSD tuvo que ser aplanado.'
  },
  'import-psd-limited-support': {
    hint: 'PSD has lots of fancy features that Kleki doesn\'t have, so when importing a PSD it often has to simplify things. So it might look different compared to when opening in Photoshop. We’re informing the user if they "flatten" the image it will be accurate despite losing layers. The user has to weigh, do they care about having layers, or do they care about it looking right.',
    original: 'PSD support is limited. Flattened will more likely look correct.',
    value: 'La compatibilidad con PSD es limitada. Es más probable que aplanado se vea correcto.'
  },
  'import-psd-too-large': {
    original: 'Image exceeds maximum dimensions of {x} x {x} pixels. Unable to import.',
    value: 'La imagen supera las dimensiones máximas de {x} x {x} píxeles. No se puede importar.'
  },
  'import-psd-size': {
    original: 'Image size',
    value: 'Tamaño de Imagen'
  },
  'hand-reset': {
    hint: 'resets zoom (to 100%) and rotation (to 0°)',
    original: 'Reset',
    value: 'Reiniciar'
  },
  'hand-fit': {
    hint: 'makes canvas cover whole working area',
    original: 'Fit',
    value: 'Caber'
  },
  'hand-inertia-scrolling': {
    hint: 'Canvas will continue to move even after releasing pointer.',
    original: 'Inertia Scrolling',
    value: 'Desplazamiento por incercia'
  },
  'bucket-tolerance': {
    hint: 'a common feature for paint bucket',
    original: 'Tolerance',
    value: 'Tolerancia'
  },
  'bucket-sample': {
    hint: 'like "taking a sample"',
    original: 'Sample',
    value: 'Probar'
  },
  'bucket-sample-title': {
    original: 'Which layers to sample color from',
    value: 'De qué capas tomar muestras de color'
  },
  'bucket-sample-all': {
    hint: 'all layers',
    original: 'All',
    value: 'Todos'
  },
  'bucket-sample-active': {
    hint: 'currently active layer',
    original: 'Active',
    value: 'Activo'
  },
  'bucket-sample-above': {
    hint: 'layer above active layer',
    original: 'Above',
    value: 'Arriba'
  },
  'bucket-grow': {
    hint: 'verb. Or expand.',
    original: 'Grow',
    value: 'Crecer'
  },
  'bucket-grow-title': {
    original: 'Grow filled area (in pixels)',
    value: 'Área llena de crecimiento (en píxeles)'
  },
  'bucket-contiguous': {
    hint: 'common setting for paint bucket in image editing software',
    original: 'Contiguous',
    value: 'Contiguo'
  },
  'bucket-contiguous-title': {
    hint: 'tooltip explaining Contiguous',
    original: 'Only fill connected areas',
    value: 'Llene solo áreas conectadas'
  },
  'gradient-linear': {
    original: 'Linear',
    value: 'Lineal'
  },
  'gradient-linear-mirror': {
    original: 'Linear-Mirror',
    value: 'Espejo-lineal'
  },
  'gradient-radial': {
    original: 'Radial',
    value: 'Radial'
  },
  'shape-stroke': {
    original: 'Stroke',
    value: 'Trazo'
  },
  'shape-fill': {
    original: 'Fill',
    value: 'Llenar'
  },
  'shape-rect': {
    hint: 'rectangle shape',
    original: 'Rectangle',
    value: 'Rectángulo'
  },
  'shape-ellipse': {
    hint: 'ellipse shape',
    original: 'Ellipse',
    value: 'Elipse'
  },
  'shape-line': {
    original: 'Line',
    value: 'Línea'
  },
  'shape-line-width': {
    original: 'Line Width',
    value: 'Ancho de línea'
  },
  'shape-outwards': {
    hint: 'grows shape from the center outwards',
    original: 'Outwards',
    value: 'Exteriormente'
  },
  'shape-fixed': {
    hint: 'fixed ratio of 1:1',
    original: 'Fixed 1:1',
    value: 'Fijo 1:1'
  },
  'text-instruction': {
    original: 'Click canvas to place text',
    value: 'Clic el lienzo para colocar el texto'
  },
  'text-title': {
    original: 'Add Text',
    value: 'Añadir texto'
  },
  'text-text': {
    original: 'Text',
    value: 'Texto'
  },
  'text-font': {
    original: 'Font',
    value: 'Tipo de letra'
  },
  'text-placeholder': {
    hint: 'placeholder in text input.',
    original: 'Your text',
    value: 'Tu texto'
  },
  'text-color': {
    original: 'Color',
    value: 'Color'
  },
  'text-size': {
    original: 'Size',
    value: 'Tamaño'
  },
  'text-line-height': {
    original: 'Line Height',
    value: 'Altura de la línea'
  },
  'text-letter-spacing': {
    original: 'Letter Spacing',
    value: 'Espaciado de letras'
  },
  'text-left': {
    hint: 'left, center, right text formatting like in Word',
    original: 'Left',
    value: 'Izquierda'
  },
  'text-center': {
    original: 'Center',
    value: 'Centro'
  },
  'text-right': {
    original: 'Right',
    value: 'Derecho'
  },
  'text-italic': {
    original: 'Italic',
    value: 'Cursiva'
  },
  'text-bold': {
    original: 'Bold',
    value: 'Negrita'
  },
  'select-select': {
    original: 'Select',
    value: 'Seleccionar'
  },
  'select-transform': {
    original: 'Transform',
    value: 'Transformar'
  },
  'select-lasso': {
    original: 'Lasso',
    value: 'Lazo'
  },
  'select-polygon': {
    original: 'Polygon',
    value: 'Polígono'
  },
  'select-boolean-replace': {
    original: 'Replace',
    value: 'Reemplazar'
  },
  'select-boolean-add': {
    original: 'Add',
    value: 'Añadir'
  },
  'select-boolean-subtract': {
    original: 'Subtract',
    value: 'Sustraer'
  },
  'select-all': {
    original: 'All',
    value: 'Todo'
  },
  'select-invert': {
    original: 'Invert',
    value: 'Invertir'
  },
  'select-reset': {
    original: 'Reset',
    value: 'Reiniciar'
  },
  'select-fill': {
    original: 'Fill',
    value: 'Llenar'
  },
  'select-erase': {
    original: 'Erase',
    value: 'Borrar'
  },
  'select-transform-clone': {
    original: 'Clone',
    value: 'Clonar'
  },
  'select-transform-clone-applied': {
    original: 'Cloned',
    value: 'Clonado'
  },
  'select-transform-move-to-layer': {
    original: 'Move to layer:',
    value: 'Mover a capa:'
  },
  'select-transform-applied': {
    original: 'Transformation applied',
    value: 'Transformación aplicada'
  },
  'select-transform-empty': {
    original: 'Selected area on active layer is empty.',
    value: 'área seleccionada en la capa activa está vacía.'
  },
  'save-reminder-title': {
    hint: 'Save Reminder is a popup that shows up after 20 minutes of not saving. Allows user to save on the spot.',
    original: 'Unsaved Work',
    value: 'Trabajo no esta guardado'
  },
  'save-reminder-text': {
    hint: 'turns into: "...saved in <strong>12 minutes</strong>. Save..."',
    original: 'Image was not saved in {a} minutes{b}. Save now to prevent eventual loss.',
    value: 'La imagen no se guardó en {a} minutos{b}. Guarde ahora para evitar una eventual pérdida.'
  },
  'save-reminder-save-psd': {
    original: 'Save As PSD',
    value: 'Guardar como PSD'
  },
  'save-reminder-psd-layers': {
    original: 'PSD will remember all layers.',
    value: 'PSD recordará todas las capas.'
  },
  'backup-drawing': {
    hint: '(Embed) If the upload failed, the user can backup/download their drawing.',
    original: 'You can backup your drawing.',
    value: 'Puedes hacer una copia de seguridad de tu dibujo.'
  },
  submit: {
    original: 'Submit',
    value: 'Entregar'
  },
  'submit-title': {
    hint: 'tooltip when hovering "Submit" button',
    original: 'Submit Drawing',
    value: 'Entrega Dibujo'
  },
  'submit-prompt': {
    hint: 'Dialog asking for confirmation',
    original: 'Submit drawing?',
    value: 'Entrega dibujo?'
  },
  'submit-submitting': {
    hint: 'in the process of uploading',
    original: 'Submitting',
    value: 'Sumisión...'
  },
  'embed-init-loading': {
    hint: 'when opening the app',
    original: 'Loading app',
    value: 'Cargando aplicación'
  },
  'embed-init-waiting': {
    hint: 'The app is loaded, but it’s still waiting for image',
    original: 'Waiting for image',
    value: 'Esperando imagen'
  },
  unsaved: {
    original: 'Unsaved',
    value: 'No Guardado'
  },
  help: {
    hint: 'or user manual',
    original: 'Help',
    value: 'Ayuda'
  },
  'tab-settings': {
    original: 'Settings',
    value: 'Ajustes'
  },
  'settings-language': {
    original: 'Language',
    value: 'Idioma'
  },
  'settings-language-reload': {
    hint: 'changes will be visible after reloading the page',
    original: 'Will update after reloading.',
    value: 'Se actualizará después de recargar.'
  },
  'settings-theme': {
    original: 'Theme',
    value: 'Tema'
  },
  'settings-save-reminder-label': {
    original: 'Save Reminder',
    value: 'Requerda Guardar'
  },
  'settings-save-reminder-disabled': {
    original: 'disabled',
    value: 'Desactivado'
  },
  'settings-save-reminder-confirm-title': {
    original: 'Turn off Save Reminder?',
    value: 'Apagar a Requerda Guardar?'
  },
  'settings-save-reminder-confirm-a': {
    original: "There is no autosave and browser tabs don't last forever. If you don't periodically save you will likely lose work.",
    value: 'No hay guardado automático y las pestañas no duran para siempre. Si no guardas periódicamente, probablemente perderá trabajo.'
  },
  'settings-save-reminder-confirm-b': {
    original: 'Disable at your own risk?',
    value: '¿Desactivar bajo su propia responsabilidad?'
  },
  'settings-save-reminder-confirm-disable': {
    original: 'Disable',
    value: 'Desactivar'
  },
  'theme-dark': {
    original: 'Dark',
    value: 'Oscuro'
  },
  'theme-light': {
    original: 'Light',
    value: 'Claro'
  },
  'terms-of-service': {
    original: 'Terms of Service',
    value: 'Términos de servicio'
  },
  licenses: {
    hint: 'as in software licenses',
    original: 'Licenses',
    value: 'Licencias'
  },
  'source-code': {
    original: 'Source Code',
    value: 'Código Fuente'
  },
  auto: {
    hint: 'automatically decided',
    original: 'auto',
    value: 'auto'
  },
  'zoom-in': {
    original: 'Zoom In',
    value: 'Acercarse'
  },
  'zoom-out': {
    original: 'Zoom Out',
    value: 'Disminuir el zoom'
  },
  radius: {
    original: 'Radius',
    value: 'Radio'
  },
  'constrain-proportions': {
    original: 'Constrain Proportions',
    value: 'Restringir las proporciones'
  },
  width: {
    original: 'Width',
    value: 'Ancho'
  },
  height: {
    original: 'Height',
    value: 'Altura'
  },
  opacity: {
    hint: 'for layer opacity, brush opacity, text opacity, ...',
    original: 'Opacity',
    value: 'Opacidad'
  },
  red: {
    original: 'Red',
    value: 'Rojo'
  },
  green: {
    original: 'Green',
    value: 'Verde'
  },
  blue: {
    original: 'Blue',
    value: 'Azul'
  },
  eraser: {
    hint: 'noun. As short as possible',
    original: 'Eraser',
    value: 'Borrador'
  },
  center: {
    hint: 'verb',
    original: 'Center',
    value: 'Centro'
  },
  layers: {
    hint: 'layers in the context of image editing software',
    original: 'Layers',
    value: 'Capas'
  },
  background: {
    hint: 'as in background layer',
    original: 'Background',
    value: 'Fondo'
  },
  'scaling-algorithm': {
    hint: 'Label for dropdown where user chooses which computer graphics algorithm should be used to scale the image',
    original: 'Scaling Algorithm',
    value: 'Algoritmo de Escala'
  },
  'algorithm-smooth': {
    original: 'Smooth',
    value: 'Liso'
  },
  'algorithm-pixelated': {
    original: 'Pixelated',
    value: 'Pixelado'
  },
  preview: {
    hint: 'subject. When applying a filter user sees a preview of what the result would look like.',
    original: 'Preview',
    value: 'Vista Previa'
  },
  'angle-snap': {
    hint: 'Snapping. Like when transforming an image and it snaps to the side of the frame.',
    original: 'Snap',
    value: 'Ajustar'
  },
  'angle-snap-title': {
    original: '45° Angle Snapping',
    value: 'Ajuste de Ángulo de 45°'
  },
  'lock-alpha': {
    hint: 'Short. Freezes the alpha channel of image. Images consist of 4 channels (red, green, blue, alpha). Alpha lock also found in Procreate and photoshop',
    original: 'Lock Alpha',
    value: 'Cerrar Alfa'
  },
  'lock-alpha-title': {
    hint: "same as 'lock-alpha' but longer version. Shows up as a tooltip when hovering",
    original: "Locks layer's alpha channel",
    value: 'Cerrar canal alfa de la capa'
  },
  reverse: {
    hint: 'Reversing the direction of a color gradient',
    original: 'Reverse',
    value: 'Inverso'
  },
  'compare-before': {
    original: 'Before',
    value: 'Antes'
  },
  'compare-after': {
    original: 'After',
    value: 'Después'
  },
  loading: {
    original: 'Loading',
    value: 'Cargando'
  },
  more: {
    original: 'More',
    value: 'Más'
  },
  'x-minutes': {
    original: '{x}min',
    value: '{x}min'
  },
  wip: {
    original: 'Work in progress',
    value: 'Trabajo en progreso'
  },
  'browser-zoom-help': {
    original: 'Double-tap or pinch-out to reset browser zoom.',
    value: 'Haz doble clic o pellizca para resetear el zoom del buscador.'
  },
  dismiss: {
    original: 'Dismiss',
    value: 'Descartar'
  }
}
