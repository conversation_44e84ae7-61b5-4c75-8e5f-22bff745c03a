{
  'switch-ui-left-right': {
    hint: 'Button that switches layout from left to right, to accommodate left handed & right handed visitors.',
    original: 'Switch left/right UI',
    value: '<PERSON><PERSON><PERSON><PERSON> bal/jobb felhasz<PERSON>lói felületen'
  },
  'toggle-show-tools': {
    hint: 'Button that hides/shows the toolbar.',
    original: 'Show/Hide Tools',
    value: 'Eszközök megjelenítése/elrejtése'
  },
  scroll: {
    hint: 'Verb. As in scrolling a page.',
    original: 'Scroll',
    value: 'Tekercs'
  },
  donate: {
    original: 'Donate',
    value: 'Adományoz'
  },
  home: {
    hint: 'button with logo, top left of tools',
    original: 'Home',
    value: 'Otthon'
  },
  'modal-new-tab': {
    hint: 'as in browser tab',
    original: 'Open in new tab',
    value: 'Megnyitás új lapon'
  },
  'tab-edit': {
    hint: 'label for a tab that offer filters for making color adjustments, cropping, transforming, etc.',
    original: 'Edit',
    value: 'Szer<PERSON>zté<PERSON>'
  },
  'tab-file': {
    hint: 'label for a tab that lets user save the image, import, upload, etc.',
    original: 'File',
    value: 'Fájl'
  },
  'tool-brush': {
    hint: 'tool that lets user paint (like in ms paint or photoshop)',
    original: 'Brush',
    value: 'Kefe'
  },
  'tool-paint-bucket': {
    original: 'Paint Bucket',
    value: 'Festékes vödör'
  },
  'tool-gradient': {
    original: 'Gradient',
    value: 'Gradiens'
  },
  'tool-shape': {
    hint: 'Tool for drawing rectangles, circles, lines',
    original: 'Shape',
    value: 'Alak'
  },
  'tool-text': {
    hint: 'Tool for placing text on canvas',
    original: 'Text',
    value: 'Szöveg'
  },
  'tool-hand': {
    hint: 'Tool for navigating (or scrolling around) the canvas. Same name in Photoshop.',
    original: 'Hand Tool',
    value: 'Kéziszerszám'
  },
  'tool-select': {
    original: 'Select Tool',
    value: ''
  },
  'tool-zoom': {
    original: 'Zoom',
    value: 'Zoomolás'
  },
  undo: {
    original: 'Undo',
    value: 'Visszavonás'
  },
  redo: {
    original: 'Redo',
    value: 'Újra'
  },
  'brush-pen': {
    hint: 'Generic "brush" that feels a bit like a pen or marker, as opposed to a paint brush.',
    original: 'Pen',
    value: 'Toll'
  },
  'brush-blend': {
    hint: 'brush with watercolor feel. Makes colors blend/bleed into each other. Different from smudging though.',
    original: 'Blend',
    value: 'Keverék'
  },
  'brush-sketchy': {
    hint: 'Made up brush name. Pictures drawn with this brush often look like sketches.',
    original: 'Sketchy',
    value: 'Vázlatos'
  },
  'brush-pixel': {
    original: 'Pixel',
    value: 'Pixel'
  },
  'brush-chemy': {
    hint: 'Made up brush name, derived from drawing applications "Webchemy" and "Alchemy"',
    original: 'Chemy',
    value: 'Chemy'
  },
  'brush-smudge': {
    hint: 'Common brush/tool in image editing software. Smudges colors around canvas.',
    original: 'Smudge',
    value: 'Piszok'
  },
  'brush-size': {
    original: 'Size',
    value: 'Méret'
  },
  'brush-blending': {
    hint: 'Wetness. How much colors will bleed into each other.',
    original: 'Blending',
    value: 'Keverés'
  },
  'brush-toggle-pressure': {
    hint: 'google "pressure sensitivity". Feature of Wacom tablets, Apple Pencil, XP Pen, etc.',
    original: 'Toggle Pressure Sensitivity',
    value: 'Nyomásérzékenység váltása'
  },
  'brush-pen-circle': {
    hint: 'as in circle shape',
    original: 'Circle',
    value: 'Kör'
  },
  'brush-pen-chalk': {
    hint: 'as in chalk texture',
    original: 'Chalk',
    value: 'Kréta'
  },
  'brush-pen-calligraphy': {
    hint: 'like a "chisel tip marker"',
    original: 'Calligraphy',
    value: 'Kalligráfia'
  },
  'brush-pen-square': {
    hint: 'as in square shape',
    original: 'Square',
    value: 'Négyzet'
  },
  'brush-sketchy-scale': {
    hint: 'How far the connecting lines of sketchy brush can reach.',
    original: 'Scale',
    value: 'Skála'
  },
  'brush-pixel-dither': {
    hint: 'Dithering/halftone pattern',
    original: 'Dither',
    value: 'Remeg'
  },
  'brush-chemy-fill': {
    hint: 'Like in MS Paint. A shape can have be outlined, and filled.',
    original: 'Fill',
    value: 'Tölt'
  },
  'brush-chemy-stroke': {
    original: 'Stroke',
    value: 'Kontúr'
  },
  'brush-chemy-mirror-x': {
    original: 'Horizontal Symmetry',
    value: 'Vízszintes szimmetria'
  },
  'brush-chemy-mirror-y': {
    original: 'Vertical Symmetry',
    value: 'Függőleges szimmetria'
  },
  'brush-chemy-gradient': {
    hint: 'as in color gradient',
    original: 'Gradient',
    value: 'Gradiens'
  },
  'brush-eraser-transparent-bg': {
    hint: 'as in transparent background/bottom layer',
    original: 'Transparent Background',
    value: 'Átlátszó háttér'
  },
  stabilizer: {
    hint: 'Common feature in drawing software to make lines smoother',
    original: 'Stabilizer',
    value: 'Stabilizátor'
  },
  'stabilizer-title': {
    hint: '-title keys are usually more verbose description. shown as a tooltip.',
    original: 'Stroke Stabilizer',
    value: 'Löket stabilizátor'
  },
  eyedropper: {
    original: 'Eyedropper',
    value: 'Szemcseppentő'
  },
  'secondary-color': {
    hint: 'or sometimes called "background" color. Photoshop, MS Paint, etc. allow user to switch between two colors.',
    original: 'Secondary Color',
    value: 'Másodlagos szín'
  },
  'manual-color-input': {
    hint: 'A dialog where users can manually type the color, each channel a number. "mci" keys all related to this dialog.',
    original: 'Manual Color Input',
    value: 'Kézi színbevitel'
  },
  'mci-hex': {
    hint: 'as in hex code. Hex representation of color. „#f00“ → red',
    original: 'Hex',
    value: 'Hex'
  },
  'mci-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Másolat'
  },
  'modal-ok': {
    hint: 'main confirmation button in dialogs. Can be to confirm an action, not just acknowledge information.',
    original: 'Ok',
    value: 'Rendben'
  },
  'modal-cancel': {
    original: 'Cancel',
    value: 'Megszünteti'
  },
  'modal-close': {
    hint: 'as in close a dialog box',
    original: 'Close',
    value: 'Bezárás'
  },
  'layers-active-layer': {
    hint: 'currently selected layer that user is drawing on',
    original: 'Active Layer',
    value: 'Aktív réteg'
  },
  'layers-layer': {
    original: 'Layer',
    value: 'Réteg'
  },
  'layers-copy': {
    hint: 'noun',
    original: 'copy',
    value: 'másolat'
  },
  'layers-blending': {
    hint: 'Common feature in image editing & drawing software. All layers-blend- keys relate to this. https://en.wikipedia.org/wiki/Blend_modes',
    original: 'Blending',
    value: 'Keverés'
  },
  'layers-new': {
    hint: 'action',
    original: 'New Layer',
    value: 'Új réteg'
  },
  'layers-remove': {
    hint: 'action',
    original: 'Remove Layer',
    value: 'Réteg eltávolítása'
  },
  'layers-duplicate': {
    hint: 'action',
    original: 'Duplicate Layer',
    value: 'Második réteg'
  },
  'layers-merge': {
    hint: 'action',
    original: 'Merge with layer below',
    value: 'Egyesítse az alábbi réteggel'
  },
  'layers-merge-all': {
    original: 'Merge all',
    value: 'Egyesítse az összeset'
  },
  'layers-rename': {
    hint: 'action',
    original: 'Rename',
    value: 'Átnevezés'
  },
  'layers-active-layer-visible': {
    original: 'Active layer is visible',
    value: 'Az aktív réteg látható'
  },
  'layers-active-layer-hidden': {
    original: 'Active layer is hidden',
    value: 'Az aktív réteg el van rejtve'
  },
  'layers-visibility-toggle': {
    original: 'Layer Visibility',
    value: 'Réteg láthatósága'
  },
  'layers-blend-normal': {
    original: 'normal',
    value: 'Normál'
  },
  'layers-blend-darken': {
    original: 'darken',
    value: 'sötétedni'
  },
  'layers-blend-multiply': {
    original: 'multiply',
    value: 'szaporodnak'
  },
  'layers-blend-color-burn': {
    original: 'color burn',
    value: 'színes égés'
  },
  'layers-blend-lighten': {
    original: 'lighten',
    value: 'könnyítsen'
  },
  'layers-blend-screen': {
    original: 'screen',
    value: 'képernyő'
  },
  'layers-blend-color-dodge': {
    original: 'color dodge',
    value: 'színes kitérő'
  },
  'layers-blend-overlay': {
    original: 'overlay',
    value: 'Átfedés'
  },
  'layers-blend-soft-light': {
    original: 'soft light',
    value: 'lágy fény'
  },
  'layers-blend-hard-light': {
    original: 'hard light',
    value: 'Kemény fény'
  },
  'layers-blend-difference': {
    original: 'difference',
    value: 'különbség'
  },
  'layers-blend-exclusion': {
    original: 'exclusion',
    value: 'kirekesztés'
  },
  'layers-blend-hue': {
    original: 'hue',
    value: 'színárnyalat'
  },
  'layers-blend-saturation': {
    original: 'saturation',
    value: 'telítettség'
  },
  'layers-blend-color': {
    original: 'color',
    value: 'szín'
  },
  'layers-blend-luminosity': {
    original: 'luminosity',
    value: 'fényesség'
  },
  'layers-rename-title': {
    hint: '-title in code usually means it shows up in a tooltip, or it’s the header of a dialog box, so it can be a little bit longer.',
    original: 'Rename Layer',
    value: 'Réteg átnevezése'
  },
  'layers-rename-name': {
    hint: 'noun',
    original: 'Name',
    value: 'Név'
  },
  'layers-rename-clear': {
    hint: 'aka action of deleting entered name',
    original: 'Clear Name',
    value: 'Név törlése'
  },
  'layers-rename-sketch': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Sketch',
    value: 'Vázlat'
  },
  'layers-rename-colors': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Colors',
    value: 'Színek'
  },
  'layers-rename-shading': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Shading',
    value: 'Árnyékolás'
  },
  'layers-rename-lines': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Lines',
    value: 'Vonalak'
  },
  'layers-rename-effects': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Effects',
    value: 'Hatások'
  },
  'layers-rename-foreground': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Foreground',
    value: 'Előtér'
  },
  'layers-merge-modal-title': {
    original: 'Merge/Mix Layers',
    value: 'Rétegek egyesítése/keverése'
  },
  'layers-merge-description': {
    hint: 'After ":" a list of mix/blend modes is shown.',
    original: 'Merges the selected layer with the one underneath. Select the mix mode:',
    value: 'Egyesíti a kiválasztott réteget az alatta lévővel. Válassza ki a keverési módot:'
  },
  'file-no-autosave': {
    hint: "let user know they can't autosave, and there's no cloud storage. Keep short so fits on one line",
    original: 'No autosave, no cloud storage',
    value: 'Nincs automatikus mentés, nincs felhőtárhely'
  },
  'file-new': {
    hint: 'action',
    original: 'New',
    value: 'Új'
  },
  'file-import': {
    hint: 'action',
    original: 'Import',
    value: 'Importálás'
  },
  'file-save': {
    hint: 'action',
    original: 'Save',
    value: 'Megment'
  },
  'file-format': {
    original: 'File Format',
    value: 'Fájlformátum'
  },
  'file-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Másolat'
  },
  'file-copy-title': {
    original: 'Copy To Clipboard',
    value: 'Másolja a vágólapra'
  },
  'file-share': {
    hint: 'Common feature on phones. Pressing share button allows to send data to a different app, e.g. as a text message to someone.',
    original: 'Share',
    value: 'Ossza meg'
  },
  'file-storage': {
    hint: 'Made up term. Allows users to store drawing in the browser. (not synced across devices)',
    original: 'Browser Storage',
    value: 'Böngésző tároló'
  },
  'file-storage-thumb-title': {
    hint: 'when reopening or reloading',
    original: 'Restores when reopening page',
    value: 'Az oldal újranyitásakor visszaáll'
  },
  'file-storage-about': {
    hint: 'link to Browser Storage help/user manual',
    original: 'About Browser Storage',
    value: 'A böngésző tárolásáról'
  },
  'file-storage-cant-access': {
    hint: 'an error. Can’t access the storage.',
    original: "Can't access",
    value: 'Nem lehet hozzáférni'
  },
  'file-storage-empty': {
    hint: 'nothing stored',
    original: 'Empty',
    value: 'Üres'
  },
  'file-storage-store': {
    hint: 'verb',
    original: 'Store',
    value: 'Bolt'
  },
  'file-storage-clear': {
    original: 'Clear',
    value: 'Egyértelmű'
  },
  'file-storage-storing': {
    hint: 'while it is in the process of storing',
    original: 'Storing',
    value: 'Tárolás'
  },
  'file-storage-overwrite': {
    hint: 'overwrite what is already stored',
    original: 'Overwrite',
    value: 'Átír'
  },
  'file-storage-min-ago': {
    hint: "{x} <- needs to stay, it's a placeholder.",
    original: '{x}min ago',
    value: '{x} perce'
  },
  'file-storage-hours-ago': {
    original: '{x}h ago',
    value: '{x} órája'
  },
  'file-storage-days-ago': {
    original: '{x}d ago',
    value: '{x} napja'
  },
  'file-storage-month-ago': {
    original: '> 1month ago',
    value: '> 1 hónapja'
  },
  'file-storage-restored': {
    original: 'Restored from Browser Storage',
    value: 'Visszaállítva a böngésző tárhelyéről'
  },
  'file-storage-stored': {
    original: 'Stored to Browser Storage',
    value: 'Tárolva a böngésző tárhelyén'
  },
  'file-storage-failed': {
    original: 'Failed to store to Browser Storage',
    value: 'Nem sikerült tárolni a böngésző tárhelyén'
  },
  'file-storage-failed-1': {
    original: 'Failed to store. Possible causes:',
    value: 'Nem sikerült tárolni. Lehetséges okok:'
  },
  'file-storage-failed-2': {
    original: 'Out of disk space',
    value: 'Elfogyott a lemezterület'
  },
  'file-storage-failed-3': {
    original: 'Storage disabled in incognito tab',
    value: 'A tárhely le van tiltva az inkognitó lapon'
  },
  'file-storage-failed-4': {
    hint: 'as in: unsupported feature',
    original: "Browser doesn't support storage",
    value: 'A böngésző nem támogatja a tárolást'
  },
  'file-storage-failed-clear': {
    original: 'Failed to clear.',
    value: 'Nem sikerült törölni.'
  },
  'file-upload': {
    hint: 'verb',
    original: 'Upload',
    value: 'Feltöltés'
  },
  'cleared-layer': {
    hint: 'the layer has been cleared. It is now empty',
    original: 'Cleared layer',
    value: 'Letisztult réteg'
  },
  'cleared-selected-area': {
    original: 'Cleared selected area',
    value: 'Letisztított kijelölt terület'
  },
  filled: {
    hint: 'layer got filled with a color',
    original: 'Filled',
    value: 'Kitöltött'
  },
  'filled-selected-area': {
    original: 'Filled selection',
    value: 'Kitöltött kijelölés'
  },
  'new-title': {
    original: 'New Image',
    value: 'Új kép'
  },
  'new-current': {
    hint: 'as in "reuse the current image resolution"',
    original: 'Current',
    value: 'Jelenlegi'
  },
  'new-fit': {
    hint: 'make dimensions of image fit your window size',
    original: 'Fit',
    value: 'Elfér'
  },
  'new-oversize': {
    hint: 'like oversized clothes.',
    original: 'Oversize',
    value: 'Túlméretes'
  },
  'new-square': {
    hint: 'square image format',
    original: 'Square',
    value: 'Négyzet'
  },
  'new-landscape': {
    hint: 'https://en.wikipedia.org/wiki/Page_orientation',
    original: 'Landscape',
    value: 'Tájkép'
  },
  'new-portrait': {
    original: 'Portrait',
    value: 'Portré'
  },
  'new-screen': {
    hint: 'resolution of computer screen',
    original: 'Screen',
    value: 'Képernyő'
  },
  'new-video': {
    hint: 'dimensions of a video (usually 16:9)',
    original: 'Video',
    value: 'Videó'
  },
  'new-din-paper': {
    hint: 'https://en.wikipedia.org/wiki/Paper_size',
    original: 'DIN Paper',
    value: 'DIN papír'
  },
  'new-px': {
    hint: 'abbreviation of pixel',
    original: 'px',
    value: 'px'
  },
  'new-ratio': {
    hint: 'aspect ratio',
    original: 'Ratio',
    value: 'Hányados'
  },
  'upload-title': {
    hint: '"Imgur" is a website name.',
    original: 'Upload to Imgur',
    value: 'Feltöltés az Imgurba'
  },
  'upload-link-notice': {
    hint: 'Explaining to user even though their upload is unlisted, they should be careful who they share the link with. No password protection.',
    original: 'Anyone with the link to your uploaded image will be able to view it.',
    value: 'Bárki, aki rendelkezik a feltöltött kép linkjével, megtekintheti azt.'
  },
  'upload-name': {
    hint: 'noun',
    original: 'Title',
    value: 'Cím'
  },
  'upload-title-untitled': {
    original: 'Untitled',
    value: 'Névtelen'
  },
  'upload-caption': {
    hint: 'as in image caption / description',
    original: 'Caption',
    value: 'Felirat'
  },
  'upload-submit': {
    hint: 'verb',
    original: 'Upload',
    value: 'Feltöltés'
  },
  'upload-uploading': {
    hint: 'in the process of uploading',
    original: 'Uploading...',
    value: 'Feltöltés...'
  },
  'upload-success': {
    original: 'Upload Successful',
    value: 'Feltöltés sikeres'
  },
  'upload-failed': {
    original: 'Upload failed.',
    value: 'Feltöltés sikertelen.'
  },
  'upload-delete': {
    original: 'To delete your image from Imgur visit:',
    value: 'A kép Imgurból való törléséhez látogassa meg:'
  },
  'cropcopy-title-copy': {
    original: 'Copy To Clipboard',
    value: 'Másolja a vágólapra'
  },
  'cropcopy-title-crop': {
    hint: 'Cropping is a common feature in image editing.',
    original: 'Crop',
    value: 'Vág'
  },
  'cropcopy-click-hold': {
    original: 'Right-click or press hold to copy.',
    value: 'Kattintson a jobb gombbal, vagy nyomja meg a tartást a másoláshoz.'
  },
  'cropcopy-btn-copy': {
    hint: 'confirmation button. Should be short.',
    original: 'To Clipboard',
    value: 'A vágólapra'
  },
  'cropcopy-copied': {
    original: 'Copied.',
    value: 'Másolva.'
  },
  'cropcopy-btn-crop': {
    hint: 'confirmation button. Should be short.',
    original: 'Apply Crop',
    value: 'Alkalmazza a Vágást'
  },
  'crop-drag-to-crop': {
    hint: 'drag the mouse to adjust cropping',
    original: 'Drag to crop',
    value: 'Húzza a körbevágáshoz'
  },
  'filter-crop-extend': {
    original: 'Crop/Extend',
    value: 'Vágás/hosszabbítás'
  },
  'filter-flip': {
    original: 'Flip',
    value: 'Flip'
  },
  'filter-perspective': {
    hint: 'perspective transformation',
    original: 'Perspective',
    value: 'Perspektíva'
  },
  'filter-resize': {
    original: 'Resize',
    value: 'Átméretezés'
  },
  'filter-rotate': {
    original: 'Rotate',
    value: 'Forog'
  },
  'filter-transform': {
    hint: 'more generic transformation. Commonly known as "free transform"',
    original: 'Transform',
    value: 'Átalakítás'
  },
  'filter-bright-contrast': {
    hint: 'brightness contrast',
    original: 'Bright/Contrast',
    value: 'Világos/kontraszt'
  },
  'filter-curves': {
    hint: 'color curves',
    original: 'Curves',
    value: 'Görbék'
  },
  'filter-hue-sat': {
    original: 'Hue/Saturation',
    value: 'Színárnyalat/telítettség'
  },
  'filter-invert': {
    hint: 'invert the colors',
    original: 'Invert',
    value: 'Invert'
  },
  'filter-tilt-shift': {
    hint: 'or just depth-of-field. https://en.wikipedia.org/wiki/Tilt%E2%80%93shift_photography',
    original: 'Tilt Shift',
    value: 'Tilt Shift'
  },
  'filter-to-alpha': {
    hint: 'feature that can isolate line drawings if it is a black and white image.',
    original: 'To Alpha',
    value: 'Alfának'
  },
  'filter-triangle-blur': {
    hint: 'or just blur',
    original: 'Triangle Blur',
    value: 'Háromszög elmosódás'
  },
  'filter-unsharp-mask': {
    hint: 'or just sharpen, https://en.wikipedia.org/wiki/Unsharp_masking',
    original: 'Unsharp Mask',
    value: 'Életlen maszk'
  },
  'filter-crop-title': {
    hint: 'same as the button, but can be longer',
    original: 'Crop / Extend',
    value: 'Vágás / meghosszabbítás'
  },
  'filter-crop-description': {
    hint: 'descriptions explain the "filter" a tiny bit. Most importantly it says if it’s applied to a single layer, or the entire image (all layer)',
    original: 'Crop or extend the image.',
    value: 'Vágja ki vagy bővítse ki a képet.'
  },
  'filter-crop-left': {
    original: 'Left',
    value: 'Bal'
  },
  'filter-crop-right': {
    original: 'Right',
    value: 'Jobb'
  },
  'filter-crop-top': {
    original: 'Top',
    value: 'Felső'
  },
  'filter-crop-bottom': {
    original: 'Bottom',
    value: 'Alsó'
  },
  'filter-crop-rule-thirds': {
    hint: 'https://en.wikipedia.org/wiki/Rule_of_thirds',
    original: 'Rule of Thirds',
    value: 'Harmadik szabálya'
  },
  'filter-crop-fill': {
    hint: 'If image gets extended, what color to fill that new area with',
    original: 'Fill',
    value: 'Tölt'
  },
  'filter-flip-title': {
    hint: 'same as the button, but can be longer',
    original: 'Flip',
    value: 'Flip'
  },
  'filter-flip-description': {
    original: 'Flips layer or whole image.',
    value: 'Megfordítja a réteget vagy a teljes képet.'
  },
  'filter-flip-horizontal': {
    original: 'Horizontal',
    value: 'Vízszintes'
  },
  'filter-flip-vertical': {
    original: 'Vertical',
    value: 'Függőleges'
  },
  'filter-flip-image': {
    original: 'Flip Image',
    value: 'Kép megfordítása'
  },
  'filter-flip-layer': {
    original: 'Flip Layer',
    value: ''
  },
  'filter-perspective-title': {
    hint: 'same as the button, but can be longer',
    original: 'Perspective',
    value: 'Perspektíva'
  },
  'filter-perspective-description': {
    original: 'Transforms the selected layer.',
    value: 'Átalakítja a kiválasztott réteget.'
  },
  'filter-resize-title': {
    hint: 'same as the button, but can be longer',
    original: 'Resize',
    value: 'Átméretezés'
  },
  'filter-resize-description': {
    original: 'Resizes the image.',
    value: 'Átméretezi a képet.'
  },
  'filter-rotate-title': {
    hint: 'same as the button, but can be longer',
    original: 'Rotate',
    value: 'Forog'
  },
  'filter-rotate-description': {
    original: 'Rotates the image.',
    value: 'Elforgatja a képet.'
  },
  'filter-transform-empty': {
    hint: 'nothing on the layer, just transparency',
    original: 'Layer is empty.',
    value: 'A réteg üres.'
  },
  'filter-transform-title': {
    hint: 'same as the button, but can be longer',
    original: 'Transform',
    value: 'Átalakítani'
  },
  'filter-transform-description': {
    hint: 'as in the Shift key.',
    original: 'Transforms selected layer. Hold Shift for additional behavior.',
    value: 'Átalakítja a kiválasztott réteget. Tartsa lenyomva a Shift billentyűt a további viselkedéshez.'
  },
  'filter-transform-rotation': {
    hint: 'or Angle',
    original: 'Rotation',
    value: 'Forgás'
  },
  'filter-transform-flip': {
    original: 'Flip',
    value: 'Flip'
  },
  'filter-transform-center': {
    hint: 'verb or noun',
    original: 'Center',
    value: 'Központ'
  },
  'filter-transform-constrain': {
    hint: 'short version of "constrain proportions" – feature that locks the aspect ratio',
    original: 'Constrain',
    value: 'Korlátozni'
  },
  'filter-transform-snap': {
    hint: 'or snapping. Common feature in graphics software. Makes stuff snap into place so it’s easier to align elements in an image',
    original: 'Snap',
    value: ''
  },
  'filter-transform-snap-title': {
    hint: 'same as above, but as a tooltip',
    original: 'Snap Rotation And Position',
    value: 'Snap forgatás és pozíció'
  },
  'filter-bright-contrast-title': {
    hint: 'same as the button, but can be longer',
    original: 'Brightness / Contrast',
    value: 'Fényerő / kontraszt'
  },
  'filter-bright-contrast-description': {
    original: 'Change brightness and contrast for the selected layer.',
    value: 'Módosítja a kiválasztott réteg fényerejét és kontrasztját.'
  },
  'filter-bright-contrast-brightness': {
    original: 'Brightness',
    value: 'Fényerősség'
  },
  'filter-bright-contrast-contrast': {
    original: 'Contrast',
    value: 'Kontraszt'
  },
  'filter-curves-title': {
    hint: 'same as the button, but can be longer',
    original: 'Curves',
    value: 'Görbék'
  },
  'filter-curves-description': {
    original: 'Apply curves on the selected layer.',
    value: 'Görbék alkalmazása a kiválasztott rétegen.'
  },
  'filter-curves-all': {
    original: 'All',
    value: 'Minden'
  },
  'filter-hue-sat-title': {
    hint: 'same as the button, but can be longer',
    original: 'Hue / Saturation',
    value: 'Színárnyalat telítettségét'
  },
  'filter-hue-sat-description': {
    original: 'Change hue and saturation for the selected layer.',
    value: 'Módosítsa a kiválasztott réteg színárnyalatát és telítettségét.'
  },
  'filter-hue-sat-hue': {
    original: 'Hue',
    value: 'Színárnyalat'
  },
  'filter-hue-sat-saturation': {
    original: 'Saturation',
    value: 'Telítettség'
  },
  'filter-applied': {
    hint: 'used like this: „Invert“ applied. Informs that a filter was applied',
    original: 'applied',
    value: 'alkalmazott'
  },
  'filter-tilt-shift-title': {
    hint: 'same as the button, but can be longer',
    original: 'Tilt Shift',
    value: 'Tilt Shift'
  },
  'filter-tilt-shift-description': {
    original: 'Applies tilt shift on the selected layer.',
    value: 'Döntéseltolást alkalmaz a kiválasztott rétegen.'
  },
  'filter-tilt-shift-blur': {
    hint: 'words can be individually translated',
    original: 'Blur Radius',
    value: 'Elmosódási sugár'
  },
  'filter-tilt-shift-gradient': {
    hint: 'words can be individually translated',
    original: 'Gradient Radius',
    value: 'Gradiens sugár'
  },
  'filter-to-alpha-title': {
    hint: 'same as the button, but can be longer',
    original: 'To Alpha',
    value: 'Alfának'
  },
  'filter-to-alpha-description': {
    hint: 'after ":" follow two options',
    original: 'Generates alpha channel for selected layer from:',
    value: 'Alfa csatornát hoz létre a kiválasztott réteghez a következőkből:'
  },
  'filter-to-alpha-inverted-lum': {
    hint: 'luminance is same as the layer blend mode "luminosity"',
    original: 'Inverted Luminance',
    value: 'Fordított fényerő'
  },
  'filter-to-alpha-lum': {
    original: 'Luminance',
    value: 'Fényerő'
  },
  'filter-to-alpha-replace': {
    hint: 'What color to replace the rgb channels with',
    original: 'Replace RGB',
    value: 'Cserélje ki az RGB-t'
  },
  'filter-triangle-blur-title': {
    hint: 'same as the button, but can be longer',
    original: 'Triangle Blur',
    value: 'Háromszög elmosódás'
  },
  'filter-triangle-blur-description': {
    original: 'Applies triangle blur on the selected layer.',
    value: 'Háromszög elmosódást alkalmaz a kiválasztott rétegen.'
  },
  'filter-unsharp-mask-title': {
    hint: 'same as the button, but can be longer',
    original: 'Unsharp Mask',
    value: 'Életlen maszk'
  },
  'filter-unsharp-mask-description': {
    original: 'Sharpens the selected layer by scaling pixels away from the average of their neighbors.',
    value: 'Élesíti a kiválasztott réteget úgy, hogy pixelekkel távolítja el a szomszédok átlagától.'
  },
  'filter-unsharp-mask-strength': {
    original: 'Strength',
    value: 'Erő'
  },
  'filter-grid': {
    original: 'Grid',
    value: 'Rács'
  },
  'filter-grid-description': {
    original: 'Draws grid on selected layer.',
    value: 'Rácsot rajzol a kiválasztott rétegre.'
  },
  'filter-noise': {
    original: 'Noise',
    value: 'Zaj'
  },
  'filter-noise-description': {
    original: 'Adds noise to selected layer.',
    value: 'Zajt ad a kiválasztott réteghez.'
  },
  'filter-noise-scale': {
    original: 'Scale',
    value: 'Skála'
  },
  'filter-noise-alpha': {
    original: 'Alpha',
    value: 'Alpha'
  },
  'filter-pattern': {
    original: 'Pattern',
    value: 'Minta'
  },
  'filter-pattern-description': {
    original: 'Generates pattern on selected layer. Drag the preview for further controls.',
    value: 'Mintázatot generál a kiválasztott rétegen. A további vezérlők megtekintéséhez húzza el az előnézetet.'
  },
  'filter-distort': {
    original: 'Distort',
    value: 'Torzít'
  },
  'filter-distort-description': {
    original: 'Distorts the selected layer.',
    value: 'Torzítja a kiválasztott réteget.'
  },
  'filter-distort-phase': {
    original: 'Phase',
    value: 'Fázis'
  },
  'filter-distort-stepsize': {
    original: 'Step Size',
    value: 'Lépés mérete'
  },
  'filter-distort-sync-xy': {
    original: 'Sync XY',
    value: 'XY szinkronizálása'
  },
  'filter-vanish-point': {
    original: 'Vanish Point',
    value: 'Eltűnési pont'
  },
  'filter-vanish-point-title': {
    original: 'Vanishing Point',
    value: 'Távlatpont'
  },
  'filter-vanish-point-description': {
    original: 'Adds vanishing point to selected layer. Drag preview to move.',
    value: 'Eltűnési pontot ad a kiválasztott réteghez. A mozgatáshoz húzza az előnézetet.'
  },
  'filter-vanish-point-lines': {
    hint: 'As in: the number of lines',
    original: 'Lines',
    value: 'Vonalak'
  },
  'import-opening': {
    hint: 'in the process of opening a large file',
    original: 'Opening file...',
    value: 'Fájl megnyitása...'
  },
  'import-title': {
    original: 'Import Image',
    value: 'Kép importálása'
  },
  'import-too-large': {
    original: 'Image too large, will be downscaled.',
    value: 'A kép túl nagy, kicsinyítve lesz.'
  },
  'import-btn-as-layer': {
    hint: 'import as layer. Button label, should be short',
    original: 'As Layer',
    value: 'Rétegként'
  },
  'import-btn-as-image': {
    hint: 'import as image. Button label, should be short',
    original: 'As Image',
    value: 'Képként'
  },
  'import-as-layer-title': {
    original: 'Import Image as New Layer',
    value: 'Kép importálása új rétegként'
  },
  'import-as-layer-description': {
    original: 'Adjust the position of the imported image.',
    value: 'Állítsa be az importált kép helyzetét.'
  },
  'import-as-layer-limit-reached': {
    hint: 'Kleki allows only a certain number of layers',
    original: 'Layer limit reached. Image will be placed on existing layer.',
    value: 'Elérte a rétegkorlátot. A kép a meglévő rétegre kerül.'
  },
  'import-as-layer-fit': {
    hint: 'verb. imported image made to fit inside canvas.',
    original: 'Fit',
    value: 'Elfér'
  },
  'import-flatten': {
    hint: 'Turn image that has multiple layers into an image that has only one layer.',
    original: 'Flatten image',
    value: 'Lapos kép'
  },
  'import-unsupported-file': {
    hint: 'e.g. user tried to open a WAV',
    original: 'Unsupported file type. See Help for supported types.',
    value: 'Nem támogatott fájl típus. A támogatott típusokért lásd a Súgót.'
  },
  'import-broken-file': {
    hint: 'if an image file is broken',
    original: "Couldn't load image. File might be corrupted.",
    value: 'Nem sikerült betölteni a képet. Lehet, hogy a fájl sérült.'
  },
  'import-psd-unsupported': {
    hint: 'PSD is photoshop file format. flatten as in „flatten image“ in photoshop. Merged all layers',
    original: 'Unsupported features. PSD had to be flattened.',
    value: 'Nem támogatott funkciók. A PSD-t lapítani kellett.'
  },
  'import-psd-limited-support': {
    hint: 'PSD has lots of fancy features that Kleki doesn\'t have, so when importing a PSD it often has to simplify things. So it might look different compared to when opening in Photoshop. We’re informing the user if they "flatten" the image it will be accurate despite losing layers. The user has to weigh, do they care about having layers, or do they care about it looking right.',
    original: 'PSD support is limited. Flattened will more likely look correct.',
    value: 'A PSD támogatás korlátozott. A lelapított nagy valószínűséggel helyesen fog kinézni.'
  },
  'import-psd-too-large': {
    original: 'Image exceeds maximum dimensions of {x} x {x} pixels. Unable to import.',
    value: 'A kép meghaladja a maximális {x} x {x} képpont méretet. Nem lehet importálni.'
  },
  'import-psd-size': {
    original: 'Image size',
    value: 'Képméret'
  },
  'hand-reset': {
    hint: 'resets zoom (to 100%) and rotation (to 0°)',
    original: 'Reset',
    value: 'Visszaállítás'
  },
  'hand-fit': {
    hint: 'makes canvas cover whole working area',
    original: 'Fit',
    value: 'Elfér'
  },
  'hand-inertia-scrolling': {
    hint: 'Canvas will continue to move even after releasing pointer.',
    original: 'Inertia Scrolling',
    value: ''
  },
  'bucket-tolerance': {
    hint: 'a common feature for paint bucket',
    original: 'Tolerance',
    value: 'Megértés'
  },
  'bucket-sample': {
    hint: 'like "taking a sample"',
    original: 'Sample',
    value: 'Minta'
  },
  'bucket-sample-title': {
    original: 'Which layers to sample color from',
    value: 'Melyik rétegből kell színmintát venni'
  },
  'bucket-sample-all': {
    hint: 'all layers',
    original: 'All',
    value: 'Minden'
  },
  'bucket-sample-active': {
    hint: 'currently active layer',
    original: 'Active',
    value: 'Aktív'
  },
  'bucket-sample-above': {
    hint: 'layer above active layer',
    original: 'Above',
    value: 'Felett'
  },
  'bucket-grow': {
    hint: 'verb. Or expand.',
    original: 'Grow',
    value: 'Nö'
  },
  'bucket-grow-title': {
    original: 'Grow filled area (in pixels)',
    value: 'A kitöltött terület növelése (pixelben)'
  },
  'bucket-contiguous': {
    hint: 'common setting for paint bucket in image editing software',
    original: 'Contiguous',
    value: 'Szomszédos'
  },
  'bucket-contiguous-title': {
    hint: 'tooltip explaining Contiguous',
    original: 'Only fill connected areas',
    value: 'Csak az összekapcsolt területeket töltse fel'
  },
  'gradient-linear': {
    original: 'Linear',
    value: 'Lineáris'
  },
  'gradient-linear-mirror': {
    original: 'Linear-Mirror',
    value: 'Lineáris tükör'
  },
  'gradient-radial': {
    original: 'Radial',
    value: 'Sugárirányú'
  },
  'shape-stroke': {
    original: 'Stroke',
    value: 'Stroke'
  },
  'shape-fill': {
    original: 'Fill',
    value: 'Tölt'
  },
  'shape-rect': {
    hint: 'rectangle shape',
    original: 'Rectangle',
    value: 'Téglalap'
  },
  'shape-ellipse': {
    hint: 'ellipse shape',
    original: 'Ellipse',
    value: 'Ellipszis'
  },
  'shape-line': {
    original: 'Line',
    value: 'Vonal'
  },
  'shape-line-width': {
    original: 'Line Width',
    value: 'Vonalvastagság'
  },
  'shape-outwards': {
    hint: 'grows shape from the center outwards',
    original: 'Outwards',
    value: 'Kifelé'
  },
  'shape-fixed': {
    hint: 'fixed ratio of 1:1',
    original: 'Fixed 1:1',
    value: 'Javítva 1:1'
  },
  'text-instruction': {
    original: 'Click canvas to place text',
    value: 'Kattintson a vászonra a szöveg elhelyezéséhez'
  },
  'text-title': {
    original: 'Add Text',
    value: 'Szöveg hozzáadása'
  },
  'text-text': {
    original: 'Text',
    value: 'Szöveg'
  },
  'text-font': {
    original: 'Font',
    value: 'Betűtípus'
  },
  'text-placeholder': {
    hint: 'placeholder in text input.',
    original: 'Your text',
    value: 'A te szöveged'
  },
  'text-color': {
    original: 'Color',
    value: 'Szín'
  },
  'text-size': {
    original: 'Size',
    value: 'Méret'
  },
  'text-line-height': {
    original: 'Line Height',
    value: 'Vonalmagasság'
  },
  'text-letter-spacing': {
    original: 'Letter Spacing',
    value: 'Betűtávolságok'
  },
  'text-left': {
    hint: 'left, center, right text formatting like in Word',
    original: 'Left',
    value: 'Bal'
  },
  'text-center': {
    original: 'Center',
    value: 'Központ'
  },
  'text-right': {
    original: 'Right',
    value: 'Jobb'
  },
  'text-italic': {
    original: 'Italic',
    value: 'Dőlt'
  },
  'text-bold': {
    original: 'Bold',
    value: 'Bátor'
  },
  'select-select': {
    original: 'Select',
    value: ''
  },
  'select-transform': {
    original: 'Transform',
    value: ''
  },
  'select-lasso': {
    original: 'Lasso',
    value: ''
  },
  'select-polygon': {
    original: 'Polygon',
    value: ''
  },
  'select-boolean-replace': {
    original: 'Replace',
    value: ''
  },
  'select-boolean-add': {
    original: 'Add',
    value: ''
  },
  'select-boolean-subtract': {
    original: 'Subtract',
    value: ''
  },
  'select-all': {
    original: 'All',
    value: ''
  },
  'select-invert': {
    original: 'Invert',
    value: ''
  },
  'select-reset': {
    original: 'Reset',
    value: ''
  },
  'select-fill': {
    original: 'Fill',
    value: ''
  },
  'select-erase': {
    original: 'Erase',
    value: ''
  },
  'select-transform-clone': {
    original: 'Clone',
    value: ''
  },
  'select-transform-clone-applied': {
    original: 'Cloned',
    value: ''
  },
  'select-transform-move-to-layer': {
    original: 'Move to layer:',
    value: ''
  },
  'select-transform-applied': {
    original: 'Transformation applied',
    value: ''
  },
  'select-transform-empty': {
    original: 'Selected area on active layer is empty.',
    value: ''
  },
  'save-reminder-title': {
    hint: 'Save Reminder is a popup that shows up after 20 minutes of not saving. Allows user to save on the spot.',
    original: 'Unsaved Work',
    value: 'Nem mentett munka'
  },
  'save-reminder-text': {
    hint: 'turns into: "...saved in <strong>12 minutes</strong>. Save..."',
    original: 'Image was not saved in {a} minutes{b}. Save now to prevent eventual loss.',
    value: 'A kép mentése nem sikerült {a} percen belül{b}. Mentse el most, hogy elkerülje az esetleges veszteséget.'
  },
  'save-reminder-save-psd': {
    original: 'Save As PSD',
    value: 'Mentés PSD-ként'
  },
  'save-reminder-psd-layers': {
    original: 'PSD will remember all layers.',
    value: 'A PSD minden rétegre emlékezni fog.'
  },
  'backup-drawing': {
    hint: '(Embed) If the upload failed, the user can backup/download their drawing.',
    original: 'You can backup your drawing.',
    value: 'Biztonsági másolatot készíthet a rajzról.'
  },
  submit: {
    original: 'Submit',
    value: 'Beküldés'
  },
  'submit-title': {
    hint: 'tooltip when hovering "Submit" button',
    original: 'Submit Drawing',
    value: 'Rajz beküldése'
  },
  'submit-prompt': {
    hint: 'Dialog asking for confirmation',
    original: 'Submit drawing?',
    value: 'Elküldi a rajzot?'
  },
  'submit-submitting': {
    hint: 'in the process of uploading',
    original: 'Submitting',
    value: 'Beküldés'
  },
  'embed-init-loading': {
    hint: 'when opening the app',
    original: 'Loading app',
    value: 'Alkalmazás betöltése'
  },
  'embed-init-waiting': {
    hint: 'The app is loaded, but it’s still waiting for image',
    original: 'Waiting for image',
    value: 'Képre várva'
  },
  unsaved: {
    original: 'Unsaved',
    value: 'Nincs mentve'
  },
  help: {
    hint: 'or user manual',
    original: 'Help',
    value: 'Segítség'
  },
  'tab-settings': {
    original: 'Settings',
    value: 'Beállítások'
  },
  'settings-language': {
    original: 'Language',
    value: 'Nyelv'
  },
  'settings-language-reload': {
    hint: 'changes will be visible after reloading the page',
    original: 'Will update after reloading.',
    value: 'Újratöltés után frissül.'
  },
  'settings-theme': {
    original: 'Theme',
    value: 'Téma'
  },
  'settings-save-reminder-label': {
    original: 'Save Reminder',
    value: 'Emlékeztető mentés'
  },
  'settings-save-reminder-disabled': {
    original: 'disabled',
    value: 'Tiltva'
  },
  'settings-save-reminder-confirm-title': {
    original: 'Turn off Save Reminder?',
    value: 'Kikapcsolja az Emlékeztető mentést?'
  },
  'settings-save-reminder-confirm-a': {
    original: "There is no autosave and browser tabs don't last forever. If you don't periodically save you will likely lose work.",
    value: 'Nincs automatikus mentés, és a böngésző lapjai sem tartanak örökké. Ha nem menti rendszeresen, akkor valószínűleg elveszíti a munkáját.'
  },
  'settings-save-reminder-confirm-b': {
    original: 'Disable at your own risk?',
    value: 'Saját felelősségére letiltani?'
  },
  'settings-save-reminder-confirm-disable': {
    original: 'Disable',
    value: 'Letiltás'
  },
  'theme-dark': {
    original: 'Dark',
    value: 'Sötét'
  },
  'theme-light': {
    original: 'Light',
    value: 'Fény'
  },
  'terms-of-service': {
    original: 'Terms of Service',
    value: 'Szolgáltatás feltételei'
  },
  licenses: {
    hint: 'as in software licenses',
    original: 'Licenses',
    value: 'Licencek'
  },
  'source-code': {
    original: 'Source Code',
    value: 'Forráskód'
  },
  auto: {
    hint: 'automatically decided',
    original: 'auto',
    value: 'auto'
  },
  'zoom-in': {
    original: 'Zoom In',
    value: 'Nagyítás'
  },
  'zoom-out': {
    original: 'Zoom Out',
    value: 'Kicsinyítés'
  },
  radius: {
    original: 'Radius',
    value: 'Sugár'
  },
  'constrain-proportions': {
    original: 'Constrain Proportions',
    value: 'Rögzített méretearánya'
  },
  width: {
    original: 'Width',
    value: 'Szélesség'
  },
  height: {
    original: 'Height',
    value: 'Magasság'
  },
  opacity: {
    hint: 'for layer opacity, brush opacity, text opacity, ...',
    original: 'Opacity',
    value: 'Átlátszatlanság'
  },
  red: {
    original: 'Red',
    value: 'Piros'
  },
  green: {
    original: 'Green',
    value: 'Zöld'
  },
  blue: {
    original: 'Blue',
    value: 'Kék'
  },
  eraser: {
    hint: 'noun. As short as possible',
    original: 'Eraser',
    value: 'Radír'
  },
  center: {
    hint: 'verb',
    original: 'Center',
    value: 'Középre'
  },
  layers: {
    hint: 'layers in the context of image editing software',
    original: 'Layers',
    value: 'Rétegek'
  },
  background: {
    hint: 'as in background layer',
    original: 'Background',
    value: 'Háttér'
  },
  'scaling-algorithm': {
    hint: 'Label for dropdown where user chooses which computer graphics algorithm should be used to scale the image',
    original: 'Scaling Algorithm',
    value: 'Méretezési algoritmus'
  },
  'algorithm-smooth': {
    original: 'Smooth',
    value: 'Sima'
  },
  'algorithm-pixelated': {
    original: 'Pixelated',
    value: 'Pixeles'
  },
  preview: {
    hint: 'subject. When applying a filter user sees a preview of what the result would look like.',
    original: 'Preview',
    value: 'Előnézet'
  },
  'angle-snap': {
    hint: 'Snapping. Like when transforming an image and it snaps to the side of the frame.',
    original: 'Snap',
    value: 'Snap'
  },
  'angle-snap-title': {
    original: '45° Angle Snapping',
    value: '45°-os szögkattintás'
  },
  'lock-alpha': {
    hint: 'Short. Freezes the alpha channel of image. Images consist of 4 channels (red, green, blue, alpha). Alpha lock also found in Procreate and photoshop',
    original: 'Lock Alpha',
    value: 'Alfa zárása'
  },
  'lock-alpha-title': {
    hint: "same as 'lock-alpha' but longer version. Shows up as a tooltip when hovering",
    original: "Locks layer's alpha channel",
    value: 'Zárolja a réteg alfa csatornáját'
  },
  reverse: {
    hint: 'Reversing the direction of a color gradient',
    original: 'Reverse',
    value: 'Fordított'
  },
  'compare-before': {
    original: 'Before',
    value: 'Előtt'
  },
  'compare-after': {
    original: 'After',
    value: 'Után'
  },
  loading: {
    original: 'Loading',
    value: 'Betöltés'
  },
  more: {
    original: 'More',
    value: 'Több'
  },
  'x-minutes': {
    original: '{x}min',
    value: '{x}perc'
  },
  wip: {
    original: 'Work in progress',
    value: ''
  },
  'browser-zoom-help': {
    original: 'Double-tap or pinch-out to reset browser zoom.',
    value: ''
  },
  dismiss: {
    original: 'Dismiss',
    value: ''
  }
}
