{
  'switch-ui-left-right': {
    hint: 'Button that switches layout from left to right, to accommodate left handed & right handed visitors.',
    original: 'Switch left/right UI',
    value: 'Ngalih UI kiwa/tengen'
  },
  'toggle-show-tools': {
    hint: 'Button that hides/shows the toolbar.',
    original: 'Show/Hide Tools',
    value: 'Tampilake / Singidaken Piranti'
  },
  scroll: {
    hint: 'Verb. As in scrolling a page.',
    original: 'Scroll',
    value: 'Gulung'
  },
  donate: {
    original: 'Donate',
    value: 'Nyumbang'
  },
  home: {
    hint: 'button with logo, top left of tools',
    original: 'Home',
    value: 'Ngarep'
  },
  'modal-new-tab': {
    hint: 'as in browser tab',
    original: 'Open in new tab',
    value: 'Bukak ing tab anyar'
  },
  'tab-edit': {
    hint: 'label for a tab that offer filters for making color adjustments, cropping, transforming, etc.',
    original: 'Edit',
    value: 'Nyunting'
  },
  'tab-file': {
    hint: 'label for a tab that lets user save the image, import, upload, etc.',
    original: 'File',
    value: 'File'
  },
  'tool-brush': {
    hint: 'tool that lets user paint (like in ms paint or photoshop)',
    original: 'Brush',
    value: 'Sikat'
  },
  'tool-paint-bucket': {
    original: 'Paint Bucket',
    value: 'Ember Cat'
  },
  'tool-gradient': {
    original: 'Gradient',
    value: 'Gradien'
  },
  'tool-shape': {
    hint: 'Tool for drawing rectangles, circles, lines',
    original: 'Shape',
    value: 'Wangun'
  },
  'tool-text': {
    hint: 'Tool for placing text on canvas',
    original: 'Text',
    value: 'Teks'
  },
  'tool-hand': {
    hint: 'Tool for navigating (or scrolling around) the canvas. Same name in Photoshop.',
    original: 'Hand Tool',
    value: 'Alat tangan'
  },
  'tool-select': {
    original: 'Select Tool',
    value: 'Milih Alat'
  },
  'tool-zoom': {
    original: 'Zoom',
    value: 'Zoom'
  },
  undo: {
    original: 'Undo',
    value: 'Batalaken'
  },
  redo: {
    original: 'Redo',
    value: 'Baleni maneh'
  },
  'brush-pen': {
    hint: 'Generic "brush" that feels a bit like a pen or marker, as opposed to a paint brush.',
    original: 'Pen',
    value: 'Pen'
  },
  'brush-blend': {
    hint: 'brush with watercolor feel. Makes colors blend/bleed into each other. Different from smudging though.',
    original: 'Blend',
    value: 'Nyampur'
  },
  'brush-sketchy': {
    hint: 'Made up brush name. Pictures drawn with this brush often look like sketches.',
    original: 'Sketchy',
    value: 'Sketsa'
  },
  'brush-pixel': {
    original: 'Pixel',
    value: 'Piksel'
  },
  'brush-chemy': {
    hint: 'Made up brush name, derived from drawing applications "Webchemy" and "Alchemy"',
    original: 'Chemy',
    value: 'Kimia'
  },
  'brush-smudge': {
    hint: 'Common brush/tool in image editing software. Smudges colors around canvas.',
    original: 'Smudge',
    value: 'Smudge'
  },
  'brush-size': {
    original: 'Size',
    value: 'Ukuran'
  },
  'brush-blending': {
    hint: 'Wetness. How much colors will bleed into each other.',
    original: 'Blending',
    value: 'Nyampur'
  },
  'brush-toggle-pressure': {
    hint: 'google "pressure sensitivity". Feature of Wacom tablets, Apple Pencil, XP Pen, etc.',
    original: 'Toggle Pressure Sensitivity',
    value: 'Ngalih Sensitivitas Tekanan'
  },
  'brush-pen-circle': {
    hint: 'as in circle shape',
    original: 'Circle',
    value: 'Lingkaran'
  },
  'brush-pen-chalk': {
    hint: 'as in chalk texture',
    original: 'Chalk',
    value: 'Kapur'
  },
  'brush-pen-calligraphy': {
    hint: 'like a "chisel tip marker"',
    original: 'Calligraphy',
    value: 'Kaligrafi'
  },
  'brush-pen-square': {
    hint: 'as in square shape',
    original: 'Square',
    value: 'Kothak'
  },
  'brush-sketchy-scale': {
    hint: 'How far the connecting lines of sketchy brush can reach.',
    original: 'Scale',
    value: 'Skala'
  },
  'brush-pixel-dither': {
    hint: 'Dithering/halftone pattern',
    original: 'Dither',
    value: 'Dither'
  },
  'brush-chemy-fill': {
    hint: 'Like in MS Paint. A shape can have be outlined, and filled.',
    original: 'Fill',
    value: 'Isi'
  },
  'brush-chemy-stroke': {
    original: 'Stroke',
    value: 'Stroke'
  },
  'brush-chemy-mirror-x': {
    original: 'Horizontal Symmetry',
    value: 'Simetri Horisontal'
  },
  'brush-chemy-mirror-y': {
    original: 'Vertical Symmetry',
    value: 'Simetri Vertikal'
  },
  'brush-chemy-gradient': {
    hint: 'as in color gradient',
    original: 'Gradient',
    value: 'Gradien'
  },
  'brush-eraser-transparent-bg': {
    hint: 'as in transparent background/bottom layer',
    original: 'Transparent Background',
    value: 'Latar mburi Transparan'
  },
  stabilizer: {
    hint: 'Common feature in drawing software to make lines smoother',
    original: 'Stabilizer',
    value: 'Penstabil'
  },
  'stabilizer-title': {
    hint: '-title keys are usually more verbose description. shown as a tooltip.',
    original: 'Stroke Stabilizer',
    value: 'Stroke Stabiliser'
  },
  eyedropper: {
    original: 'Eyedropper',
    value: 'Eyedropper'
  },
  'secondary-color': {
    hint: 'or sometimes called "background" color. Photoshop, MS Paint, etc. allow user to switch between two colors.',
    original: 'Secondary Color',
    value: 'Warna Sekunder'
  },
  'manual-color-input': {
    hint: 'A dialog where users can manually type the color, each channel a number. "mci" keys all related to this dialog.',
    original: 'Manual Color Input',
    value: 'Input Warna Manual'
  },
  'mci-hex': {
    hint: 'as in hex code. Hex representation of color. „#f00“ → red',
    original: 'Hex',
    value: 'Hex'
  },
  'mci-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Nyalin'
  },
  'modal-ok': {
    hint: 'main confirmation button in dialogs. Can be to confirm an action, not just acknowledge information.',
    original: 'Ok',
    value: 'Nggih'
  },
  'modal-cancel': {
    original: 'Cancel',
    value: 'Mbatalake'
  },
  'modal-close': {
    hint: 'as in close a dialog box',
    original: 'Close',
    value: 'Nutup'
  },
  'layers-active-layer': {
    hint: 'currently selected layer that user is drawing on',
    original: 'Active Layer',
    value: 'Lapisan Aktif'
  },
  'layers-layer': {
    original: 'Layer',
    value: 'Lapisan'
  },
  'layers-copy': {
    hint: 'noun',
    original: 'copy',
    value: 'salinan'
  },
  'layers-blending': {
    hint: 'Common feature in image editing & drawing software. All layers-blend- keys relate to this. https://en.wikipedia.org/wiki/Blend_modes',
    original: 'Blending',
    value: 'Nyampur'
  },
  'layers-new': {
    hint: 'action',
    original: 'New Layer',
    value: 'Lapisan Anyar'
  },
  'layers-remove': {
    hint: 'action',
    original: 'Remove Layer',
    value: 'Mbusak Lapisan'
  },
  'layers-duplicate': {
    hint: 'action',
    original: 'Duplicate Layer',
    value: 'Duplikat Lapisan'
  },
  'layers-merge': {
    hint: 'action',
    original: 'Merge with layer below',
    value: 'Gabung karo lapisan ngisor'
  },
  'layers-merge-all': {
    original: 'Merge all',
    value: 'Gabung Kabeh'
  },
  'layers-rename': {
    hint: 'action',
    original: 'Rename',
    value: 'Ganti jeneng'
  },
  'layers-active-layer-visible': {
    original: 'Active layer is visible',
    value: 'Lapisan aktif katon'
  },
  'layers-active-layer-hidden': {
    original: 'Active layer is hidden',
    value: 'Lapisan aktif didhelikake'
  },
  'layers-visibility-toggle': {
    original: 'Layer Visibility',
    value: 'Visibilitas Lapisan'
  },
  'layers-blend-normal': {
    original: 'normal',
    value: 'lumrah'
  },
  'layers-blend-darken': {
    original: 'darken',
    value: 'peteng'
  },
  'layers-blend-multiply': {
    original: 'multiply',
    value: 'multiply'
  },
  'layers-blend-color-burn': {
    original: 'color burn',
    value: 'bakar werna'
  },
  'layers-blend-lighten': {
    original: 'lighten',
    value: 'ngenthengake'
  },
  'layers-blend-screen': {
    original: 'screen',
    value: 'layar'
  },
  'layers-blend-color-dodge': {
    original: 'color dodge',
    value: 'werna ngindari'
  },
  'layers-blend-overlay': {
    original: 'overlay',
    value: 'overlay'
  },
  'layers-blend-soft-light': {
    original: 'soft light',
    value: 'cahya alus'
  },
  'layers-blend-hard-light': {
    original: 'hard light',
    value: 'meh ora cahya'
  },
  'layers-blend-difference': {
    original: 'difference',
    value: 'prabédan'
  },
  'layers-blend-exclusion': {
    original: 'exclusion',
    value: 'pangecualian'
  },
  'layers-blend-hue': {
    original: 'hue',
    value: 'werna'
  },
  'layers-blend-saturation': {
    original: 'saturation',
    value: 'kejenuhan'
  },
  'layers-blend-color': {
    original: 'color',
    value: 'werna'
  },
  'layers-blend-luminosity': {
    original: 'luminosity',
    value: 'padhang'
  },
  'layers-rename-title': {
    hint: '-title in code usually means it shows up in a tooltip, or it’s the header of a dialog box, so it can be a little bit longer.',
    original: 'Rename Layer',
    value: 'Ganti jeneng Layer'
  },
  'layers-rename-name': {
    hint: 'noun',
    original: 'Name',
    value: 'Jeneng'
  },
  'layers-rename-clear': {
    hint: 'aka action of deleting entered name',
    original: 'Clear Name',
    value: 'Cetha Jeneng'
  },
  'layers-rename-sketch': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Sketch',
    value: 'Sketsa'
  },
  'layers-rename-colors': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Colors',
    value: 'Werna-werna'
  },
  'layers-rename-shading': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Shading',
    value: 'Shading'
  },
  'layers-rename-lines': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Lines',
    value: 'Garis-garis'
  },
  'layers-rename-effects': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Effects',
    value: 'Efek'
  },
  'layers-rename-foreground': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Foreground',
    value: 'Latar ngarep'
  },
  'layers-merge-modal-title': {
    original: 'Merge/Mix Layers',
    value: 'Gabung / Nyampur Lapisan'
  },
  'layers-merge-description': {
    hint: 'After ":" a list of mix/blend modes is shown.',
    original: 'Merges the selected layer with the one underneath. Select the mix mode:',
    value: 'Nggabungake lapisan sing dipilih karo sing ana ing ngisor. Pilih mode campuran:'
  },
  'file-no-autosave': {
    hint: "let user know they can't autosave, and there's no cloud storage. Keep short so fits on one line",
    original: 'No autosave, no cloud storage',
    value: 'Ora ana autosave, ora ana panyimpenan awan'
  },
  'file-new': {
    hint: 'action',
    original: 'New',
    value: 'Anyar'
  },
  'file-import': {
    hint: 'action',
    original: 'Import',
    value: 'Ngimpor'
  },
  'file-save': {
    hint: 'action',
    original: 'Save',
    value: 'Simpen'
  },
  'file-format': {
    original: 'File Format',
    value: 'Format file'
  },
  'file-copy': {
    hint: 'verb',
    original: 'Copy',
    value: 'Nyalin'
  },
  'file-copy-title': {
    original: 'Copy To Clipboard',
    value: 'Nyalin Menyang Clipboard'
  },
  'file-share': {
    hint: 'Common feature on phones. Pressing share button allows to send data to a different app, e.g. as a text message to someone.',
    original: 'Share',
    value: 'Nuduhake'
  },
  'file-storage': {
    hint: 'Made up term. Allows users to store drawing in the browser. (not synced across devices)',
    original: 'Browser Storage',
    value: 'Panyimpenan Browser'
  },
  'file-storage-thumb-title': {
    hint: 'when reopening or reloading',
    original: 'Restores when reopening page',
    value: 'Mulihake nalika mbukak maneh kaca'
  },
  'file-storage-about': {
    hint: 'link to Browser Storage help/user manual',
    original: 'About Browser Storage',
    value: 'Babagan Panyimpenan Browser'
  },
  'file-storage-cant-access': {
    hint: 'an error. Can’t access the storage.',
    original: "Can't access",
    value: 'Ora bisa ngakses'
  },
  'file-storage-empty': {
    hint: 'nothing stored',
    original: 'Empty',
    value: 'Kosong'
  },
  'file-storage-store': {
    hint: 'verb',
    original: 'Store',
    value: 'nyimpen'
  },
  'file-storage-clear': {
    original: 'Clear',
    value: 'Cetha'
  },
  'file-storage-storing': {
    hint: 'while it is in the process of storing',
    original: 'Storing',
    value: 'Nyimpen'
  },
  'file-storage-overwrite': {
    hint: 'overwrite what is already stored',
    original: 'Overwrite',
    value: 'Nimpa'
  },
  'file-storage-min-ago': {
    hint: "{x} <- needs to stay, it's a placeholder.",
    original: '{x}min ago',
    value: '{x}min kepungkur'
  },
  'file-storage-hours-ago': {
    original: '{x}h ago',
    value: '{x}h kepungkur'
  },
  'file-storage-days-ago': {
    original: '{x}d ago',
    value: '{x}d biyen'
  },
  'file-storage-month-ago': {
    original: '> 1month ago',
    value: '> 1wulan kepungkur'
  },
  'file-storage-restored': {
    original: 'Restored from Browser Storage',
    value: 'Dipulihake saka Panyimpenan Browser'
  },
  'file-storage-stored': {
    original: 'Stored to Browser Storage',
    value: 'Disimpen menyang Panyimpenan Browser'
  },
  'file-storage-failed': {
    original: 'Failed to store to Browser Storage',
    value: 'Gagal nyimpen menyang Panyimpenan Browser'
  },
  'file-storage-failed-1': {
    original: 'Failed to store. Possible causes:',
    value: 'Gagal nyimpen. Penyebab sing bisa ditindakake:'
  },
  'file-storage-failed-2': {
    original: 'Out of disk space',
    value: 'Metu saka papan disk'
  },
  'file-storage-failed-3': {
    original: 'Storage disabled in incognito tab',
    value: 'Panyimpenan dipateni ing tab incognito'
  },
  'file-storage-failed-4': {
    hint: 'as in: unsupported feature',
    original: "Browser doesn't support storage",
    value: 'Browser ora ndhukung panyimpenan'
  },
  'file-storage-failed-clear': {
    original: 'Failed to clear.',
    value: 'Gagal mbusak.'
  },
  'file-upload': {
    hint: 'verb',
    original: 'Upload',
    value: 'Unggahan'
  },
  'cleared-layer': {
    hint: 'the layer has been cleared. It is now empty',
    original: 'Cleared layer',
    value: 'Lapisan sing diresiki'
  },
  'cleared-selected-area': {
    original: 'Cleared selected area',
    value: 'Mbusak area sing dipilih'
  },
  filled: {
    hint: 'layer got filled with a color',
    original: 'Filled',
    value: 'Kapenuhan'
  },
  'filled-selected-area': {
    original: 'Filled selection',
    value: 'Pilihan sing diisi'
  },
  'new-title': {
    original: 'New Image',
    value: 'Gambar Anyar'
  },
  'new-current': {
    hint: 'as in "reuse the current image resolution"',
    original: 'Current',
    value: 'Saiki'
  },
  'new-fit': {
    hint: 'make dimensions of image fit your window size',
    original: 'Fit',
    value: 'Fit'
  },
  'new-oversize': {
    hint: 'like oversized clothes.',
    original: 'Oversize',
    value: 'Kegedhen'
  },
  'new-square': {
    hint: 'square image format',
    original: 'Square',
    value: 'Kothak'
  },
  'new-landscape': {
    hint: 'https://en.wikipedia.org/wiki/Page_orientation',
    original: 'Landscape',
    value: 'Malang'
  },
  'new-portrait': {
    original: 'Portrait',
    value: 'Mujur'
  },
  'new-screen': {
    hint: 'resolution of computer screen',
    original: 'Screen',
    value: 'Layar'
  },
  'new-video': {
    hint: 'dimensions of a video (usually 16:9)',
    original: 'Video',
    value: 'Video'
  },
  'new-din-paper': {
    hint: 'https://en.wikipedia.org/wiki/Paper_size',
    original: 'DIN Paper',
    value: 'Kertas DIN'
  },
  'new-px': {
    hint: 'abbreviation of pixel',
    original: 'px',
    value: 'px'
  },
  'new-ratio': {
    hint: 'aspect ratio',
    original: 'Ratio',
    value: 'Rasio'
  },
  'upload-title': {
    hint: '"Imgur" is a website name.',
    original: 'Upload to Imgur',
    value: 'Upload menyang Imgur'
  },
  'upload-link-notice': {
    hint: 'Explaining to user even though their upload is unlisted, they should be careful who they share the link with. No password protection.',
    original: 'Anyone with the link to your uploaded image will be able to view it.',
    value: 'Sapa wae sing duwe pranala menyang gambar sing sampeyan upload bakal bisa ndeleng.'
  },
  'upload-name': {
    hint: 'noun',
    original: 'Title',
    value: 'Judhul'
  },
  'upload-title-untitled': {
    original: 'Untitled',
    value: 'Tanpa irah-irahan'
  },
  'upload-caption': {
    hint: 'as in image caption / description',
    original: 'Caption',
    value: 'Caption'
  },
  'upload-submit': {
    hint: 'verb',
    original: 'Upload',
    value: 'Unggahan'
  },
  'upload-uploading': {
    hint: 'in the process of uploading',
    original: 'Uploading...',
    value: 'Ngunggah...'
  },
  'upload-success': {
    original: 'Upload Successful',
    value: 'Upload Kasil'
  },
  'upload-failed': {
    original: 'Upload failed.',
    value: 'Unggahan gagal'
  },
  'upload-delete': {
    original: 'To delete your image from Imgur visit:',
    value: 'Kanggo mbusak gambar saka Imgur ngunjungi:'
  },
  'cropcopy-title-copy': {
    original: 'Copy To Clipboard',
    value: 'Salin menyang Clipboard'
  },
  'cropcopy-title-crop': {
    hint: 'Cropping is a common feature in image editing.',
    original: 'Crop',
    value: 'Potong'
  },
  'cropcopy-click-hold': {
    original: 'Right-click or press hold to copy.',
    value: 'Klik-tengen utawa pencet terus kanggo nyalin.'
  },
  'cropcopy-btn-copy': {
    hint: 'confirmation button. Should be short.',
    original: 'To Clipboard',
    value: 'Kanggo Clipboard.'
  },
  'cropcopy-copied': {
    original: 'Copied.',
    value: 'Disalin.'
  },
  'cropcopy-btn-crop': {
    hint: 'confirmation button. Should be short.',
    original: 'Apply Crop',
    value: 'Aplikasi Potong'
  },
  'crop-drag-to-crop': {
    hint: 'drag the mouse to adjust cropping',
    original: 'Drag to crop',
    value: 'Seret kanggo motong'
  },
  'filter-crop-extend': {
    original: 'Crop/Extend',
    value: 'Potong/Ngluwihi'
  },
  'filter-flip': {
    original: 'Flip',
    value: 'Flip'
  },
  'filter-perspective': {
    hint: 'perspective transformation',
    original: 'Perspective',
    value: 'Perspektif'
  },
  'filter-resize': {
    original: 'Resize',
    value: 'Ngowahi ukuran'
  },
  'filter-rotate': {
    original: 'Rotate',
    value: 'Puteran'
  },
  'filter-transform': {
    hint: 'more generic transformation. Commonly known as "free transform"',
    original: 'Transform',
    value: 'Ngowahi'
  },
  'filter-bright-contrast': {
    hint: 'brightness contrast',
    original: 'Bright/Contrast',
    value: 'Padhang/Kontras'
  },
  'filter-curves': {
    hint: 'color curves',
    original: 'Curves',
    value: 'Kurva'
  },
  'filter-hue-sat': {
    original: 'Hue/Saturation',
    value: 'Werna/Saturasi'
  },
  'filter-invert': {
    hint: 'invert the colors',
    original: 'Invert',
    value: 'Walik'
  },
  'filter-tilt-shift': {
    hint: 'or just depth-of-field. https://en.wikipedia.org/wiki/Tilt%E2%80%93shift_photography',
    original: 'Tilt Shift',
    value: 'Ngalih Miring'
  },
  'filter-to-alpha': {
    hint: 'feature that can isolate line drawings if it is a black and white image.',
    original: 'To Alpha',
    value: 'Kanggo Alpha'
  },
  'filter-triangle-blur': {
    hint: 'or just blur',
    original: 'Triangle Blur',
    value: 'Triangle Blur'
  },
  'filter-unsharp-mask': {
    hint: 'or just sharpen, https://en.wikipedia.org/wiki/Unsharp_masking',
    original: 'Unsharp Mask',
    value: 'Topeng Unsharp'
  },
  'filter-crop-title': {
    hint: 'same as the button, but can be longer',
    original: 'Crop / Extend',
    value: 'Potong/Ngluwihi'
  },
  'filter-crop-description': {
    hint: 'descriptions explain the "filter" a tiny bit. Most importantly it says if it’s applied to a single layer, or the entire image (all layer)',
    original: 'Crop or extend the image.',
    value: 'Potong utawa ngluwihi gambar.'
  },
  'filter-crop-left': {
    original: 'Left',
    value: 'Ngiwa'
  },
  'filter-crop-right': {
    original: 'Right',
    value: 'Bener'
  },
  'filter-crop-top': {
    original: 'Top',
    value: 'Ndhuwur'
  },
  'filter-crop-bottom': {
    original: 'Bottom',
    value: 'Ngisor'
  },
  'filter-crop-rule-thirds': {
    hint: 'https://en.wikipedia.org/wiki/Rule_of_thirds',
    original: 'Rule of Thirds',
    value: 'Aturan Katelu'
  },
  'filter-crop-fill': {
    hint: 'If image gets extended, what color to fill that new area with',
    original: 'Fill',
    value: 'Isi'
  },
  'filter-flip-title': {
    hint: 'same as the button, but can be longer',
    original: 'Flip',
    value: 'Flip'
  },
  'filter-flip-description': {
    original: 'Flips layer or whole image.',
    value: 'Flips lapisan utawa kabèh gambar.'
  },
  'filter-flip-horizontal': {
    original: 'Horizontal',
    value: 'Horisontal'
  },
  'filter-flip-vertical': {
    original: 'Vertical',
    value: 'Vertikal'
  },
  'filter-flip-image': {
    original: 'Flip Image',
    value: 'Flip Gambar'
  },
  'filter-flip-layer': {
    original: 'Flip Layer',
    value: 'Flip Lapisan'
  },
  'filter-perspective-title': {
    hint: 'same as the button, but can be longer',
    original: 'Perspective',
    value: 'Perspektif'
  },
  'filter-perspective-description': {
    original: 'Transforms the selected layer.',
    value: 'Ngowahi lapisan sing dipilih.'
  },
  'filter-resize-title': {
    hint: 'same as the button, but can be longer',
    original: 'Resize',
    value: 'Ngowahi ukuran'
  },
  'filter-resize-description': {
    original: 'Resizes the image.',
    value: 'Ngowahi ukuran gambar.'
  },
  'filter-rotate-title': {
    hint: 'same as the button, but can be longer',
    original: 'Rotate',
    value: 'Puteran'
  },
  'filter-rotate-description': {
    original: 'Rotates the image.',
    value: 'Puter gambar.'
  },
  'filter-transform-empty': {
    hint: 'nothing on the layer, just transparency',
    original: 'Layer is empty.',
    value: 'Lapisan kosong'
  },
  'filter-transform-title': {
    hint: 'same as the button, but can be longer',
    original: 'Transform',
    value: 'Ngowahi'
  },
  'filter-transform-description': {
    hint: 'as in the Shift key.',
    original: 'Transforms selected layer. Hold Shift for additional behavior.',
    value: 'Ngowahi lapisan sing dipilih. Tahan Shift kanggo prilaku tambahan.'
  },
  'filter-transform-rotation': {
    hint: 'or Angle',
    original: 'Rotation',
    value: 'Rotasi'
  },
  'filter-transform-flip': {
    original: 'Flip',
    value: 'Flip'
  },
  'filter-transform-center': {
    hint: 'verb or noun',
    original: 'Center',
    value: 'Tengah'
  },
  'filter-transform-constrain': {
    hint: 'short version of "constrain proportions" – feature that locks the aspect ratio',
    original: 'Constrain',
    value: 'Watesan'
  },
  'filter-transform-snap': {
    hint: 'or snapping. Common feature in graphics software. Makes stuff snap into place so it’s easier to align elements in an image',
    original: 'Snap',
    value: 'Snap'
  },
  'filter-transform-snap-title': {
    hint: 'same as above, but as a tooltip',
    original: 'Snap Rotation And Position',
    value: 'Snap Rotasi lan Posisi'
  },
  'filter-bright-contrast-title': {
    hint: 'same as the button, but can be longer',
    original: 'Brightness / Contrast',
    value: 'Padhang / Kontras'
  },
  'filter-bright-contrast-description': {
    original: 'Change brightness and contrast for the selected layer.',
    value: 'Ganti padhange lan kontras kanggo lapisan sing dipilih.'
  },
  'filter-bright-contrast-brightness': {
    original: 'Brightness',
    value: 'Padhang'
  },
  'filter-bright-contrast-contrast': {
    original: 'Contrast',
    value: 'Kontras'
  },
  'filter-curves-title': {
    hint: 'same as the button, but can be longer',
    original: 'Curves',
    value: 'Kurva'
  },
  'filter-curves-description': {
    original: 'Apply curves on the selected layer.',
    value: 'Gunakake kurva ing lapisan sing dipilih.'
  },
  'filter-curves-all': {
    original: 'All',
    value: 'Kabeh'
  },
  'filter-hue-sat-title': {
    hint: 'same as the button, but can be longer',
    original: 'Hue / Saturation',
    value: 'Hue/Saturasi'
  },
  'filter-hue-sat-description': {
    original: 'Change hue and saturation for the selected layer.',
    value: 'Ganti hue lan jenuh kanggo lapisan sing dipilih.'
  },
  'filter-hue-sat-hue': {
    original: 'Hue',
    value: 'Hue'
  },
  'filter-hue-sat-saturation': {
    original: 'Saturation',
    value: 'Saturasi'
  },
  'filter-applied': {
    hint: 'used like this: „Invert“ applied. Informs that a filter was applied',
    original: 'applied',
    value: 'ditrapake'
  },
  'filter-tilt-shift-title': {
    hint: 'same as the button, but can be longer',
    original: 'Tilt Shift',
    value: 'Ngalih Miring'
  },
  'filter-tilt-shift-description': {
    original: 'Applies tilt shift on the selected layer.',
    value: 'Nindakake shift miring ing lapisan sing dipilih.'
  },
  'filter-tilt-shift-blur': {
    hint: 'words can be individually translated',
    original: 'Blur Radius',
    value: 'Radius Blur'
  },
  'filter-tilt-shift-gradient': {
    hint: 'words can be individually translated',
    original: 'Gradient Radius',
    value: 'Radius Gradien'
  },
  'filter-to-alpha-title': {
    hint: 'same as the button, but can be longer',
    original: 'To Alpha',
    value: 'Kanggo Alpha'
  },
  'filter-to-alpha-description': {
    hint: 'after ":" follow two options',
    original: 'Generates alpha channel for selected layer from:',
    value: 'Ngasilake saluran alpha kanggo lapisan sing dipilih saka:'
  },
  'filter-to-alpha-inverted-lum': {
    hint: 'luminance is same as the layer blend mode "luminosity"',
    original: 'Inverted Luminance',
    value: 'Walik Luminance'
  },
  'filter-to-alpha-lum': {
    original: 'Luminance',
    value: 'Luminance'
  },
  'filter-to-alpha-replace': {
    hint: 'What color to replace the rgb channels with',
    original: 'Replace RGB',
    value: 'Ganti RGB'
  },
  'filter-triangle-blur-title': {
    hint: 'same as the button, but can be longer',
    original: 'Triangle Blur',
    value: 'Triangle Blur'
  },
  'filter-triangle-blur-description': {
    original: 'Applies triangle blur on the selected layer.',
    value: 'Aplikasi blur segitiga ing lapisan sing dipilih.'
  },
  'filter-unsharp-mask-title': {
    hint: 'same as the button, but can be longer',
    original: 'Unsharp Mask',
    value: 'Topeng Unsharp'
  },
  'filter-unsharp-mask-description': {
    original: 'Sharpens the selected layer by scaling pixels away from the average of their neighbors.',
    value: 'Ngasah lapisan sing dipilih kanthi ukuran piksel adoh saka rata-rata tanggane.'
  },
  'filter-unsharp-mask-strength': {
    original: 'Strength',
    value: 'Kekuwatan'
  },
  'filter-grid': {
    original: 'Grid',
    value: 'Grid'
  },
  'filter-grid-description': {
    original: 'Draws grid on selected layer.',
    value: 'Nggambar kothak ing lapisan sing dipilih.'
  },
  'filter-noise': {
    original: 'Noise',
    value: 'Rame'
  },
  'filter-noise-description': {
    original: 'Adds noise to selected layer.',
    value: 'Nambahake gangguan menyang lapisan sing dipilih.'
  },
  'filter-noise-scale': {
    original: 'Scale',
    value: 'Skala'
  },
  'filter-noise-alpha': {
    original: 'Alpha',
    value: 'Alpha'
  },
  'filter-pattern': {
    original: 'Pattern',
    value: 'Pola'
  },
  'filter-pattern-description': {
    original: 'Generates pattern on selected layer. Drag the preview for further controls.',
    value: 'Ngasilake pola ing lapisan sing dipilih. Seret pratinjau kanggo kontrol luwih lanjut.'
  },
  'filter-distort': {
    original: 'Distort',
    value: 'Distort'
  },
  'filter-distort-description': {
    original: 'Distorts the selected layer.',
    value: 'Ngrusak lapisan sing dipilih.'
  },
  'filter-distort-phase': {
    original: 'Phase',
    value: 'Urutane'
  },
  'filter-distort-stepsize': {
    original: 'Step Size',
    value: 'Ukuran Langkah'
  },
  'filter-distort-sync-xy': {
    original: 'Sync XY',
    value: 'Sinkronisasi XY'
  },
  'filter-vanish-point': {
    original: 'Vanish Point',
    value: 'Titik Lenyap'
  },
  'filter-vanish-point-title': {
    original: 'Vanishing Point',
    value: 'Titik ilang'
  },
  'filter-vanish-point-description': {
    original: 'Adds vanishing point to selected layer. Drag preview to move.',
    value: 'Nambah titik ilang menyang lapisan sing dipilih. Seret pratinjau kanggo mindhah.'
  },
  'filter-vanish-point-lines': {
    hint: 'As in: the number of lines',
    original: 'Lines',
    value: 'Garis-garis'
  },
  'import-opening': {
    hint: 'in the process of opening a large file',
    original: 'Opening file...',
    value: 'Mbukak file...'
  },
  'import-title': {
    original: 'Import Image',
    value: 'Impor Gambar'
  },
  'import-too-large': {
    original: 'Image too large, will be downscaled.',
    value: 'Gambar gedhe banget, bakal dikurangi.'
  },
  'import-btn-as-layer': {
    hint: 'import as layer. Button label, should be short',
    original: 'As Layer',
    value: 'Minangka Lapisan'
  },
  'import-btn-as-image': {
    hint: 'import as image. Button label, should be short',
    original: 'As Image',
    value: 'Minangka Gambar'
  },
  'import-as-layer-title': {
    original: 'Import Image as New Layer',
    value: 'Impor Gambar minangka Lapisan Anyar'
  },
  'import-as-layer-description': {
    original: 'Adjust the position of the imported image.',
    value: 'Nyetel posisi gambar sing diimpor.'
  },
  'import-as-layer-limit-reached': {
    hint: 'Kleki allows only a certain number of layers',
    original: 'Layer limit reached. Image will be placed on existing layer.',
    value: 'Watesan lapisan wis tekan. Gambar bakal diselehake ing lapisan sing wis ana.'
  },
  'import-as-layer-fit': {
    hint: 'verb. imported image made to fit inside canvas.',
    original: 'Fit',
    value: 'Fit'
  },
  'import-flatten': {
    hint: 'Turn image that has multiple layers into an image that has only one layer.',
    original: 'Flatten image',
    value: 'Gambar flatten'
  },
  'import-unsupported-file': {
    hint: 'e.g. user tried to open a WAV',
    original: 'Unsupported file type. See Help for supported types.',
    value: 'Jinis file sing ora didhukung. Waca Bantuan kanggo jinis sing didhukung.'
  },
  'import-broken-file': {
    hint: 'if an image file is broken',
    original: "Couldn't load image. File might be corrupted.",
    value: 'Ora bisa mbukak gambar. File bisa rusak.'
  },
  'import-psd-unsupported': {
    hint: 'PSD is photoshop file format. flatten as in „flatten image“ in photoshop. Merged all layers',
    original: 'Unsupported features. PSD had to be flattened.',
    value: 'Fitur sing ora didhukung. PSD kudu flattened.'
  },
  'import-psd-limited-support': {
    hint: 'PSD has lots of fancy features that Kleki doesn\'t have, so when importing a PSD it often has to simplify things. So it might look different compared to when opening in Photoshop. We’re informing the user if they "flatten" the image it will be accurate despite losing layers. The user has to weigh, do they care about having layers, or do they care about it looking right.',
    original: 'PSD support is limited. Flattened will more likely look correct.',
    value: 'Dhukungan PSD diwatesi. Flattened bakal luwih katon bener.'
  },
  'import-psd-too-large': {
    original: 'Image exceeds maximum dimensions of {x} x {x} pixels. Unable to import.',
    value: 'Gambar ngluwihi ukuran maksimal {x} x {x} piksel. Ora bisa ngimpor.'
  },
  'import-psd-size': {
    original: 'Image size',
    value: 'Ukuran gambar'
  },
  'hand-reset': {
    hint: 'resets zoom (to 100%) and rotation (to 0°)',
    original: 'Reset',
    value: 'Reset'
  },
  'hand-fit': {
    hint: 'makes canvas cover whole working area',
    original: 'Fit',
    value: 'Fit'
  },
  'hand-inertia-scrolling': {
    hint: 'Canvas will continue to move even after releasing pointer.',
    original: 'Inertia Scrolling',
    value: 'Gulung Inertia'
  },
  'bucket-tolerance': {
    hint: 'a common feature for paint bucket',
    original: 'Tolerance',
    value: 'Toleransi'
  },
  'bucket-sample': {
    hint: 'like "taking a sample"',
    original: 'Sample',
    value: 'Sampel'
  },
  'bucket-sample-title': {
    original: 'Which layers to sample color from',
    value: 'Kang lapisan kanggo sampel werna saka'
  },
  'bucket-sample-all': {
    hint: 'all layers',
    original: 'All',
    value: 'Kabeh'
  },
  'bucket-sample-active': {
    hint: 'currently active layer',
    original: 'Active',
    value: 'Aktif'
  },
  'bucket-sample-above': {
    hint: 'layer above active layer',
    original: 'Above',
    value: 'Ndhuwur'
  },
  'bucket-grow': {
    hint: 'verb. Or expand.',
    original: 'Grow',
    value: 'Tuwuh'
  },
  'bucket-grow-title': {
    original: 'Grow filled area (in pixels)',
    value: 'Tuwuh area sing diisi (ing piksel)'
  },
  'bucket-contiguous': {
    hint: 'common setting for paint bucket in image editing software',
    original: 'Contiguous',
    value: 'Jejer'
  },
  'bucket-contiguous-title': {
    hint: 'tooltip explaining Contiguous',
    original: 'Only fill connected areas',
    value: 'Mung isi wilayah sing disambungake'
  },
  'gradient-linear': {
    original: 'Linear',
    value: 'Linear'
  },
  'gradient-linear-mirror': {
    original: 'Linear-Mirror',
    value: 'Linear-Mirror'
  },
  'gradient-radial': {
    original: 'Radial',
    value: 'Radial'
  },
  'shape-stroke': {
    original: 'Stroke',
    value: 'Stroke'
  },
  'shape-fill': {
    original: 'Fill',
    value: 'Isi'
  },
  'shape-rect': {
    hint: 'rectangle shape',
    original: 'Rectangle',
    value: 'Persegi panjang'
  },
  'shape-ellipse': {
    hint: 'ellipse shape',
    original: 'Ellipse',
    value: 'Ellipse'
  },
  'shape-line': {
    original: 'Line',
    value: 'Baris'
  },
  'shape-line-width': {
    original: 'Line Width',
    value: 'Jembar Garis'
  },
  'shape-outwards': {
    hint: 'grows shape from the center outwards',
    original: 'Outwards',
    value: 'Njaba'
  },
  'shape-fixed': {
    hint: 'fixed ratio of 1:1',
    original: 'Fixed 1:1',
    value: 'Tetep 1:1'
  },
  'text-instruction': {
    original: 'Click canvas to place text',
    value: 'Klik kanvas kanggo nyelehake teks'
  },
  'text-title': {
    original: 'Add Text',
    value: 'Tambah Teks'
  },
  'text-text': {
    original: 'Text',
    value: 'Teks'
  },
  'text-font': {
    original: 'Font',
    value: 'Font'
  },
  'text-placeholder': {
    hint: 'placeholder in text input.',
    original: 'Your text',
    value: 'Teks Panjenengan'
  },
  'text-color': {
    original: 'Color',
    value: 'Werna'
  },
  'text-size': {
    original: 'Size',
    value: 'Ukuran'
  },
  'text-line-height': {
    original: 'Line Height',
    value: 'Dhuwur garis'
  },
  'text-letter-spacing': {
    original: 'Letter Spacing',
    value: 'Spasi layang'
  },
  'text-left': {
    hint: 'left, center, right text formatting like in Word',
    original: 'Left',
    value: 'Ngiwa'
  },
  'text-center': {
    original: 'Center',
    value: 'Tengah'
  },
  'text-right': {
    original: 'Right',
    value: 'Bener'
  },
  'text-italic': {
    original: 'Italic',
    value: 'Miring'
  },
  'text-bold': {
    original: 'Bold',
    value: 'Kandel'
  },
  'select-select': {
    original: 'Select',
    value: 'Pilih'
  },
  'select-transform': {
    original: 'Transform',
    value: 'Ngowahi'
  },
  'select-lasso': {
    original: 'Lasso',
    value: 'Lasso'
  },
  'select-polygon': {
    original: 'Polygon',
    value: 'Poligon'
  },
  'select-boolean-replace': {
    original: 'Replace',
    value: 'Ngganti'
  },
  'select-boolean-add': {
    original: 'Add',
    value: 'Tambah'
  },
  'select-boolean-subtract': {
    original: 'Subtract',
    value: 'Ngurangi'
  },
  'select-all': {
    original: 'All',
    value: 'Kabeh'
  },
  'select-invert': {
    original: 'Invert',
    value: 'Walik'
  },
  'select-reset': {
    original: 'Reset',
    value: 'Reset'
  },
  'select-fill': {
    original: 'Fill',
    value: 'Isi'
  },
  'select-erase': {
    original: 'Erase',
    value: 'Mbusak'
  },
  'select-transform-clone': {
    original: 'Clone',
    value: 'Klone'
  },
  'select-transform-clone-applied': {
    original: 'Cloned',
    value: 'Kloning'
  },
  'select-transform-move-to-layer': {
    original: 'Move to layer:',
    value: 'Pindhah menyang lapisan:'
  },
  'select-transform-applied': {
    original: 'Transformation applied',
    value: 'Transformasi ditrapake'
  },
  'select-transform-empty': {
    original: 'Selected area on active layer is empty.',
    value: 'Wilayah sing dipilih ing lapisan aktif kosong.'
  },
  'save-reminder-title': {
    hint: 'Save Reminder is a popup that shows up after 20 minutes of not saving. Allows user to save on the spot.',
    original: 'Unsaved Work',
    value: 'Karya sing ora disimpen'
  },
  'save-reminder-text': {
    hint: 'turns into: "...saved in <strong>12 minutes</strong>. Save..."',
    original: 'Image was not saved in {a} minutes{b}. Save now to prevent eventual loss.',
    value: 'Gambar ora disimpen ing {a} menit{b}. Simpen saiki kanggo nyegah mundhut pungkasan.'
  },
  'save-reminder-save-psd': {
    original: 'Save As PSD',
    value: 'Simpen Minangka PSD'
  },
  'save-reminder-psd-layers': {
    original: 'PSD will remember all layers.',
    value: 'PSD bakal ngelingi kabeh lapisan.'
  },
  'backup-drawing': {
    hint: '(Embed) If the upload failed, the user can backup/download their drawing.',
    original: 'You can backup your drawing.',
    value: 'Sampeyan bisa nggawe serep gambar sampeyan.'
  },
  submit: {
    original: 'Submit',
    value: 'Kirimake'
  },
  'submit-title': {
    hint: 'tooltip when hovering "Submit" button',
    original: 'Submit Drawing',
    value: 'Kirim Gambar'
  },
  'submit-prompt': {
    hint: 'Dialog asking for confirmation',
    original: 'Submit drawing?',
    value: 'Kirim gambar?'
  },
  'submit-submitting': {
    hint: 'in the process of uploading',
    original: 'Submitting',
    value: 'Ngirim'
  },
  'embed-init-loading': {
    hint: 'when opening the app',
    original: 'Loading app',
    value: 'Loading app'
  },
  'embed-init-waiting': {
    hint: 'The app is loaded, but it’s still waiting for image',
    original: 'Waiting for image',
    value: 'Nunggu gambar'
  },
  unsaved: {
    original: 'Unsaved',
    value: 'Ora disimpen'
  },
  help: {
    hint: 'or user manual',
    original: 'Help',
    value: 'Pitulung'
  },
  'tab-settings': {
    original: 'Settings',
    value: 'Setelan'
  },
  'settings-language': {
    original: 'Language',
    value: 'Basa'
  },
  'settings-language-reload': {
    hint: 'changes will be visible after reloading the page',
    original: 'Will update after reloading.',
    value: 'Bakal nganyari sawise reloading.'
  },
  'settings-theme': {
    original: 'Theme',
    value: 'Tema'
  },
  'settings-save-reminder-label': {
    original: 'Save Reminder',
    value: 'Simpen Pangeling'
  },
  'settings-save-reminder-disabled': {
    original: 'disabled',
    value: 'dipatèni'
  },
  'settings-save-reminder-confirm-title': {
    original: 'Turn off Save Reminder?',
    value: 'Pateni Simpen Pangeling?'
  },
  'settings-save-reminder-confirm-a': {
    original: "There is no autosave and browser tabs don't last forever. If you don't periodically save you will likely lose work.",
    value: 'Ora ana autosave lan tab browser ora langgeng. Yen sampeyan ora nyimpen kanthi periodik, sampeyan bakal kelangan karya.'
  },
  'settings-save-reminder-confirm-b': {
    original: 'Disable at your own risk?',
    value: 'Mateni kanthi resiko dhewe?'
  },
  'settings-save-reminder-confirm-disable': {
    original: 'Disable',
    value: 'Pateni'
  },
  'theme-dark': {
    original: 'Dark',
    value: 'Peteng'
  },
  'theme-light': {
    original: 'Light',
    value: 'Cahya'
  },
  'terms-of-service': {
    original: 'Terms of Service',
    value: 'Katentuan Pangginaan supados langkung Service'
  },
  licenses: {
    hint: 'as in software licenses',
    original: 'Licenses',
    value: 'Lisensi'
  },
  'source-code': {
    original: 'Source Code',
    value: 'Kode Sumber'
  },
  auto: {
    hint: 'automatically decided',
    original: 'auto',
    value: 'otomatis'
  },
  'zoom-in': {
    original: 'Zoom In',
    value: 'Nggedhekake'
  },
  'zoom-out': {
    original: 'Zoom Out',
    value: 'Zoom Metu'
  },
  radius: {
    original: 'Radius',
    value: 'Radius'
  },
  'constrain-proportions': {
    original: 'Constrain Proportions',
    value: 'Watesan Proporsi'
  },
  width: {
    original: 'Width',
    value: 'Jembar'
  },
  height: {
    original: 'Height',
    value: 'Dhuwur'
  },
  opacity: {
    hint: 'for layer opacity, brush opacity, text opacity, ...',
    original: 'Opacity',
    value: 'Opacity'
  },
  red: {
    original: 'Red',
    value: 'Abang'
  },
  green: {
    original: 'Green',
    value: 'Ijo'
  },
  blue: {
    original: 'Blue',
    value: 'Biru'
  },
  eraser: {
    hint: 'noun. As short as possible',
    original: 'Eraser',
    value: 'Pambupus'
  },
  center: {
    hint: 'verb',
    original: 'Center',
    value: 'Tengah'
  },
  layers: {
    hint: 'layers in the context of image editing software',
    original: 'Layers',
    value: 'Lapisans'
  },
  background: {
    hint: 'as in background layer',
    original: 'Background',
    value: 'Latar mburi'
  },
  'scaling-algorithm': {
    hint: 'Label for dropdown where user chooses which computer graphics algorithm should be used to scale the image',
    original: 'Scaling Algorithm',
    value: 'Algoritma Skala'
  },
  'algorithm-smooth': {
    original: 'Smooth',
    value: 'Gamelan'
  },
  'algorithm-pixelated': {
    original: 'Pixelated',
    value: 'Pixelated'
  },
  preview: {
    hint: 'subject. When applying a filter user sees a preview of what the result would look like.',
    original: 'Preview',
    value: 'Pratinjau'
  },
  'angle-snap': {
    hint: 'Snapping. Like when transforming an image and it snaps to the side of the frame.',
    original: 'Snap',
    value: 'Snap'
  },
  'angle-snap-title': {
    original: '45° Angle Snapping',
    value: '45° Sudut Snapping'
  },
  'lock-alpha': {
    hint: 'Short. Freezes the alpha channel of image. Images consist of 4 channels (red, green, blue, alpha). Alpha lock also found in Procreate and photoshop',
    original: 'Lock Alpha',
    value: 'Kunci Alpha'
  },
  'lock-alpha-title': {
    hint: "same as 'lock-alpha' but longer version. Shows up as a tooltip when hovering",
    original: "Locks layer's alpha channel",
    value: 'Ngunci saluran alpha lapisan'
  },
  reverse: {
    hint: 'Reversing the direction of a color gradient',
    original: 'Reverse',
    value: 'Mbalikke'
  },
  'compare-before': {
    original: 'Before',
    value: 'Sadurunge'
  },
  'compare-after': {
    original: 'After',
    value: 'Sawise'
  },
  loading: {
    original: 'Loading',
    value: 'Loading'
  },
  more: {
    original: 'More',
    value: 'Liyane'
  },
  'x-minutes': {
    original: '{x}min',
    value: '{x}min'
  },
  wip: {
    original: 'Work in progress',
    value: 'Kerja ing kemajuan'
  },
  'browser-zoom-help': {
    original: 'Double-tap or pinch-out to reset browser zoom.',
    value: 'Tutul kaping pindho utawa jiwitake kanggo ngreset zoom browser.'
  },
  dismiss: {
    original: 'Dismiss',
    value: 'Ngilangi'
  }
}
