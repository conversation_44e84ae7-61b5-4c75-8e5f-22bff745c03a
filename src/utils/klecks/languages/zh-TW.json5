{
  'switch-ui-left-right': {
    hint: 'Button that switches layout from left to right, to accommodate left handed & right handed visitors.',
    original: 'Switch left/right UI',
    value: '向左/右切換'
  },
  'toggle-show-tools': {
    hint: 'Button that hides/shows the toolbar.',
    original: 'Show/Hide Tools',
    value: '顯示/隱藏工具'
  },
  scroll: {
    hint: 'Verb. As in scrolling a page.',
    original: 'Scroll',
    value: '上滑/下滑'
  },
  donate: {
    original: 'Donate',
    value: '贊助'
  },
  home: {
    hint: 'button with logo, top left of tools',
    original: 'Home',
    value: '主頁'
  },
  'modal-new-tab': {
    hint: 'as in browser tab',
    original: 'Open in new tab',
    value: '在新頁面打開'
  },
  'tab-edit': {
    hint: 'label for a tab that offer filters for making color adjustments, cropping, transforming, etc.',
    original: 'Edit',
    value: '編輯'
  },
  'tab-file': {
    hint: 'label for a tab that lets user save the image, import, upload, etc.',
    original: 'File',
    value: '文件'
  },
  'tool-brush': {
    hint: 'tool that lets user paint (like in ms paint or photoshop)',
    original: 'Brush',
    value: '畫筆工具'
  },
  'tool-paint-bucket': {
    original: 'Paint Bucket',
    value: '油漆桶'
  },
  'tool-gradient': {
    original: 'Gradient',
    value: '漸層'
  },
  'tool-shape': {
    hint: 'Tool for drawing rectangles, circles, lines',
    original: 'Shape',
    value: '形狀'
  },
  'tool-text': {
    hint: 'Tool for placing text on canvas',
    original: 'Text',
    value: '文本'
  },
  'tool-hand': {
    hint: 'Tool for navigating (or scrolling around) the canvas. Same name in Photoshop.',
    original: 'Hand Tool',
    value: '手型工具'
  },
  'tool-select': {
    original: 'Select Tool',
    value: ''
  },
  'tool-zoom': {
    original: 'Zoom',
    value: '縮放工具'
  },
  undo: {
    original: 'Undo',
    value: '撤銷'
  },
  redo: {
    original: 'Redo',
    value: '重做'
  },
  'brush-pen': {
    hint: 'Generic "brush" that feels a bit like a pen or marker, as opposed to a paint brush.',
    original: 'Pen',
    value: '畫筆'
  },
  'brush-blend': {
    hint: 'brush with watercolor feel. Makes colors blend/bleed into each other. Different from smudging though.',
    original: 'Blend',
    value: '水彩刷'
  },
  'brush-sketchy': {
    hint: 'Made up brush name. Pictures drawn with this brush often look like sketches.',
    original: 'Sketchy',
    value: '素描筆'
  },
  'brush-pixel': {
    original: 'Pixel',
    value: '像素化'
  },
  'brush-chemy': {
    hint: 'Made up brush name, derived from drawing applications "Webchemy" and "Alchemy"',
    original: 'Chemy',
    value: '異形圖形'
  },
  'brush-smudge': {
    hint: 'Common brush/tool in image editing software. Smudges colors around canvas.',
    original: 'Smudge',
    value: '暈染'
  },
  'brush-size': {
    original: 'Size',
    value: '筆刷大小'
  },
  'brush-blending': {
    hint: 'Wetness. How much colors will bleed into each other.',
    original: 'Blending',
    value: '水分量'
  },
  'brush-toggle-pressure': {
    hint: 'google "pressure sensitivity". Feature of Wacom tablets, Apple Pencil, XP Pen, etc.',
    original: 'Toggle Pressure Sensitivity',
    value: '筆壓敏度'
  },
  'brush-pen-circle': {
    hint: 'as in circle shape',
    original: 'Circle',
    value: '圓形'
  },
  'brush-pen-chalk': {
    hint: 'as in chalk texture',
    original: 'Chalk',
    value: '粉筆'
  },
  'brush-pen-calligraphy': {
    hint: 'like a "chisel tip marker"',
    original: 'Calligraphy',
    value: '斜角筆'
  },
  'brush-pen-square': {
    hint: 'as in square shape',
    original: 'Square',
    value: '方形'
  },
  'brush-sketchy-scale': {
    hint: 'How far the connecting lines of sketchy brush can reach.',
    original: 'Scale',
    value: '網格長度'
  },
  'brush-pixel-dither': {
    hint: 'Dithering/halftone pattern',
    original: 'Dither',
    value: '像素抖動'
  },
  'brush-chemy-fill': {
    hint: 'Like in MS Paint. A shape can have be outlined, and filled.',
    original: 'Fill',
    value: '填充'
  },
  'brush-chemy-stroke': {
    original: 'Stroke',
    value: '線條'
  },
  'brush-chemy-mirror-x': {
    original: 'Horizontal Symmetry',
    value: '水平對稱'
  },
  'brush-chemy-mirror-y': {
    original: 'Vertical Symmetry',
    value: '垂直對稱'
  },
  'brush-chemy-gradient': {
    hint: 'as in color gradient',
    original: 'Gradient',
    value: '漸變'
  },
  'brush-eraser-transparent-bg': {
    hint: 'as in transparent background/bottom layer',
    original: 'Transparent Background',
    value: '底層透明度'
  },
  stabilizer: {
    hint: 'Common feature in drawing software to make lines smoother',
    original: 'Stabilizer',
    value: '抖動修正'
  },
  'stabilizer-title': {
    hint: '-title keys are usually more verbose description. shown as a tooltip.',
    original: 'Stroke Stabilizer',
    value: '抖動修正線條'
  },
  eyedropper: {
    original: 'Eyedropper',
    value: '顏色選取'
  },
  'secondary-color': {
    hint: 'or sometimes called "background" color. Photoshop, MS Paint, etc. allow user to switch between two colors.',
    original: 'Secondary Color',
    value: '二級色'
  },
  'manual-color-input': {
    hint: 'A dialog where users can manually type the color, each channel a number. "mci" keys all related to this dialog.',
    original: 'Manual Color Input',
    value: '手動輸入顏色'
  },
  'mci-hex': {
    hint: 'as in hex code. Hex representation of color. „#f00“ → red',
    original: 'Hex',
    value: '16進制'
  },
  'mci-copy': {
    hint: 'verb',
    original: 'Copy',
    value: '複製'
  },
  'modal-ok': {
    hint: 'main confirmation button in dialogs. Can be to confirm an action, not just acknowledge information.',
    original: 'Ok',
    value: 'Ok'
  },
  'modal-cancel': {
    original: 'Cancel',
    value: '取消'
  },
  'modal-close': {
    hint: 'as in close a dialog box',
    original: 'Close',
    value: '關閉'
  },
  'layers-active-layer': {
    hint: 'currently selected layer that user is drawing on',
    original: 'Active Layer',
    value: '當前圖層'
  },
  'layers-layer': {
    original: 'Layer',
    value: '圖層'
  },
  'layers-copy': {
    hint: 'noun',
    original: 'copy',
    value: '副本'
  },
  'layers-blending': {
    hint: 'Common feature in image editing & drawing software. All layers-blend- keys relate to this. https://en.wikipedia.org/wiki/Blend_modes',
    original: 'Blending',
    value: '混合模式'
  },
  'layers-new': {
    hint: 'action',
    original: 'New Layer',
    value: '新建圖層'
  },
  'layers-remove': {
    hint: 'action',
    original: 'Remove Layer',
    value: '刪除圖層'
  },
  'layers-duplicate': {
    hint: 'action',
    original: 'Duplicate Layer',
    value: '複製圖層'
  },
  'layers-merge': {
    hint: 'action',
    original: 'Merge with layer below',
    value: '向下合併'
  },
  'layers-merge-all': {
    original: 'Merge all',
    value: ''
  },
  'layers-rename': {
    hint: 'action',
    original: 'Rename',
    value: '重命名'
  },
  'layers-active-layer-visible': {
    original: 'Active layer is visible',
    value: '當前圖層可見'
  },
  'layers-active-layer-hidden': {
    original: 'Active layer is hidden',
    value: '當前圖層隱藏'
  },
  'layers-visibility-toggle': {
    original: 'Layer Visibility',
    value: '圖層可見性'
  },
  'layers-blend-normal': {
    original: 'normal',
    value: '正常'
  },
  'layers-blend-darken': {
    original: 'darken',
    value: '變暗'
  },
  'layers-blend-multiply': {
    original: 'multiply',
    value: '正片疊底'
  },
  'layers-blend-color-burn': {
    original: 'color burn',
    value: '顏色加深'
  },
  'layers-blend-lighten': {
    original: 'lighten',
    value: '變亮'
  },
  'layers-blend-screen': {
    original: 'screen',
    value: '濾色'
  },
  'layers-blend-color-dodge': {
    original: 'color dodge',
    value: '顏色減淡'
  },
  'layers-blend-overlay': {
    original: 'overlay',
    value: '疊加'
  },
  'layers-blend-soft-light': {
    original: 'soft light',
    value: '柔光'
  },
  'layers-blend-hard-light': {
    original: 'hard light',
    value: '強光'
  },
  'layers-blend-difference': {
    original: 'difference',
    value: '差值'
  },
  'layers-blend-exclusion': {
    original: 'exclusion',
    value: '排除'
  },
  'layers-blend-hue': {
    original: 'hue',
    value: '色相'
  },
  'layers-blend-saturation': {
    original: 'saturation',
    value: '飽和度'
  },
  'layers-blend-color': {
    original: 'color',
    value: '顏色'
  },
  'layers-blend-luminosity': {
    original: 'luminosity',
    value: '明度'
  },
  'layers-rename-title': {
    hint: '-title in code usually means it shows up in a tooltip, or it’s the header of a dialog box, so it can be a little bit longer.',
    original: 'Rename Layer',
    value: '重命名圖層'
  },
  'layers-rename-name': {
    hint: 'noun',
    original: 'Name',
    value: '重命名'
  },
  'layers-rename-clear': {
    hint: 'aka action of deleting entered name',
    original: 'Clear Name',
    value: '清除'
  },
  'layers-rename-sketch': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Sketch',
    value: '草圖'
  },
  'layers-rename-colors': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Colors',
    value: '色彩'
  },
  'layers-rename-shading': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Shading',
    value: '陰影'
  },
  'layers-rename-lines': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Lines',
    value: '線條'
  },
  'layers-rename-effects': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Effects',
    value: '效果'
  },
  'layers-rename-foreground': {
    hint: 'noun. Name suggestion for a layer',
    original: 'Foreground',
    value: '前景'
  },
  'layers-merge-modal-title': {
    original: 'Merge/Mix Layers',
    value: '向下合併'
  },
  'layers-merge-description': {
    hint: 'After ":" a list of mix/blend modes is shown.',
    original: 'Merges the selected layer with the one underneath. Select the mix mode:',
    value: '選中層與底下一層合併。選擇混合模式：'
  },
  'file-no-autosave': {
    hint: "let user know they can't autosave, and there's no cloud storage. Keep short so fits on one line",
    original: 'No autosave, no cloud storage',
    value: '不能自動保存，沒有雲端儲存'
  },
  'file-new': {
    hint: 'action',
    original: 'New',
    value: '新建'
  },
  'file-import': {
    hint: 'action',
    original: 'Import',
    value: '打開'
  },
  'file-save': {
    hint: 'action',
    original: 'Save',
    value: '保存'
  },
  'file-format': {
    original: 'File Format',
    value: '檔案格式'
  },
  'file-copy': {
    hint: 'verb',
    original: 'Copy',
    value: '複製'
  },
  'file-copy-title': {
    original: 'Copy To Clipboard',
    value: '複製到剪貼板'
  },
  'file-share': {
    hint: 'Common feature on phones. Pressing share button allows to send data to a different app, e.g. as a text message to someone.',
    original: 'Share',
    value: '分享'
  },
  'file-storage': {
    hint: 'Made up term. Allows users to store drawing in the browser. (not synced across devices)',
    original: 'Browser Storage',
    value: '保存至瀏覽器'
  },
  'file-storage-thumb-title': {
    hint: 'when reopening or reloading',
    original: 'Restores when reopening page',
    value: '重新打開頁面時恢復'
  },
  'file-storage-about': {
    hint: 'link to Browser Storage help/user manual',
    original: 'About Browser Storage',
    value: '關於保存至瀏覽器'
  },
  'file-storage-cant-access': {
    hint: 'an error. Can’t access the storage.',
    original: "Can't access",
    value: '無法獲取'
  },
  'file-storage-empty': {
    hint: 'nothing stored',
    original: 'Empty',
    value: '無內容'
  },
  'file-storage-store': {
    hint: 'verb',
    original: 'Store',
    value: '保存'
  },
  'file-storage-clear': {
    original: 'Clear',
    value: '清除'
  },
  'file-storage-storing': {
    hint: 'while it is in the process of storing',
    original: 'Storing',
    value: '保存中'
  },
  'file-storage-overwrite': {
    hint: 'overwrite what is already stored',
    original: 'Overwrite',
    value: '覆蓋'
  },
  'file-storage-min-ago': {
    hint: "{x} <- needs to stay, it's a placeholder.",
    original: '{x}min ago',
    value: '{x}分鐘前'
  },
  'file-storage-hours-ago': {
    original: '{x}h ago',
    value: '{x}小時前'
  },
  'file-storage-days-ago': {
    original: '{x}d ago',
    value: '{x}天前'
  },
  'file-storage-month-ago': {
    original: '> 1month ago',
    value: '>一個月前'
  },
  'file-storage-restored': {
    original: 'Restored from Browser Storage',
    value: '從瀏覽器內存中恢復'
  },
  'file-storage-stored': {
    original: 'Stored to Browser Storage',
    value: '保存至瀏覽器內存'
  },
  'file-storage-failed': {
    original: 'Failed to store to Browser Storage',
    value: '保存失敗'
  },
  'file-storage-failed-1': {
    original: 'Failed to store. Possible causes:',
    value: '保存失敗。可能是由於：'
  },
  'file-storage-failed-2': {
    original: 'Out of disk space',
    value: '磁盤空間不足'
  },
  'file-storage-failed-3': {
    original: 'Storage disabled in incognito tab',
    value: '隱身頁面中禁用存儲功能'
  },
  'file-storage-failed-4': {
    hint: 'as in: unsupported feature',
    original: "Browser doesn't support storage",
    value: '瀏覽器不支持'
  },
  'file-storage-failed-clear': {
    original: 'Failed to clear.',
    value: '清除失敗'
  },
  'file-upload': {
    hint: 'verb',
    original: 'Upload',
    value: '上傳'
  },
  'cleared-layer': {
    hint: 'the layer has been cleared. It is now empty',
    original: 'Cleared layer',
    value: '圖層已清除'
  },
  'cleared-selected-area': {
    original: 'Cleared selected area',
    value: ''
  },
  filled: {
    hint: 'layer got filled with a color',
    original: 'Filled',
    value: '圖層已填充'
  },
  'filled-selected-area': {
    original: 'Filled selection',
    value: ''
  },
  'new-title': {
    original: 'New Image',
    value: '新建項目'
  },
  'new-current': {
    hint: 'as in "reuse the current image resolution"',
    original: 'Current',
    value: '當前大小'
  },
  'new-fit': {
    hint: 'make dimensions of image fit your window size',
    original: 'Fit',
    value: '適應頁面'
  },
  'new-oversize': {
    hint: 'like oversized clothes.',
    original: 'Oversize',
    value: '大畫布'
  },
  'new-square': {
    hint: 'square image format',
    original: 'Square',
    value: '正方形'
  },
  'new-landscape': {
    hint: 'https://en.wikipedia.org/wiki/Page_orientation',
    original: 'Landscape',
    value: '橫向尺寸'
  },
  'new-portrait': {
    original: 'Portrait',
    value: '縱向尺寸'
  },
  'new-screen': {
    hint: 'resolution of computer screen',
    original: 'Screen',
    value: '顯示分辨率'
  },
  'new-video': {
    hint: 'dimensions of a video (usually 16:9)',
    original: 'Video',
    value: '視頻'
  },
  'new-din-paper': {
    hint: 'https://en.wikipedia.org/wiki/Paper_size',
    original: 'DIN Paper',
    value: '紙張大小'
  },
  'new-px': {
    hint: 'abbreviation of pixel',
    original: 'px',
    value: '像素'
  },
  'new-ratio': {
    hint: 'aspect ratio',
    original: 'Ratio',
    value: '比例'
  },
  'upload-title': {
    hint: '"Imgur" is a website name.',
    original: 'Upload to Imgur',
    value: '上傳到Imgur'
  },
  'upload-link-notice': {
    hint: 'Explaining to user even though their upload is unlisted, they should be careful who they share the link with. No password protection.',
    original: 'Anyone with the link to your uploaded image will be able to view it.',
    value: '他人可以通過生成的圖片鏈接查看你的作品。 '
  },
  'upload-name': {
    hint: 'noun',
    original: 'Title',
    value: '標題'
  },
  'upload-title-untitled': {
    original: 'Untitled',
    value: '未命名'
  },
  'upload-caption': {
    hint: 'as in image caption / description',
    original: 'Caption',
    value: '描述'
  },
  'upload-submit': {
    hint: 'verb',
    original: 'Upload',
    value: '上傳'
  },
  'upload-uploading': {
    hint: 'in the process of uploading',
    original: 'Uploading...',
    value: '上傳中...'
  },
  'upload-success': {
    original: 'Upload Successful',
    value: '上傳成功'
  },
  'upload-failed': {
    original: 'Upload failed.',
    value: '上傳失敗'
  },
  'upload-delete': {
    original: 'To delete your image from Imgur visit:',
    value: '要從Imgur刪除你的圖片, 請訪問:'
  },
  'cropcopy-title-copy': {
    original: 'Copy To Clipboard',
    value: '複製到剪貼板'
  },
  'cropcopy-title-crop': {
    hint: 'Cropping is a common feature in image editing.',
    original: 'Crop',
    value: '裁剪'
  },
  'cropcopy-click-hold': {
    original: 'Right-click or press hold to copy.',
    value: '點擊右鍵或長按進行複制。'
  },
  'cropcopy-btn-copy': {
    hint: 'confirmation button. Should be short.',
    original: 'To Clipboard',
    value: '至剪貼板'
  },
  'cropcopy-copied': {
    original: 'Copied.',
    value: '已複製'
  },
  'cropcopy-btn-crop': {
    hint: 'confirmation button. Should be short.',
    original: 'Apply Crop',
    value: '應用裁剪'
  },
  'crop-drag-to-crop': {
    hint: 'drag the mouse to adjust cropping',
    original: 'Drag to crop',
    value: '拖動裁剪'
  },
  'filter-crop-extend': {
    original: 'Crop/Extend',
    value: '畫布大小'
  },
  'filter-flip': {
    original: 'Flip',
    value: '翻轉'
  },
  'filter-perspective': {
    hint: 'perspective transformation',
    original: 'Perspective',
    value: '透視'
  },
  'filter-resize': {
    original: 'Resize',
    value: '圖像大小'
  },
  'filter-rotate': {
    original: 'Rotate',
    value: '旋轉'
  },
  'filter-transform': {
    hint: 'more generic transformation. Commonly known as "free transform"',
    original: 'Transform',
    value: '自由變換'
  },
  'filter-bright-contrast': {
    hint: 'brightness contrast',
    original: 'Bright/Contrast',
    value: '亮度/對比'
  },
  'filter-curves': {
    hint: 'color curves',
    original: 'Curves',
    value: '曲線'
  },
  'filter-hue-sat': {
    original: 'Hue/Saturation',
    value: '色相/飽和度'
  },
  'filter-invert': {
    hint: 'invert the colors',
    original: 'Invert',
    value: '反相'
  },
  'filter-tilt-shift': {
    hint: 'or just depth-of-field. https://en.wikipedia.org/wiki/Tilt%E2%80%93shift_photography',
    original: 'Tilt Shift',
    value: '移軸'
  },
  'filter-to-alpha': {
    hint: 'feature that can isolate line drawings if it is a black and white image.',
    original: 'To Alpha',
    value: '轉換至Alpha'
  },
  'filter-triangle-blur': {
    hint: 'or just blur',
    original: 'Triangle Blur',
    value: '模糊'
  },
  'filter-unsharp-mask': {
    hint: 'or just sharpen, https://en.wikipedia.org/wiki/Unsharp_masking',
    original: 'Unsharp Mask',
    value: 'USM 銳化'
  },
  'filter-crop-title': {
    hint: 'same as the button, but can be longer',
    original: 'Crop / Extend',
    value: '畫布大小'
  },
  'filter-crop-description': {
    hint: 'descriptions explain the "filter" a tiny bit. Most importantly it says if it’s applied to a single layer, or the entire image (all layer)',
    original: 'Crop or extend the image.',
    value: '裁剪圖片'
  },
  'filter-crop-left': {
    original: 'Left',
    value: '左'
  },
  'filter-crop-right': {
    original: 'Right',
    value: '右'
  },
  'filter-crop-top': {
    original: 'Top',
    value: '上'
  },
  'filter-crop-bottom': {
    original: 'Bottom',
    value: '下'
  },
  'filter-crop-rule-thirds': {
    hint: 'https://en.wikipedia.org/wiki/Rule_of_thirds',
    original: 'Rule of Thirds',
    value: '三分法'
  },
  'filter-crop-fill': {
    hint: 'If image gets extended, what color to fill that new area with',
    original: 'Fill',
    value: '填充'
  },
  'filter-flip-title': {
    hint: 'same as the button, but can be longer',
    original: 'Flip',
    value: '翻轉'
  },
  'filter-flip-description': {
    original: 'Flips layer or whole image.',
    value: '翻轉圖層或整個圖像'
  },
  'filter-flip-horizontal': {
    original: 'Horizontal',
    value: '水平'
  },
  'filter-flip-vertical': {
    original: 'Vertical',
    value: '垂直'
  },
  'filter-flip-image': {
    original: 'Flip Image',
    value: '翻轉圖像'
  },
  'filter-flip-layer': {
    original: 'Flip Layer',
    value: '翻轉圖層'
  },
  'filter-perspective-title': {
    hint: 'same as the button, but can be longer',
    original: 'Perspective',
    value: '透視'
  },
  'filter-perspective-description': {
    original: 'Transforms the selected layer.',
    value: '變換已選圖層'
  },
  'filter-resize-title': {
    hint: 'same as the button, but can be longer',
    original: 'Resize',
    value: '圖像大小'
  },
  'filter-resize-description': {
    original: 'Resizes the image.',
    value: '更改圖像大小'
  },
  'filter-rotate-title': {
    hint: 'same as the button, but can be longer',
    original: 'Rotate',
    value: '旋轉'
  },
  'filter-rotate-description': {
    original: 'Rotates the image.',
    value: '旋轉圖像'
  },
  'filter-transform-empty': {
    hint: 'nothing on the layer, just transparency',
    original: 'Layer is empty.',
    value: '圖層為空'
  },
  'filter-transform-title': {
    hint: 'same as the button, but can be longer',
    original: 'Transform',
    value: '變換'
  },
  'filter-transform-description': {
    hint: 'as in the Shift key.',
    original: 'Transforms selected layer. Hold Shift for additional behavior.',
    value: '變換已選圖層。按住Shift操作。 '
  },
  'filter-transform-rotation': {
    hint: 'or Angle',
    original: 'Rotation',
    value: '旋轉'
  },
  'filter-transform-flip': {
    original: 'Flip',
    value: '翻轉'
  },
  'filter-transform-center': {
    hint: 'verb or noun',
    original: 'Center',
    value: '居中'
  },
  'filter-transform-constrain': {
    hint: 'short version of "constrain proportions" – feature that locks the aspect ratio',
    original: 'Constrain',
    value: '保持'
  },
  'filter-transform-snap': {
    hint: 'or snapping. Common feature in graphics software. Makes stuff snap into place so it’s easier to align elements in an image',
    original: 'Snap',
    value: '對齊'
  },
  'filter-transform-snap-title': {
    hint: 'same as above, but as a tooltip',
    original: 'Snap Rotation And Position',
    value: '旋轉對齊/水平對齊'
  },
  'filter-bright-contrast-title': {
    hint: 'same as the button, but can be longer',
    original: 'Brightness / Contrast',
    value: '亮度/對比'
  },
  'filter-bright-contrast-description': {
    original: 'Change brightness and contrast for the selected layer.',
    value: '更改已選圖層的亮度和對比度。 '
  },
  'filter-bright-contrast-brightness': {
    original: 'Brightness',
    value: '亮度'
  },
  'filter-bright-contrast-contrast': {
    original: 'Contrast',
    value: '對比'
  },
  'filter-curves-title': {
    hint: 'same as the button, but can be longer',
    original: 'Curves',
    value: '曲線'
  },
  'filter-curves-description': {
    original: 'Apply curves on the selected layer.',
    value: '應用曲線到已選圖層。 '
  },
  'filter-curves-all': {
    original: 'All',
    value: '全部'
  },
  'filter-hue-sat-title': {
    hint: 'same as the button, but can be longer',
    original: 'Hue / Saturation',
    value: '色相/飽和度'
  },
  'filter-hue-sat-description': {
    original: 'Change hue and saturation for the selected layer.',
    value: '更改已選圖層的色相和飽和度。 '
  },
  'filter-hue-sat-hue': {
    original: 'Hue',
    value: '色相'
  },
  'filter-hue-sat-saturation': {
    original: 'Saturation',
    value: '飽和度'
  },
  'filter-applied': {
    hint: 'used like this: „Invert“ applied. Informs that a filter was applied',
    original: 'applied',
    value: '應用'
  },
  'filter-tilt-shift-title': {
    hint: 'same as the button, but can be longer',
    original: 'Tilt Shift',
    value: '移軸'
  },
  'filter-tilt-shift-description': {
    original: 'Applies tilt shift on the selected layer.',
    value: '應用移軸到已選圖層。 '
  },
  'filter-tilt-shift-blur': {
    hint: 'words can be individually translated',
    original: 'Blur Radius',
    value: '模糊半徑'
  },
  'filter-tilt-shift-gradient': {
    hint: 'words can be individually translated',
    original: 'Gradient Radius',
    value: '梯度半徑'
  },
  'filter-to-alpha-title': {
    hint: 'same as the button, but can be longer',
    original: 'To Alpha',
    value: '至Alpha'
  },
  'filter-to-alpha-description': {
    hint: 'after ":" follow two options',
    original: 'Generates alpha channel for selected layer from:',
    value: '對已選圖層生成Alpha通道：'
  },
  'filter-to-alpha-inverted-lum': {
    hint: 'luminance is same as the layer blend mode "luminosity"',
    original: 'Inverted Luminance',
    value: '亮度翻轉'
  },
  'filter-to-alpha-lum': {
    original: 'Luminance',
    value: '亮度'
  },
  'filter-to-alpha-replace': {
    hint: 'What color to replace the rgb channels with',
    original: 'Replace RGB',
    value: '替換RGB'
  },
  'filter-triangle-blur-title': {
    hint: 'same as the button, but can be longer',
    original: 'Triangle Blur',
    value: '模糊'
  },
  'filter-triangle-blur-description': {
    original: 'Applies triangle blur on the selected layer.',
    value: '應用模糊到已選圖層。 '
  },
  'filter-unsharp-mask-title': {
    hint: 'same as the button, but can be longer',
    original: 'Unsharp Mask',
    value: 'USM銳化'
  },
  'filter-unsharp-mask-description': {
    original: 'Sharpens the selected layer by scaling pixels away from the average of their neighbors.',
    value: '根據附近像素的均值應用銳化到已選圖層。 '
  },
  'filter-unsharp-mask-strength': {
    original: 'Strength',
    value: '強度'
  },
  'filter-grid': {
    original: 'Grid',
    value: '網格'
  },
  'filter-grid-description': {
    original: 'Draws grid on selected layer.',
    value: '給當前層添加網格線。'
  },
  'filter-noise': {
    original: 'Noise',
    value: '噪聲'
  },
  'filter-noise-description': {
    original: 'Adds noise to selected layer.',
    value: '應用噪聲到達選擇層。'
  },
  'filter-noise-scale': {
    original: 'Scale',
    value: '圖案大小'
  },
  'filter-noise-alpha': {
    original: 'Alpha',
    value: 'Alpha'
  },
  'filter-pattern': {
    original: 'Pattern',
    value: '圖案'
  },
  'filter-pattern-description': {
    original: 'Generates pattern on selected layer. Drag the preview for further controls.',
    value: '在選定圖層上生成圖案。 拖動預覽以獲得更多控制。'
  },
  'filter-distort': {
    original: 'Distort',
    value: '扭曲'
  },
  'filter-distort-description': {
    original: 'Distorts the selected layer.',
    value: '扭曲選定的圖層。'
  },
  'filter-distort-phase': {
    original: 'Phase',
    value: '階段'
  },
  'filter-distort-stepsize': {
    original: 'Step Size',
    value: '步長'
  },
  'filter-distort-sync-xy': {
    original: 'Sync XY',
    value: '同步 XY'
  },
  'filter-vanish-point': {
    original: 'Vanish Point',
    value: '消失點'
  },
  'filter-vanish-point-title': {
    original: 'Vanishing Point',
    value: '消失點'
  },
  'filter-vanish-point-description': {
    original: 'Adds vanishing point to selected layer. Drag preview to move.',
    value: '添加消失點至所選圖層。'
  },
  'filter-vanish-point-lines': {
    hint: 'As in: the number of lines',
    original: 'Lines',
    value: '線條數量'
  },
  'import-opening': {
    hint: 'in the process of opening a large file',
    original: 'Opening file...',
    value: '打開文件...'
  },
  'import-title': {
    original: 'Import Image',
    value: '導入圖像'
  },
  'import-too-large': {
    original: 'Image too large, will be downscaled.',
    value: '圖像過大，將會進行縮小。 '
  },
  'import-btn-as-layer': {
    hint: 'import as layer. Button label, should be short',
    original: 'As Layer',
    value: '作為圖層'
  },
  'import-btn-as-image': {
    hint: 'import as image. Button label, should be short',
    original: 'As Image',
    value: '作為圖像'
  },
  'import-as-layer-title': {
    original: 'Import Image as New Layer',
    value: '導入圖像作為新圖層'
  },
  'import-as-layer-description': {
    original: 'Adjust the position of the imported image.',
    value: '調整導入圖像的位置。 '
  },
  'import-as-layer-limit-reached': {
    hint: 'Kleki allows only a certain number of layers',
    original: 'Layer limit reached. Image will be placed on existing layer.',
    value: '達到圖層數量上限。圖像將會應用到已有圖層。'
  },
  'import-as-layer-fit': {
    hint: 'verb. imported image made to fit inside canvas.',
    original: 'Fit',
    value: '適應'
  },
  'import-flatten': {
    hint: 'Turn image that has multiple layers into an image that has only one layer.',
    original: 'Flatten image',
    value: '拼合圖像'
  },
  'import-unsupported-file': {
    hint: 'e.g. user tried to open a WAV',
    original: 'Unsupported file type. See Help for supported types.',
    value: '不支持此類文件類型。查看幫助。 '
  },
  'import-broken-file': {
    hint: 'if an image file is broken',
    original: "Couldn't load image. File might be corrupted.",
    value: '不能加載圖像。文件可能受損。 '
  },
  'import-psd-unsupported': {
    hint: 'PSD is photoshop file format. flatten as in „flatten image“ in photoshop. Merged all layers',
    original: 'Unsupported features. PSD had to be flattened.',
    value: '不支持該功能。 PSD需要拼合。 '
  },
  'import-psd-limited-support': {
    hint: 'PSD has lots of fancy features that Kleki doesn\'t have, so when importing a PSD it often has to simplify things. So it might look different compared to when opening in Photoshop. We’re informing the user if they "flatten" the image it will be accurate despite losing layers. The user has to weigh, do they care about having layers, or do they care about it looking right.',
    original: 'PSD support is limited. Flattened will more likely look correct.',
    value: 'PSD支持受限。拼合功能將會更近原始圖像。 '
  },
  'import-psd-too-large': {
    original: 'Image exceeds maximum dimensions of {x} x {x} pixels. Unable to import.',
    value: '圖像超過{x} x {x}個像素。不能導入。 '
  },
  'import-psd-size': {
    original: 'Image size',
    value: '圖像大小'
  },
  'hand-reset': {
    hint: 'resets zoom (to 100%) and rotation (to 0°)',
    original: 'Reset',
    value: '重置'
  },
  'hand-fit': {
    hint: 'makes canvas cover whole working area',
    original: 'Fit',
    value: '適應'
  },
  'hand-inertia-scrolling': {
    hint: 'Canvas will continue to move even after releasing pointer.',
    original: 'Inertia Scrolling',
    value: ''
  },
  'bucket-tolerance': {
    hint: 'a common feature for paint bucket',
    original: 'Tolerance',
    value: '容差'
  },
  'bucket-sample': {
    hint: 'like "taking a sample"',
    original: 'Sample',
    value: '採樣'
  },
  'bucket-sample-title': {
    original: 'Which layers to sample color from',
    value: '從特定圖層進行色彩採樣'
  },
  'bucket-sample-all': {
    hint: 'all layers',
    original: 'All',
    value: '全部'
  },
  'bucket-sample-active': {
    hint: 'currently active layer',
    original: 'Active',
    value: '當前'
  },
  'bucket-sample-above': {
    hint: 'layer above active layer',
    original: 'Above',
    value: '之前'
  },
  'bucket-grow': {
    hint: 'verb. Or expand.',
    original: 'Grow',
    value: '填充擴張'
  },
  'bucket-grow-title': {
    original: 'Grow filled area (in pixels)',
    value: '擴張填充的區域（像素）'
  },
  'bucket-contiguous': {
    hint: 'common setting for paint bucket in image editing software',
    original: 'Contiguous',
    value: '連續的'
  },
  'bucket-contiguous-title': {
    hint: 'tooltip explaining Contiguous',
    original: 'Only fill connected areas',
    value: '僅填充相連的區域'
  },
  'gradient-linear': {
    original: 'Linear',
    value: '線性'
  },
  'gradient-linear-mirror': {
    original: 'Linear-Mirror',
    value: '反射性'
  },
  'gradient-radial': {
    original: 'Radial',
    value: '放射狀'
  },
  'shape-stroke': {
    original: 'Stroke',
    value: '線條'
  },
  'shape-fill': {
    original: 'Fill',
    value: '填充'
  },
  'shape-rect': {
    hint: 'rectangle shape',
    original: 'Rectangle',
    value: '方形'
  },
  'shape-ellipse': {
    hint: 'ellipse shape',
    original: 'Ellipse',
    value: '圓形'
  },
  'shape-line': {
    original: 'Line',
    value: '線條'
  },
  'shape-line-width': {
    original: 'Line Width',
    value: '線條寬度'
  },
  'shape-outwards': {
    hint: 'grows shape from the center outwards',
    original: 'Outwards',
    value: '自中心向外'
  },
  'shape-fixed': {
    hint: 'fixed ratio of 1:1',
    original: 'Fixed 1:1',
    value: '鎖定 1:1'
  },
  'text-instruction': {
    original: 'Click canvas to place text',
    value: '點擊畫布添加文本'
  },
  'text-title': {
    original: 'Add Text',
    value: '添加文本'
  },
  'text-text': {
    original: 'Text',
    value: ''
  },
  'text-font': {
    original: 'Font',
    value: ''
  },
  'text-placeholder': {
    hint: 'placeholder in text input.',
    original: 'Your text',
    value: '請輸入'
  },
  'text-color': {
    original: 'Color',
    value: '色彩'
  },
  'text-size': {
    original: 'Size',
    value: '大小'
  },
  'text-line-height': {
    original: 'Line Height',
    value: ''
  },
  'text-letter-spacing': {
    original: 'Letter Spacing',
    value: ''
  },
  'text-left': {
    hint: 'left, center, right text formatting like in Word',
    original: 'Left',
    value: '左對齊'
  },
  'text-center': {
    original: 'Center',
    value: '居中'
  },
  'text-right': {
    original: 'Right',
    value: '右對齊'
  },
  'text-italic': {
    original: 'Italic',
    value: '斜體'
  },
  'text-bold': {
    original: 'Bold',
    value: '加粗'
  },
  'select-select': {
    original: 'Select',
    value: ''
  },
  'select-transform': {
    original: 'Transform',
    value: ''
  },
  'select-lasso': {
    original: 'Lasso',
    value: ''
  },
  'select-polygon': {
    original: 'Polygon',
    value: ''
  },
  'select-boolean-replace': {
    original: 'Replace',
    value: ''
  },
  'select-boolean-add': {
    original: 'Add',
    value: ''
  },
  'select-boolean-subtract': {
    original: 'Subtract',
    value: ''
  },
  'select-all': {
    original: 'All',
    value: ''
  },
  'select-invert': {
    original: 'Invert',
    value: ''
  },
  'select-reset': {
    original: 'Reset',
    value: ''
  },
  'select-fill': {
    original: 'Fill',
    value: ''
  },
  'select-erase': {
    original: 'Erase',
    value: ''
  },
  'select-transform-clone': {
    original: 'Clone',
    value: ''
  },
  'select-transform-clone-applied': {
    original: 'Cloned',
    value: ''
  },
  'select-transform-move-to-layer': {
    original: 'Move to layer:',
    value: ''
  },
  'select-transform-applied': {
    original: 'Transformation applied',
    value: ''
  },
  'select-transform-empty': {
    original: 'Selected area on active layer is empty.',
    value: ''
  },
  'save-reminder-title': {
    hint: 'Save Reminder is a popup that shows up after 20 minutes of not saving. Allows user to save on the spot.',
    original: 'Unsaved Work',
    value: '作品未保存'
  },
  'save-reminder-text': {
    hint: 'turns into: "...saved in <strong>12 minutes</strong>. Save..."',
    original: 'Image was not saved in {a} minutes{b}. Save now to prevent eventual loss.',
    value: '圖畫已{a}分鐘{b}未保存。立刻保存以避免進度丟失。'
  },
  'save-reminder-save-psd': {
    original: 'Save As PSD',
    value: '保存為PSD'
  },
  'save-reminder-psd-layers': {
    original: 'PSD will remember all layers.',
    value: 'PSD可保留所有圖層。'
  },
  'backup-drawing': {
    hint: '(Embed) If the upload failed, the user can backup/download their drawing.',
    original: 'You can backup your drawing.',
    value: '您可以備份繪圖。'
  },
  submit: {
    original: 'Submit',
    value: '送出'
  },
  'submit-title': {
    hint: 'tooltip when hovering "Submit" button',
    original: 'Submit Drawing',
    value: '送出圖像'
  },
  'submit-prompt': {
    hint: 'Dialog asking for confirmation',
    original: 'Submit drawing?',
    value: '確認送出？'
  },
  'submit-submitting': {
    hint: 'in the process of uploading',
    original: 'Submitting',
    value: '送出中'
  },
  'embed-init-loading': {
    hint: 'when opening the app',
    original: 'Loading app',
    value: '加載app'
  },
  'embed-init-waiting': {
    hint: 'The app is loaded, but it’s still waiting for image',
    original: 'Waiting for image',
    value: '圖像上傳中'
  },
  unsaved: {
    original: 'Unsaved',
    value: '未保存'
  },
  help: {
    hint: 'or user manual',
    original: 'Help',
    value: '幫助'
  },
  'tab-settings': {
    original: 'Settings',
    value: '設置'
  },
  'settings-language': {
    original: 'Language',
    value: '語言'
  },
  'settings-language-reload': {
    hint: 'changes will be visible after reloading the page',
    original: 'Will update after reloading.',
    value: '刷新後更改語言'
  },
  'settings-theme': {
    original: 'Theme',
    value: '外觀主題'
  },
  'settings-save-reminder-label': {
    original: 'Save Reminder',
    value: ''
  },
  'settings-save-reminder-disabled': {
    original: 'disabled',
    value: ''
  },
  'settings-save-reminder-confirm-title': {
    original: 'Turn off Save Reminder?',
    value: ''
  },
  'settings-save-reminder-confirm-a': {
    original: "There is no autosave and browser tabs don't last forever. If you don't periodically save you will likely lose work.",
    value: ''
  },
  'settings-save-reminder-confirm-b': {
    original: 'Disable at your own risk?',
    value: ''
  },
  'settings-save-reminder-confirm-disable': {
    original: 'Disable',
    value: ''
  },
  'theme-dark': {
    original: 'Dark',
    value: '深色'
  },
  'theme-light': {
    original: 'Light',
    value: '淺色'
  },
  'terms-of-service': {
    original: 'Terms of Service',
    value: '服務條款'
  },
  licenses: {
    hint: 'as in software licenses',
    original: 'Licenses',
    value: '許可'
  },
  'source-code': {
    original: 'Source Code',
    value: '源代碼'
  },
  auto: {
    hint: 'automatically decided',
    original: 'auto',
    value: '自動'
  },
  'zoom-in': {
    original: 'Zoom In',
    value: '放大'
  },
  'zoom-out': {
    original: 'Zoom Out',
    value: '縮小'
  },
  radius: {
    original: 'Radius',
    value: '半徑'
  },
  'constrain-proportions': {
    original: 'Constrain Proportions',
    value: '保持縱橫比'
  },
  width: {
    original: 'Width',
    value: '寬度'
  },
  height: {
    original: 'Height',
    value: '高度'
  },
  opacity: {
    hint: 'for layer opacity, brush opacity, text opacity, ...',
    original: 'Opacity',
    value: '不透明度'
  },
  red: {
    original: 'Red',
    value: '紅'
  },
  green: {
    original: 'Green',
    value: '綠'
  },
  blue: {
    original: 'Blue',
    value: '藍'
  },
  eraser: {
    hint: 'noun. As short as possible',
    original: 'Eraser',
    value: '橡皮擦'
  },
  center: {
    hint: 'verb',
    original: 'Center',
    value: '居中'
  },
  layers: {
    hint: 'layers in the context of image editing software',
    original: 'Layers',
    value: '圖層'
  },
  background: {
    hint: 'as in background layer',
    original: 'Background',
    value: '背景'
  },
  'scaling-algorithm': {
    hint: 'Label for dropdown where user chooses which computer graphics algorithm should be used to scale the image',
    original: 'Scaling Algorithm',
    value: '縮放算法'
  },
  'algorithm-smooth': {
    original: 'Smooth',
    value: '平滑'
  },
  'algorithm-pixelated': {
    original: 'Pixelated',
    value: '像素化'
  },
  preview: {
    hint: 'subject. When applying a filter user sees a preview of what the result would look like.',
    original: 'Preview',
    value: '預覽'
  },
  'angle-snap': {
    hint: 'Snapping. Like when transforming an image and it snaps to the side of the frame.',
    original: 'Snap',
    value: '對齊'
  },
  'angle-snap-title': {
    original: '45° Angle Snapping',
    value: '以45°角度對齊'
  },
  'lock-alpha': {
    hint: 'Short. Freezes the alpha channel of image. Images consist of 4 channels (red, green, blue, alpha). Alpha lock also found in Procreate and photoshop',
    original: 'Lock Alpha',
    value: '鎖定'
  },
  'lock-alpha-title': {
    hint: "same as 'lock-alpha' but longer version. Shows up as a tooltip when hovering",
    original: "Locks layer's alpha channel",
    value: '鎖定圖層透明度'
  },
  reverse: {
    hint: 'Reversing the direction of a color gradient',
    original: 'Reverse',
    value: '反向'
  },
  'compare-before': {
    original: 'Before',
    value: '之前'
  },
  'compare-after': {
    original: 'After',
    value: '之後'
  },
  loading: {
    original: 'Loading',
    value: ''
  },
  more: {
    original: 'More',
    value: ''
  },
  'x-minutes': {
    original: '{x}min',
    value: '{x}分鐘'
  },
  wip: {
    original: 'Work in progress',
    value: ''
  },
  'browser-zoom-help': {
    original: 'Double-tap or pinch-out to reset browser zoom.',
    value: ''
  },
  dismiss: {
    original: 'Dismiss',
    value: ''
  }
}
