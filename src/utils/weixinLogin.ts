export interface IjssdkLoginParams {
  /**
   * 微信公众号appid
   */
  appid: string
  /**
   * 登录后重定向的url
   */
  redirect?: string
}

/**
 * 微信登录(公众号)
 */
export default ({ appid = '', redirect = location.href }: IjssdkLoginParams) => {
  const goUrl = 'https://open.weixin.qq.com/connect/oauth2/authorize'
  const redirect_uri = encodeURIComponent(redirect)
  location.replace(`${goUrl}?appid=${appid}&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`)
}
