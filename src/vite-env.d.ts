/// <reference types="vite/client" />

interface Window {
  refreshRem(): void
  AWSC: any
  nc: any
  electronAPI: any
  WebViewJavascriptBridge: any
}

interface Array<T> {
  findLastIndex(predicate: (value: T, index: number, obj: T[]) => unknown, thisArg?: any): number
}

// //
// declare module 'fabric-with-erasing' {
//   import * as fabric from 'fabric'

//   // 扩展 fabric 模块
//   export namespace fabric {
//     export const EraserBrush: fabric.BaseBrush
//   }

//   // 导出所有 fabric.js 的内容
//   export = fabric
// }
