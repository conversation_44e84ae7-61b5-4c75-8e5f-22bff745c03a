<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书法笔刷撤销测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .control-group {
            margin-bottom: 10px;
        }
        label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
        }
        input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 4px;
            cursor: crosshair;
        }
        .info {
            margin-top: 15px;
            padding: 10px;
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>书法笔刷撤销测试</h1>
        
        <div class="controls">
            <h3>笔峰效果参数</h3>
            <div class="control-group">
                <label>速度影响:</label>
                <input type="range" id="velocityInfluence" min="0" max="1" step="0.1" value="0.5">
                <span id="velocityValue">0.5</span>
            </div>
            <div class="control-group">
                <label>压力影响:</label>
                <input type="range" id="pressureInfluence" min="0" max="1" step="0.1" value="0.7">
                <span id="pressureValue">0.7</span>
            </div>
            <div class="control-group">
                <label>速度平滑:</label>
                <input type="range" id="velocitySmoothing" min="0" max="1" step="0.1" value="0.3">
                <span id="smoothingValue">0.3</span>
            </div>
            
            <div style="margin-top: 15px;">
                <button class="btn-primary" onclick="setCalligraphyMode()">启用书法笔锋</button>
                <button class="btn-secondary" onclick="drawTestStroke()">绘制测试笔画</button>
                <button class="btn-danger" onclick="testUndo()">测试撤销</button>
                <button class="btn-secondary" onclick="clearCanvas()">清空画布</button>
            </div>
        </div>

        <canvas id="testCanvas" width="600" height="400"></canvas>
        
        <div class="info">
            <h4>测试说明：</h4>
            <ol>
                <li>点击"启用书法笔锋"按钮设置笔刷为书法模式</li>
                <li>调整上方的笔峰参数滑块</li>
                <li>点击"绘制测试笔画"或在画布上手动绘制</li>
                <li>点击"测试撤销"按钮</li>
                <li>再次绘制，检查笔峰效果是否保持</li>
            </ol>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        // 模拟书法笔刷测试
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');
        
        // 模拟笔刷参数
        let calligraphyParams = {
            velocityInfluence: 0.5,
            pressureInfluence: 0.7,
            velocitySmoothing: 0.3
        };
        
        let isCalligraphyMode = false;
        let history = [];
        
        // 更新参数显示
        function updateParamDisplay() {
            document.getElementById('velocityValue').textContent = calligraphyParams.velocityInfluence;
            document.getElementById('pressureValue').textContent = calligraphyParams.pressureInfluence;
            document.getElementById('smoothingValue').textContent = calligraphyParams.velocitySmoothing;
        }
        
        // 参数滑块事件
        document.getElementById('velocityInfluence').addEventListener('input', function(e) {
            calligraphyParams.velocityInfluence = parseFloat(e.target.value);
            updateParamDisplay();
            showStatus('速度影响参数已更新: ' + e.target.value, 'success');
        });
        
        document.getElementById('pressureInfluence').addEventListener('input', function(e) {
            calligraphyParams.pressureInfluence = parseFloat(e.target.value);
            updateParamDisplay();
            showStatus('压力影响参数已更新: ' + e.target.value, 'success');
        });
        
        document.getElementById('velocitySmoothing').addEventListener('input', function(e) {
            calligraphyParams.velocitySmoothing = parseFloat(e.target.value);
            updateParamDisplay();
            showStatus('速度平滑参数已更新: ' + e.target.value, 'success');
        });
        
        function setCalligraphyMode() {
            isCalligraphyMode = true;
            showStatus('书法笔锋模式已启用', 'success');
        }
        
        function drawTestStroke() {
            if (!isCalligraphyMode) {
                showStatus('请先启用书法笔锋模式', 'error');
                return;
            }
            
            // 保存当前状态到历史
            history.push({
                imageData: ctx.getImageData(0, 0, canvas.width, canvas.height),
                params: {...calligraphyParams}
            });
            
            // 模拟书法笔画（使用当前参数）
            ctx.save();
            ctx.strokeStyle = '#000';
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // 根据参数调整线条效果
            const baseWidth = 10;
            const velocityEffect = calligraphyParams.velocityInfluence * 5;
            const pressureEffect = calligraphyParams.pressureInfluence * 8;
            
            ctx.beginPath();
            ctx.lineWidth = baseWidth + velocityEffect + pressureEffect;
            ctx.moveTo(100, 200);
            ctx.quadraticCurveTo(300, 100, 500, 200);
            ctx.stroke();
            
            // 添加笔峰效果
            ctx.lineWidth = Math.max(1, (baseWidth + velocityEffect + pressureEffect) * 0.3);
            ctx.beginPath();
            ctx.moveTo(500, 200);
            ctx.lineTo(520, 210);
            ctx.stroke();
            
            ctx.restore();
            
            showStatus(`测试笔画已绘制 (速度:${calligraphyParams.velocityInfluence}, 压力:${calligraphyParams.pressureInfluence}, 平滑:${calligraphyParams.velocitySmoothing})`, 'success');
        }
        
        function testUndo() {
            if (history.length === 0) {
                showStatus('没有可撤销的操作', 'error');
                return;
            }
            
            const lastState = history.pop();
            
            // 模拟撤销：恢复画布状态
            ctx.putImageData(lastState.imageData, 0, 0);
            
            // 关键测试：检查参数是否被保持
            const paramsChanged = 
                calligraphyParams.velocityInfluence !== lastState.params.velocityInfluence ||
                calligraphyParams.pressureInfluence !== lastState.params.pressureInfluence ||
                calligraphyParams.velocitySmoothing !== lastState.params.velocitySmoothing;
            
            if (paramsChanged) {
                showStatus('❌ 测试失败：撤销后笔峰参数丢失！', 'error');
                // 恢复参数（模拟修复后的行为）
                calligraphyParams = {...lastState.params};
                updateSliders();
            } else {
                showStatus('✅ 测试成功：撤销后笔峰参数保持正常！', 'success');
            }
        }
        
        function updateSliders() {
            document.getElementById('velocityInfluence').value = calligraphyParams.velocityInfluence;
            document.getElementById('pressureInfluence').value = calligraphyParams.pressureInfluence;
            document.getElementById('velocitySmoothing').value = calligraphyParams.velocitySmoothing;
            updateParamDisplay();
        }
        
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            history = [];
            showStatus('画布已清空', 'success');
        }
        
        function showStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = 'status ' + type;
            statusEl.style.display = 'block';
            
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }
        
        // 初始化
        updateParamDisplay();
        
        // 简单的绘制功能
        let isDrawing = false;
        canvas.addEventListener('mousedown', function(e) {
            if (!isCalligraphyMode) return;
            isDrawing = true;
            
            // 保存状态
            history.push({
                imageData: ctx.getImageData(0, 0, canvas.width, canvas.height),
                params: {...calligraphyParams}
            });
        });
        
        canvas.addEventListener('mousemove', function(e) {
            if (!isDrawing || !isCalligraphyMode) return;
            
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            ctx.lineTo(x, y);
            ctx.stroke();
        });
        
        canvas.addEventListener('mouseup', function() {
            isDrawing = false;
            ctx.beginPath();
        });
    </script>
</body>
</html>
