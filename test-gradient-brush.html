<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渐变画笔测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .control-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
            font-size: 14px;
        }
        input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }
        select {
            width: 150px;
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ccc;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 4px;
            cursor: crosshair;
            background: white;
        }
        .info {
            margin-top: 15px;
            padding: 10px;
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .color-preview {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin: 2px;
            border: 2px solid #333;
        }
        .gradient-modes {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .mode-preview {
            width: 100px;
            height: 20px;
            border-radius: 10px;
            cursor: pointer;
            border: 2px solid transparent;
        }
        .mode-preview.active {
            border-color: #007bff;
        }
        .rainbow { background: linear-gradient(to right, red, orange, yellow, green, blue, purple); }
        .warm { background: linear-gradient(to right, #ff0000, #ff4500, #ff8c00, #ffd700, #ffff00); }
        .cool { background: linear-gradient(to right, #00ffff, #00bfff, #0000ff, #4b0082, #800080); }
        .custom { background: linear-gradient(to right, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #dda0dd); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 渐变画笔测试</h1>
        
        <div class="controls">
            <h3>渐变效果参数</h3>
            
            <div class="control-group">
                <label>渐变模式:</label>
                <select id="gradientMode">
                    <option value="rainbow">彩虹渐变</option>
                    <option value="warm">暖色调</option>
                    <option value="cool">冷色调</option>
                    <option value="custom">自定义</option>
                </select>
            </div>
            
            <div class="gradient-modes">
                <div class="mode-preview rainbow active" data-mode="rainbow" title="彩虹渐变"></div>
                <div class="mode-preview warm" data-mode="warm" title="暖色调"></div>
                <div class="mode-preview cool" data-mode="cool" title="冷色调"></div>
                <div class="mode-preview custom" data-mode="custom" title="自定义"></div>
            </div>
            
            <div class="control-group">
                <label>颜色变化速度:</label>
                <input type="range" id="colorChangeSpeed" min="0" max="1" step="0.1" value="0.5">
                <span id="speedValue">50%</span>
            </div>
            
            <div class="control-group">
                <label>颜色随机性:</label>
                <input type="range" id="colorRandomness" min="0" max="1" step="0.1" value="0.3">
                <span id="randomnessValue">30%</span>
            </div>
            
            <div class="control-group">
                <label>渐变平滑度:</label>
                <input type="range" id="gradientSmoothing" min="0" max="1" step="0.1" value="0.7">
                <span id="smoothingValue">70%</span>
            </div>
            
            <div class="control-group">
                <label>画笔大小:</label>
                <input type="range" id="brushSize" min="1" max="50" step="1" value="20">
                <span id="sizeValue">20px</span>
            </div>
            
            <div style="margin-top: 15px;">
                <button class="btn-primary" onclick="startDrawing()">开始绘制</button>
                <button class="btn-secondary" onclick="clearCanvas()">清空画布</button>
                <button class="btn-success" onclick="showColorPreview()">预览颜色</button>
            </div>
        </div>

        <canvas id="testCanvas" width="800" height="500"></canvas>
        
        <div class="info">
            <h4>渐变画笔特性：</h4>
            <ul>
                <li><strong>彩虹渐变</strong>：经典的红橙黄绿蓝紫渐变</li>
                <li><strong>暖色调</strong>：红、橙、黄等温暖色彩</li>
                <li><strong>冷色调</strong>：青、蓝、紫等清凉色彩</li>
                <li><strong>自定义</strong>：可配置的颜色组合</li>
                <li><strong>动态变化</strong>：绘制时颜色自动变化</li>
                <li><strong>随机效果</strong>：增加颜色变化的自然感</li>
            </ul>
        </div>
        
        <div id="colorPreview" style="margin-top: 15px;"></div>
    </div>

    <script>
        // 模拟渐变画笔测试
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');
        
        // 渐变参数
        let gradientParams = {
            mode: 'rainbow',
            colorChangeSpeed: 0.5,
            colorRandomness: 0.3,
            gradientSmoothing: 0.7,
            brushSize: 20
        };
        
        let isDrawing = false;
        let colorProgress = 0;
        let lastPoint = null;
        
        // 颜色调色板（优化过渡更自然）
        const colorPalettes = {
            rainbow: [
                { r: 255, g: 0, b: 0 },     // 红
                { r: 255, g: 127, b: 0 },   // 橙红
                { r: 255, g: 200, b: 0 },   // 橙黄
                { r: 200, g: 255, b: 0 },   // 黄绿
                { r: 0, g: 255, b: 100 },   // 绿
                { r: 0, g: 200, b: 255 },   // 青绿
                { r: 0, g: 100, b: 255 },   // 蓝
                { r: 100, g: 0, b: 255 },   // 蓝紫
                { r: 200, g: 0, b: 200 },   // 紫
                { r: 255, g: 0, b: 100 }    // 紫红
            ],
            warm: [
                { r: 255, g: 60, b: 60 },   // 暖红
                { r: 255, g: 120, b: 40 },  // 橙红
                { r: 255, g: 180, b: 20 },  // 橙
                { r: 255, g: 220, b: 60 },  // 金橙
                { r: 255, g: 240, b: 120 }, // 暖黄
                { r: 255, g: 200, b: 80 },  // 金黄
                { r: 255, g: 140, b: 60 }   // 橙色
            ],
            cool: [
                { r: 100, g: 255, b: 255 }, // 浅青
                { r: 60, g: 220, b: 255 },  // 天蓝
                { r: 80, g: 160, b: 255 },  // 蓝
                { r: 120, g: 100, b: 255 }, // 蓝紫
                { r: 160, g: 80, b: 255 },  // 紫
                { r: 200, g: 120, b: 255 }, // 淡紫
                { r: 140, g: 180, b: 255 }  // 蓝色
            ],
            custom: [
                { r: 255, g: 107, b: 107 }, // 粉红
                { r: 255, g: 154, b: 77 },  // 橙粉
                { r: 255, g: 206, b: 84 },  // 金黄
                { r: 150, g: 206, b: 180 }, // 薄荷绿
                { r: 78, g: 205, b: 196 },  // 青绿
                { r: 69, g: 183, b: 209 },  // 天蓝
                { r: 162, g: 155, b: 254 }, // 淡紫
                { r: 221, g: 160, b: 221 }  // 紫色
            ]
        };
        
        // 平滑步进函数
        function smoothStep(t) {
            return t * t * (3 - 2 * t);
        }

        // 计算当前颜色（使用平滑插值）
        function calculateCurrentColor(progress) {
            const palette = colorPalettes[gradientParams.mode];

            // 使用平滑的随机性
            const smoothRandom = Math.sin(Date.now() * 0.001) * gradientParams.colorRandomness * 0.1;
            const adjustedProgress = Math.max(0, Math.min(1, progress + smoothRandom));

            // 使用平滑曲线
            const smoothProgress = smoothStep(adjustedProgress);

            const position = smoothProgress * (palette.length - 1);
            const index1 = Math.floor(position);
            const index2 = Math.min(index1 + 1, palette.length - 1);
            const t = position - index1;

            const color1 = palette[index1];
            const color2 = palette[index2];

            // 使用平滑插值
            const smoothT = smoothStep(t);

            return {
                r: Math.round(color1.r + (color2.r - color1.r) * smoothT),
                g: Math.round(color1.g + (color2.g - color1.g) * smoothT),
                b: Math.round(color1.b + (color2.b - color1.b) * smoothT)
            };
        }
        
        // 绘制渐变线段
        function drawGradientSegment(p1, p2) {
            const gradient = ctx.createLinearGradient(p1.x, p1.y, p2.x, p2.y);
            gradient.addColorStop(0, `rgb(${p1.color.r}, ${p1.color.g}, ${p1.color.b})`);
            gradient.addColorStop(1, `rgb(${p2.color.r}, ${p2.color.g}, ${p2.color.b})`);
            
            ctx.strokeStyle = gradient;
            ctx.lineWidth = gradientParams.brushSize;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            ctx.beginPath();
            ctx.moveTo(p1.x, p1.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.stroke();
        }
        
        // 更新参数显示
        function updateParamDisplay() {
            document.getElementById('speedValue').textContent = Math.round(gradientParams.colorChangeSpeed * 100) + '%';
            document.getElementById('randomnessValue').textContent = Math.round(gradientParams.colorRandomness * 100) + '%';
            document.getElementById('smoothingValue').textContent = Math.round(gradientParams.gradientSmoothing * 100) + '%';
            document.getElementById('sizeValue').textContent = gradientParams.brushSize + 'px';
        }
        
        // 事件监听器
        document.getElementById('gradientMode').addEventListener('change', function(e) {
            gradientParams.mode = e.target.value;
            updateModePreview();
        });
        
        document.getElementById('colorChangeSpeed').addEventListener('input', function(e) {
            gradientParams.colorChangeSpeed = parseFloat(e.target.value);
            updateParamDisplay();
        });
        
        document.getElementById('colorRandomness').addEventListener('input', function(e) {
            gradientParams.colorRandomness = parseFloat(e.target.value);
            updateParamDisplay();
        });
        
        document.getElementById('gradientSmoothing').addEventListener('input', function(e) {
            gradientParams.gradientSmoothing = parseFloat(e.target.value);
            updateParamDisplay();
        });
        
        document.getElementById('brushSize').addEventListener('input', function(e) {
            gradientParams.brushSize = parseInt(e.target.value);
            updateParamDisplay();
        });
        
        // 模式预览点击
        document.querySelectorAll('.mode-preview').forEach(preview => {
            preview.addEventListener('click', function() {
                gradientParams.mode = this.dataset.mode;
                document.getElementById('gradientMode').value = gradientParams.mode;
                updateModePreview();
            });
        });
        
        function updateModePreview() {
            document.querySelectorAll('.mode-preview').forEach(p => p.classList.remove('active'));
            document.querySelector(`[data-mode="${gradientParams.mode}"]`).classList.add('active');
        }
        
        function startDrawing() {
            alert('在画布上拖拽鼠标来绘制渐变线条！');
        }
        
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            colorProgress = 0;
        }
        
        function showColorPreview() {
            const previewDiv = document.getElementById('colorPreview');
            previewDiv.innerHTML = '<h4>当前调色板预览：</h4>';
            
            const palette = colorPalettes[gradientParams.mode];
            palette.forEach(color => {
                const colorDiv = document.createElement('div');
                colorDiv.className = 'color-preview';
                colorDiv.style.backgroundColor = `rgb(${color.r}, ${color.g}, ${color.b})`;
                colorDiv.title = `RGB(${color.r}, ${color.g}, ${color.b})`;
                previewDiv.appendChild(colorDiv);
            });
        }
        
        // 鼠标绘制事件
        canvas.addEventListener('mousedown', function(e) {
            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const color = calculateCurrentColor(colorProgress);
            lastPoint = { x, y, color };
        });
        
        canvas.addEventListener('mousemove', function(e) {
            if (!isDrawing) return;
            
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 更新颜色进度
            colorProgress += gradientParams.colorChangeSpeed * 0.01;
            if (colorProgress > 1) colorProgress = 0;
            
            const color = calculateCurrentColor(colorProgress);
            const currentPoint = { x, y, color };
            
            if (lastPoint) {
                drawGradientSegment(lastPoint, currentPoint);
            }
            
            lastPoint = currentPoint;
        });
        
        canvas.addEventListener('mouseup', function() {
            isDrawing = false;
            lastPoint = null;
        });
        
        // 初始化
        updateParamDisplay();
        showColorPreview();
    </script>
</body>
</html>
