import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import react from '@vitejs/plugin-react'
import dayjs from 'dayjs'
import packageConf from './package.json'
import { UploadPlugin } from '@msb-next/vite-plugins'
import legacy from '@vitejs/plugin-legacy'
import electron from 'vite-plugin-electron'
import renderer from 'vite-plugin-electron-renderer'

export default async ({ mode }) => {
  process.env = {
    ...process.env,
    ...loadEnv(mode, process.cwd()),
    VITE_APP_NAME: packageConf.name,
    VITE_APP_VERSION: packageConf.version,
    VITE_APP_TIME: `v.${packageConf.version}.${dayjs().format('YY.MMDD.HHmm')}`
  }
  const isBuild = process.env.VITE_APP_ENV !== 'dev'
  const isBuildEle = process.env.npm_lifecycle_event.includes('ele')
  console.log('isBuild:', isBuild)
  console.log('isBuildEle:', isBuildEle)

  let base = './'
  let sourcemap = true
  const plugins = []
  const buildReact = react({
    jsxImportSource: '@emotion/react',
    babel: {
      plugins: ['@emotion/babel-plugin']
    }
  })

  const buildElePlugin = electron([
    {
      // Main-Process entry file of the Electron App.
      entry: 'electron/main/index.ts',
      onstart(options) {
        if (process.env.VSCODE_DEBUG) {
          console.log(/* For `.vscode/.debug.script.mjs` */ '[startup] Electron App')
        } else {
          options.startup()
        }
      },
      vite: {
        build: {
          sourcemap,
          minify: isBuild,
          outDir: 'dist/main',
          rollupOptions: {
            external: Object.keys('dependencies' in packageConf ? packageConf.dependencies : {})
          }
        }
      }
    },
    {
      entry: ['electron/preload/index.ts', 'electron/preload/webview.js'],
      onstart(options) {
        // Notify the Renderer-Process to reload the page when the Preload-Scripts build is complete,
        // instead of restarting the entire Electron App.
        options.reload()
      },
      vite: {
        build: {
          sourcemap: sourcemap ? 'inline' : undefined, // #332
          minify: isBuild,
          outDir: 'dist/preload',
          rollupOptions: {
            external: Object.keys('dependencies' in packageConf ? packageConf.dependencies : {})
          }
        }
      }
    }
  ])
  const buildRenderer = renderer({
    optimizeDeps: {
      include: ['events']
    }
  })
  if (isBuild) {
    if (process.env.VITE_APP_ENV === 'live') {
      sourcemap = false
    }
    if (isBuildEle) {
      plugins.push(buildElePlugin)
    } else {
      plugins.push(buildReact)
      // const prefix = `${packageConf.name}/${process.env.VITE_APP_ENV}`
      // base = `https://static.newbrush.com/${prefix}`
      base = './'
      plugins.push(
        UploadPlugin({
          prefix,
          exclude: [],
          bucket: 'meta-show',
          alwaysUpload: [/.*\.html$/] // 覆盖上传后缀为html
        })
      )
    }
  } else {
    plugins.push(buildReact)
    plugins.push(buildElePlugin)
    plugins.push(buildRenderer)
  }
  return defineConfig({
    resolve: {
      alias: {
        '@': path.join(__dirname, 'src'),
        i18n: path.join(__dirname, 'src/locales'),
        events: 'events'
      }
    },
    plugins,
    base,
    build: {
      sourcemap
    },
    server: {
      port: 3009,
      host: true,
      proxy: {
        '^/metaleap-api': {
          target: 'https://test.aigc.metaleap.com',
          // target: 'https://aigc.metaleap.com',
          changeOrigin: true
          // rewrite: (path) => path.replace(/^\/metaleap-api/, '')
        }
      }
    }
  })
}
